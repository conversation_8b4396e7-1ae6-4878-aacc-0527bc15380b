# 作品集页面原理解释文档

## 页面概述

作品集页面（`pages/gallery/gallery`）是逍遥境无人机租赁平台的核心功能页面，专门用于管理和展示用户在无人机拍摄过程中创建的所有照片和视频作品。该页面不仅是用户查看个人创作成果的重要窗口，也是整个平台内容生态的核心组成部分。

### 页面功能特点
- **作品展示管理**：以网格形式展示用户的所有照片和视频作品
- **智能分类筛选**：支持按类型（照片/视频）、时间（最近7天）、订单等多维度筛选
- **实时统计分析**：动态计算并展示拍摄天数、存储空间、分享次数等关键指标
- **多媒体上传**：支持拍照、选择相册、录制视频等多种上传方式
- **作品详情查看**：提供全屏查看、下载、分享、编辑、删除等完整操作
- **订单关联筛选**：支持从订单页面跳转并筛选特定订单的作品
- **选择模式管理**：支持批量选择和操作作品

## 文件结构

### 核心文件组成
- **gallery.js**：487行，包含完整的作品集管理业务逻辑
- **gallery.wxml**：181行，作品展示界面和交互组件
- **gallery.wxss**：样式文件，实现网格布局和模态框设计
- **gallery.json**：页面配置，设置页面标题和样式

### 核心依赖模块
```javascript
const app = getApp()
const auth = require('../../utils/auth.js')
const galleryManager = require('../../utils/galleryManager.js')
```

**依赖说明**：
- **auth模块**：用户认证状态检查和权限验证
- **galleryManager模块**：作品集数据管理、统计计算、筛选操作的核心工具

## 数据结构详解

### 1. 作品数据管理
```javascript
// 作品数据
allWorks: [],              // 所有作品的完整列表
filteredWorks: [],         // 经过筛选后显示的作品列表
```

**数据说明**：
- **allWorks**：存储用户的所有作品，包括照片和视频
- **filteredWorks**：根据当前筛选条件显示的作品子集
- **如果**：用户切换筛选条件，那么filteredWorks会动态更新

### 2. 统计数据结构
```javascript
galleryStats: {
  totalWorks: 0,           // 总作品数量
  photoCount: 0,           // 照片数量
  videoCount: 0,           // 视频数量
  shootingDays: 0,         // 拍摄天数
  storageSize: '0MB',      // 存储空间大小
  shareCount: 0            // 分享次数
}
```

**统计数据说明**：
- **totalWorks**：用户作品的总数量，用于显示作品集规模
- **photoCount/videoCount**：分类统计，用于筛选栏显示
- **shootingDays**：根据作品创建时间计算的拍摄天数
- **storageSize**：所有作品占用的存储空间大小
- **shareCount**：作品被分享的总次数

### 3. 筛选状态管理
```javascript
// 筛选状态
currentCategory: 'all',    // 当前筛选类别
```

**筛选类别说明**：
- **'all'**：显示所有作品
- **'photo'**：只显示照片作品
- **'video'**：只显示视频作品
- **'recent'**：显示最近7天的作品
- **'order'**：显示特定订单的作品（从订单页面跳转时）

### 4. 页面状态管理
```javascript
// 页面状态
loading: true,             // 页面加载状态
selectMode: false,         // 是否处于选择模式
showModal: false,          // 是否显示作品详情模态框
currentMedia: {}           // 当前查看的媒体对象
```

**状态说明**：
- **loading**：控制加载动画的显示和隐藏
- **selectMode**：切换单选查看和批量选择模式
- **showModal**：控制作品详情模态框的显示
- **currentMedia**：存储当前正在查看的作品详细信息

## 页面生命周期详解

### 1. 页面加载（onLoad）
```javascript
onLoad(options) {
  console.log('作品集页面加载', options)

  // 保存订单ID用于筛选
  if (options.orderId) {
    this.setData({
      filterOrderId: options.orderId,
      currentCategory: 'order' // 设置为订单筛选模式
    })
  }

  this.checkAuth()
  this.loadGalleryData()
}
```

**详细执行逻辑**：

1. **参数处理**：
   - **如果**：从订单页面跳转并携带orderId参数，那么设置订单筛选模式
   - **筛选设置**：将currentCategory设置为'order'，启用订单筛选

2. **认证检查**：
   - **调用**：this.checkAuth()方法验证用户登录状态
   - **如果**：用户未登录，那么重定向到登录页面

3. **数据加载**：
   - **调用**：this.loadGalleryData()方法加载作品集数据
   - **如果**：有订单筛选，那么只加载该订单的作品

### 2. 页面显示（onShow）
```javascript
onShow() {
  this.checkAuth()
}
```

**详细执行逻辑**：

1. **重新认证**：
   - **如果**：用户从其他页面返回，那么重新检查登录状态
   - **安全保护**：确保用户仍有权限访问作品集

## 核心功能详解

### 1. 用户认证检查（第106-112行）
```javascript
checkAuth() {
  if (!auth.checkLoginStatus()) {
    auth.redirectToLogin('/pages/gallery/gallery')
    return false
  }
  return true
}
```

**详细执行逻辑**：

1. **登录状态验证**：
   - **如果**：用户未登录或token过期，那么跳转到登录页面
   - **返回地址**：将当前页面路径作为登录后的返回地址

2. **认证结果**：
   - **如果**：用户已登录，那么返回true继续执行
   - **如果**：用户未登录，那么返回false并中断后续操作

### 2. 作品集数据加载（第117-174行）
```javascript
async loadGalleryData() {
  if (!this.checkAuth()) return

  this.setData({ loading: true })

  try {
    // 获取用户作品集数据
    let works = galleryManager.getUserGallery()

    // 如果指定了订单ID，只显示该订单的作品
    if (this.data.filterOrderId) {
      works = galleryManager.getWorksByOrderId(this.data.filterOrderId)
      console.log(`筛选订单 ${this.data.filterOrderId} 的作品：`, works.length, '个')
    }

    // 如果没有作品，添加一些示例作品（仅用于演示）
    if (works.length === 0 && !this.data.filterOrderId) {
      works = [
        {
          id: 'demo_001',
          type: 'photo',
          location: '书圣故里',
          date: '12-01',
          createTime: '2024-12-01 14:30:00',
          title: '书圣故里 - 古典园林',
          device: 'DJI Air 3',
          resolution: '4000×3000',
          fileSize: '2.5MB',
          url: 'http://iph.href.lu/400x400',
          thumbnail: 'http://iph.href.lu/400x400',
          selected: false,
          orderId: 'demo_order'
        }
      ]
    }

    // 计算统计数据
    const stats = galleryManager.calculateGalleryStats(works)

    this.setData({
      allWorks: works,
      galleryStats: stats
    })

    // 应用筛选
    this.applyFilter()

    console.log('作品集数据加载完成：', works.length, '个作品')
  } catch (error) {
    console.error('加载作品集失败：', error)
    wx.showToast({
      title: '加载失败',
      icon: 'error'
    })
  } finally {
    this.setData({ loading: false })
  }
}
```

**详细执行逻辑**：

1. **前置认证检查**：
   - **如果**：认证失败，那么直接返回不执行数据加载
   - **安全保护**：确保只有已登录用户才能访问作品数据

2. **加载状态管理**：
   - **开始加载**：设置loading为true显示加载动画
   - **用户反馈**：让用户知道数据正在加载中

3. **作品数据获取**：
   - **全量获取**：从galleryManager获取用户的所有作品
   - **订单筛选**：如果有filterOrderId，那么只获取该订单的作品
   - **日志记录**：记录筛选后的作品数量

4. **示例数据处理**：
   - **如果**：用户没有作品且不是订单筛选模式，那么添加示例作品
   - **演示目的**：提供示例数据让用户了解页面功能

5. **统计数据计算**：
   - **调用**：galleryManager.calculateGalleryStats()计算统计信息
   - **动态统计**：根据实际作品数据计算各项指标

6. **数据更新**：
   - **作品列表**：更新allWorks和galleryStats
   - **筛选应用**：调用applyFilter()应用当前筛选条件

7. **错误处理**：
   - **如果**：数据加载失败，那么显示错误提示
   - **最终处理**：无论成功失败都隐藏loading状态

### 3. 分类筛选变更（第179-183行）
```javascript
onCategoryChange(e) {
  const category = e.currentTarget.dataset.category
  this.setData({ currentCategory: category })
  this.applyFilter()
}
```

**详细执行逻辑**：

1. **筛选类别获取**：
   - **数据源**：从点击事件的dataset中获取category值
   - **如果**：用户点击不同的筛选标签，那么获取对应的筛选类别

2. **状态更新**：
   - **设置**：更新currentCategory为新的筛选类别
   - **界面更新**：筛选栏会显示新的激活状态

3. **筛选应用**：
   - **调用**：this.applyFilter()重新筛选作品
   - **即时响应**：用户点击后立即看到筛选结果

### 4. 安全日期解析（第188-199行）
```javascript
parseDate(dateString) {
  // 如果已经是标准ISO格式，直接使用
  if (dateString.includes('T')) {
    return new Date(dateString)
  }
  // 如果是 "2024-11-30 09:15" 格式，转换为ISO格式
  if (dateString.includes(' ')) {
    return new Date(dateString.replace(' ', 'T') + ':00')
  }
  // 其他格式直接尝试解析
  return new Date(dateString)
}
```

**详细执行逻辑**：

1. **ISO格式检查**：
   - **如果**：日期字符串包含'T'，那么认为是标准ISO格式
   - **直接解析**：使用new Date()直接解析

2. **空格格式处理**：
   - **如果**：日期字符串包含空格，那么转换为ISO格式
   - **格式转换**：将空格替换为'T'并添加':00'秒数

3. **兜底处理**：
   - **如果**：其他格式，那么直接尝试解析
   - **容错机制**：确保各种日期格式都能正确处理

### 5. 筛选逻辑应用（第204-227行）
```javascript
applyFilter() {
  let filtered = [...this.data.allWorks]

  // 如果是订单筛选模式，不需要额外筛选（已经在loadGalleryData中筛选过了）
  if (this.data.currentCategory === 'order') {
    // 订单筛选模式，直接使用所有作品
  } else if (this.data.currentCategory !== 'all') {
    if (this.data.currentCategory === 'recent') {
      // 最近7天的作品
      const sevenDaysAgo = new Date()
      sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7)
      filtered = filtered.filter(work => this.parseDate(work.createTime) >= sevenDaysAgo)
    } else {
      // 按类型筛选
      filtered = filtered.filter(work => work.type === this.data.currentCategory)
    }
  }

  // 按创建时间排序（最新的在前）
  filtered.sort((a, b) => this.parseDate(b.createTime) - this.parseDate(a.createTime))

  this.setData({ filteredWorks: filtered })
  console.log('筛选后作品数量：', filtered.length)
}
```

**详细执行逻辑**：

1. **数据复制**：
   - **创建副本**：使用扩展运算符创建allWorks的副本
   - **避免修改**：确保不直接修改原始数据

2. **订单筛选处理**：
   - **如果**：当前是订单筛选模式，那么不需要额外筛选
   - **原因**：订单作品已在loadGalleryData中筛选完成

3. **类型筛选处理**：
   - **如果**：不是'all'类别，那么需要进行筛选
   - **最近7天筛选**：计算7天前的日期，筛选创建时间在此之后的作品
   - **类型筛选**：根据work.type字段筛选照片或视频

4. **排序处理**：
   - **时间排序**：按创建时间降序排列（最新的在前）
   - **日期解析**：使用parseDate方法确保日期比较的准确性

5. **结果更新**：
   - **设置数据**：更新filteredWorks为筛选后的结果
   - **日志记录**：记录筛选后的作品数量

### 6. 作品详情查看（第232-246行）
```javascript
viewWork(e) {
  const work = e.currentTarget.dataset.work

  wx.showModal({
    title: work.title,
    content: `地点：${work.location}\n创建时间：${work.createTime}\n浏览量：${work.views}\n点赞数：${work.likes}`,
    confirmText: '查看详情',
    success: (res) => {
      if (res.confirm) {
        console.log('查看作品详情：', work.title)
        // 这里可以跳转到作品详情页面
      }
    }
  })
}
```

**详细执行逻辑**：

1. **作品数据获取**：
   - **数据源**：从事件对象的dataset中获取work对象
   - **完整信息**：包含作品的所有详细信息

2. **信息展示**：
   - **模态框显示**：使用wx.showModal显示作品基本信息
   - **信息内容**：地点、创建时间、浏览量、点赞数等关键信息

3. **用户交互**：
   - **如果**：用户点击"查看详情"，那么可以跳转到详情页面
   - **扩展功能**：为将来的详情页面跳转预留接口

### 7. 作品上传功能（第251-333行）

#### 上传选择
```javascript
uploadWork() {
  wx.showActionSheet({
    itemList: ['拍照上传', '从相册选择', '录制视频'],
    success: (res) => {
      switch (res.tapIndex) {
        case 0:
          this.takePhoto()
          break
        case 1:
          this.chooseImage()
          break
        case 2:
          this.recordVideo()
          break
      }
    }
  })
}
```

**详细执行逻辑**：

1. **选择方式展示**：
   - **操作表单**：显示三种上传方式的选择
   - **用户选择**：根据用户需求选择不同的上传方式

2. **方式分发**：
   - **如果**：用户选择拍照，那么调用takePhoto()方法
   - **如果**：用户选择相册，那么调用chooseImage()方法
   - **如果**：用户选择录制，那么调用recordVideo()方法

#### 拍照上传
```javascript
takePhoto() {
  wx.chooseMedia({
    count: 1,
    mediaType: ['image'],
    sourceType: ['camera'],
    success: (res) => {
      console.log('拍照成功：', res.tempFiles)
      this.uploadMedia(res.tempFiles[0])
    }
  })
}
```

#### 选择图片
```javascript
chooseImage() {
  wx.chooseMedia({
    count: 9,
    mediaType: ['image'],
    sourceType: ['album'],
    success: (res) => {
      console.log('选择图片成功：', res.tempFiles)
      // 可以批量上传
      res.tempFiles.forEach(file => this.uploadMedia(file))
    }
  })
}
```

#### 录制视频
```javascript
recordVideo() {
  wx.chooseMedia({
    count: 1,
    mediaType: ['video'],
    sourceType: ['camera'],
    success: (res) => {
      console.log('录制视频成功：', res.tempFiles)
      this.uploadMedia(res.tempFiles[0])
    }
  })
}
```

**上传方式说明**：

1. **拍照上传**：
   - **限制**：单张照片，仅使用相机
   - **如果**：拍照成功，那么立即上传该照片

2. **相册选择**：
   - **批量支持**：最多选择9张图片
   - **来源**：仅从相册选择
   - **如果**：选择成功，那么批量上传所有选中的图片

3. **视频录制**：
   - **限制**：单个视频，仅使用相机录制
   - **如果**：录制成功，那么立即上传该视频

#### 媒体上传处理
```javascript
uploadMedia(file) {
  wx.showLoading({ title: '上传中...' })

  // 模拟上传过程
  setTimeout(() => {
    wx.hideLoading()
    wx.showToast({
      title: '上传成功',
      icon: 'success'
    })

    // 刷新作品列表
    this.loadGalleryData()
  }, 1500)
}
```

**详细执行逻辑**：

1. **上传提示**：
   - **加载动画**：显示"上传中..."的loading提示
   - **用户反馈**：让用户知道上传正在进行

2. **模拟上传**：
   - **延时处理**：使用setTimeout模拟上传过程
   - **真实环境**：这里会调用实际的文件上传API

3. **成功处理**：
   - **隐藏loading**：上传完成后隐藏加载提示
   - **成功提示**：显示"上传成功"的提示信息
   - **数据刷新**：重新加载作品集数据以显示新上传的作品

### 8. 页面导航功能（第338-349行）

#### 设备页面跳转
```javascript
goToEquipment() {
  wx.switchTab({
    url: '/pages/equipment/equipment'
  })
}
```

#### 返回上一页
```javascript
goBack() {
  wx.navigateBack()
}
```

**导航说明**：

1. **设备页面跳转**：
   - **如果**：用户没有作品需要租赁设备，那么跳转到设备页面
   - **使用switchTab**：因为设备页面是tabBar页面

2. **返回功能**：
   - **如果**：用户需要返回上一页，那么使用navigateBack
   - **适用场景**：从订单页面跳转过来时的返回操作

### 9. 选择模式管理（第354-367行）
```javascript
toggleSelectMode() {
  const selectMode = !this.data.selectMode
  this.setData({ selectMode })

  if (!selectMode) {
    // 退出选择模式时清除所有选择
    const allWorks = this.data.allWorks.map(work => ({
      ...work,
      selected: false
    }))
    this.setData({ allWorks })
    this.filterWorks()
  }
}
```

**详细执行逻辑**：

1. **模式切换**：
   - **状态反转**：切换selectMode的布尔值
   - **界面更新**：选择模式会改变作品项的显示样式

2. **退出处理**：
   - **如果**：退出选择模式，那么清除所有作品的选中状态
   - **数据重置**：将所有作品的selected属性设为false
   - **筛选更新**：重新应用筛选以更新界面显示

### 10. 媒体点击处理（第372-388行）
```javascript
handleMediaTap(e) {
  const { work, index } = e.currentTarget.dataset

  if (this.data.selectMode) {
    // 选择模式下切换选中状态
    const allWorks = [...this.data.allWorks]
    const workIndex = allWorks.findIndex(w => w.id === work.id)
    if (workIndex !== -1) {
      allWorks[workIndex].selected = !allWorks[workIndex].selected
      this.setData({ allWorks })
      this.filterWorks()
    }
  } else {
    // 普通模式下查看媒体
    this.viewMedia(work)
  }
}
```

**详细执行逻辑**：

1. **数据获取**：
   - **作品信息**：从dataset中获取work对象和index
   - **完整数据**：包含作品的所有信息

2. **选择模式处理**：
   - **如果**：当前处于选择模式，那么切换作品的选中状态
   - **查找作品**：在allWorks中找到对应的作品
   - **状态切换**：反转selected属性的值
   - **界面更新**：重新筛选以更新选择状态显示

3. **普通模式处理**：
   - **如果**：当前是普通模式，那么查看作品详情
   - **调用**：viewMedia方法显示作品详情模态框

### 11. 媒体查看功能（第393-398行）
```javascript
viewMedia(media) {
  this.setData({
    currentMedia: media,
    showModal: true
  })
}
```

**详细执行逻辑**：

1. **媒体设置**：
   - **当前媒体**：设置currentMedia为要查看的媒体对象
   - **模态框显示**：设置showModal为true显示详情模态框

2. **界面响应**：
   - **如果**：设置成功，那么模态框会显示该媒体的详细信息
   - **全屏查看**：用户可以全屏查看照片或播放视频

### 12. 模态框关闭（第403-405行）
```javascript
closeModal() {
  this.setData({ showModal: false })
}
```

**详细执行逻辑**：
- **如果**：用户需要关闭模态框，那么设置showModal为false
- **界面隐藏**：模态框会淡出并隐藏

### 13. 媒体操作功能（第417-475行）

#### 下载媒体
```javascript
downloadMedia() {
  wx.showToast({
    title: '下载成功！作品已保存到手机相册',
    icon: 'success',
    duration: 2000
  })
}
```

#### 分享媒体
```javascript
shareMedia() {
  wx.showActionSheet({
    itemList: ['微信好友', '朋友圈', 'QQ空间', '新浪微博', '复制链接'],
    success: (res) => {
      const options = ['微信好友', '朋友圈', 'QQ空间', '新浪微博', '复制链接']
      wx.showToast({
        title: `分享到${options[res.tapIndex]}`,
        icon: 'success'
      })
    }
  })
}
```

#### 编辑媒体
```javascript
editMedia() {
  wx.showActionSheet({
    itemList: ['裁剪', '滤镜', '调色', '添加水印'],
    success: (res) => {
      const options = ['裁剪', '滤镜', '调色', '添加水印']
      wx.showToast({
        title: `${options[res.tapIndex]}功能`,
        icon: 'none'
      })
    }
  })
}
```

#### 删除媒体
```javascript
deleteMedia() {
  wx.showModal({
    title: '删除作品',
    content: '确认删除这个作品？删除后不可恢复',
    success: (res) => {
      if (res.confirm) {
        wx.showToast({
          title: '作品已删除',
          icon: 'success'
        })
        this.closeModal()
        // TODO: 实际删除逻辑
      }
    }
  })
}
```

**媒体操作说明**：

1. **下载功能**：
   - **如果**：用户需要保存作品，那么模拟下载到相册
   - **用户反馈**：显示下载成功的提示信息

2. **分享功能**：
   - **平台选择**：提供多个社交平台的分享选项
   - **如果**：用户选择分享平台，那么显示分享成功提示

3. **编辑功能**：
   - **编辑选项**：提供裁剪、滤镜、调色、水印等编辑功能
   - **如果**：用户选择编辑功能，那么显示功能提示

4. **删除功能**：
   - **确认机制**：显示确认对话框防止误删
   - **如果**：用户确认删除，那么执行删除操作并关闭模态框

## WXML结构详解

### 1. 页面整体容器（第2行）
```xml
<view class="gallery-container">
  <!-- 作品集页面内容 -->
</view>
```

**结构说明**：
- **gallery-container**：作品集页面的根容器
- **如果**：需要统一的页面布局，那么使用此容器管理所有内容区域

### 2. 顶部导航区域（第4-11行）
```xml
<view class="header">
  <view class="header-content">
    <view class="header-info">
      <text class="header-title">我的作品集</text>
      <text class="header-subtitle">共{{galleryStats.totalWorks}}张作品</text>
    </view>
  </view>
</view>
```

**详细说明**：

1. **标题显示**：
   - **页面标题**：显示"我的作品集"
   - **作品统计**：动态显示总作品数量

2. **数据绑定**：
   - **如果**：galleryStats.totalWorks更新，那么副标题会实时显示新的数量

### 3. 筛选栏区域（第13-47行）
```xml
<view class="filter-bar">
  <scroll-view class="filter-scroll" scroll-x="true">
    <view class="filter-list">
      <view
        class="filter-item {{currentCategory === 'all' ? 'active' : ''}}"
        bindtap="onCategoryChange"
        data-category="all"
      >
        全部 ({{galleryStats.totalWorks}})
      </view>
      <view
        class="filter-item {{currentCategory === 'photo' ? 'active' : ''}}"
        bindtap="onCategoryChange"
        data-category="photo"
      >
        照片 ({{galleryStats.photoCount}})
      </view>
      <!-- 其他筛选项 -->
    </view>
  </scroll-view>
</view>
```

**详细说明**：

1. **横向滚动**：
   - **scroll-view**：支持横向滚动的筛选标签
   - **如果**：筛选项过多，那么可以横向滚动查看

2. **筛选项设计**：
   - **动态样式**：根据currentCategory动态添加active样式
   - **数量显示**：每个筛选项显示对应的作品数量
   - **点击事件**：bindtap="onCategoryChange"处理筛选切换

3. **筛选类别**：
   - **全部**：显示所有作品及总数量
   - **照片**：显示照片作品及照片数量
   - **视频**：显示视频作品及视频数量
   - **最近7天**：显示最近一周的作品

### 4. 统计信息区域（第49-65行）
```xml
<view class="stats-section">
  <view class="stats-grid">
    <view class="stats-item">
      <text class="stats-number">{{galleryStats.shootingDays}}</text>
      <text class="stats-label">拍摄天数</text>
    </view>
    <view class="stats-item">
      <text class="stats-number">{{galleryStats.storageSize}}</text>
      <text class="stats-label">存储空间</text>
    </view>
    <view class="stats-item">
      <text class="stats-number">{{galleryStats.shareCount}}</text>
      <text class="stats-label">分享次数</text>
    </view>
  </view>
</view>
```

**详细说明**：

1. **网格布局**：
   - **stats-grid**：三列网格布局展示统计信息
   - **统一样式**：每个统计项使用相同的样式结构

2. **统计项目**：
   - **拍摄天数**：显示用户的拍摄活跃天数
   - **存储空间**：显示作品占用的存储空间大小
   - **分享次数**：显示作品被分享的总次数

3. **数据绑定**：
   - **如果**：统计数据更新，那么界面会实时显示新的数值

### 5. 作品网格区域（第67-114行）
```xml
<view class="media-grid" wx:if="{{filteredWorks.length > 0}}">
  <view
    class="media-item {{selectMode ? 'select-mode' : ''}}"
    wx:for="{{filteredWorks}}"
    wx:key="id"
    bindtap="handleMediaTap"
    data-work="{{item}}"
    data-index="{{index}}"
  >
    <!-- 选择模式复选框 -->
    <view class="select-checkbox" wx:if="{{selectMode}}">
      <text class="checkbox-icon">{{item.selected ? '✓' : ''}}</text>
    </view>

    <!-- 媒体内容 -->
    <image
      class="media-image"
      src="{{item.thumbnail || item.url}}"
      mode="aspectFill"
      lazy-load="true"
    />

    <!-- 视频播放图标 -->
    <view class="video-overlay" wx:if="{{item.type === 'video'}}">
      <text class="play-icon">▶</text>
    </view>

    <!-- 媒体信息 -->
    <view class="media-info">
      <view class="info-content">
        <text class="media-location">
          <text class="location-icon">{{item.type === 'video' ? '🎥' : '📷'}}</text>
          {{item.location}}
        </text>
        <text class="media-date">{{item.date}}</text>
      </view>
    </view>
  </view>

  <!-- 加载更多 -->
  <view class="load-more-item" bindtap="loadMore">
    <view class="load-more-content">
      <text class="load-more-icon">+</text>
      <text class="load-more-text">加载更多</text>
    </view>
  </view>
</view>
```

**详细说明**：

1. **条件显示**：
   - **如果**：有筛选后的作品，那么显示作品网格
   - **动态列表**：使用wx:for遍历filteredWorks数组

2. **作品项结构**：
   - **选择模式**：根据selectMode显示复选框
   - **媒体图片**：显示作品的缩略图或原图
   - **视频标识**：如果是视频，那么显示播放图标
   - **作品信息**：显示拍摄地点和日期

3. **交互设计**：
   - **点击事件**：bindtap="handleMediaTap"处理作品点击
   - **数据传递**：通过data-work和data-index传递作品信息
   - **懒加载**：图片使用lazy-load优化性能

4. **加载更多**：
   - **扩展功能**：提供加载更多作品的入口
   - **如果**：用户需要查看更多作品，那么点击加载更多

### 6. 空状态显示（第116-122行）
```xml
<view class="empty-state" wx:if="{{filteredWorks.length === 0 && !loading}}">
  <text class="empty-icon">📸</text>
  <text class="empty-title">暂无作品</text>
  <text class="empty-desc">开始你的第一次飞行，记录美好瞬间</text>
  <button class="btn btn-primary" bindtap="goToEquipment">去租赁设备</button>
</view>
```

**详细说明**：

1. **显示条件**：
   - **如果**：没有筛选后的作品且不在加载中，那么显示空状态
   - **用户引导**：提示用户如何开始创建作品

2. **引导操作**：
   - **租赁按钮**：提供"去租赁设备"的操作入口
   - **如果**：用户点击按钮，那么跳转到设备页面

### 7. 加载状态显示（第124-128行）
```xml
<view class="loading-state" wx:if="{{loading}}">
  <text class="loading-icon">⏳</text>
  <text class="loading-text">加载中...</text>
</view>
```

**详细说明**：
- **如果**：正在加载数据，那么显示加载状态
- **用户反馈**：让用户知道数据正在加载中

### 8. 媒体查看模态框（第131-181行）
```xml
<view class="media-modal {{showModal ? 'show' : ''}}" bindtap="closeModal">
  <view class="modal-content" catchtap="stopPropagation">
    <view class="media-container">
      <image
        class="modal-image"
        src="{{currentMedia.url}}"
        mode="aspectFit"
        wx:if="{{currentMedia.type === 'photo'}}"
      />

      <video
        class="modal-video"
        src="{{currentMedia.url}}"
        wx:if="{{currentMedia.type === 'video'}}"
      />

      <button class="modal-close" bindtap="closeModal">
        <text class="close-icon">×</text>
      </button>
    </view>

    <!-- 底部操作栏 -->
    <view class="modal-actions">
      <button class="action-btn" bindtap="downloadMedia">
        <text class="action-icon">⬇</text>
      </button>
      <button class="action-btn" bindtap="shareMedia">
        <text class="action-icon">↗</text>
      </button>
      <button class="action-btn" bindtap="editMedia">
        <text class="action-icon">✎</text>
      </button>
      <button class="action-btn" bindtap="deleteMedia">
        <text class="action-icon">🗑</text>
      </button>
    </view>

    <!-- 媒体信息 -->
    <view class="modal-info">
      <text class="modal-title">{{currentMedia.title}}</text>
      <view class="modal-details">
        <text class="detail-item">拍摄时间: {{currentMedia.createTime}}</text>
        <text class="detail-item">设备: {{currentMedia.device}}</text>
        <text class="detail-item" wx:if="{{currentMedia.type === 'photo'}}">分辨率: {{currentMedia.resolution}}</text>
        <text class="detail-item" wx:if="{{currentMedia.type === 'video'}}">时长: {{currentMedia.duration}}</text>
        <text class="detail-item">文件大小: {{currentMedia.fileSize}}</text>
      </view>
    </view>
  </view>
</view>
```

**详细说明**：

1. **模态框控制**：
   - **显示状态**：根据showModal动态添加show样式
   - **背景点击**：点击背景关闭模态框
   - **内容保护**：使用catchtap阻止内容区域的事件冒泡

2. **媒体显示**：
   - **照片显示**：如果是照片，那么使用image组件
   - **视频显示**：如果是视频，那么使用video组件
   - **关闭按钮**：提供明确的关闭操作

3. **操作按钮**：
   - **下载**：⬇图标，下载作品到本地
   - **分享**：↗图标，分享作品到社交平台
   - **编辑**：✎图标，编辑作品内容
   - **删除**：🗑图标，删除作品

4. **详细信息**：
   - **基本信息**：显示作品标题和拍摄时间
   - **设备信息**：显示拍摄使用的设备型号
   - **技术参数**：根据类型显示分辨率或时长
   - **文件信息**：显示文件大小

## 业务流程分析

### 1. 作品集加载流程
1. **如果**：用户进入作品集页面，那么首先检查登录状态
2. **如果**：用户已登录，那么加载用户的所有作品数据
3. **如果**：从订单页面跳转，那么只加载该订单的作品
4. **如果**：没有作品数据，那么显示示例作品或空状态
5. **如果**：数据加载完成，那么计算统计信息并应用筛选

### 2. 筛选操作流程
1. **如果**：用户点击筛选标签，那么更新当前筛选类别
2. **如果**：选择"全部"，那么显示所有作品
3. **如果**：选择"照片"或"视频"，那么按类型筛选
4. **如果**：选择"最近7天"，那么按时间筛选
5. **如果**：筛选完成，那么按时间排序并更新显示

### 3. 作品上传流程
1. **如果**：用户需要上传作品，那么显示上传方式选择
2. **如果**：选择拍照，那么调用相机拍照
3. **如果**：选择相册，那么可以批量选择图片
4. **如果**：选择录制，那么调用相机录制视频
5. **如果**：选择完成，那么模拟上传过程并刷新列表

### 4. 作品查看流程
1. **如果**：用户点击作品，那么检查当前模式
2. **如果**：是普通模式，那么显示作品详情模态框
3. **如果**：是选择模式，那么切换作品的选中状态
4. **如果**：查看详情，那么显示全屏图片或视频
5. **如果**：需要操作，那么提供下载、分享、编辑、删除功能

### 5. 选择模式流程
1. **如果**：用户切换到选择模式，那么显示复选框
2. **如果**：点击作品，那么切换选中状态
3. **如果**：退出选择模式，那么清除所有选择状态
4. **如果**：需要批量操作，那么对选中的作品执行操作

### 6. 数据同步流程
1. **如果**：用户上传新作品，那么刷新作品列表
2. **如果**：用户删除作品，那么从列表中移除
3. **如果**：统计数据变化，那么重新计算并更新显示
4. **如果**：筛选条件变化，那么重新应用筛选逻辑

## 总结

作品集页面作为用户创作成果的展示和管理中心，实现了以下关键功能：

1. **完整的作品展示系统**：如果用户需要查看作品，那么提供网格布局、筛选分类、统计信息等全面的展示功能
2. **灵活的筛选机制**：如果用户需要查找特定作品，那么提供类型、时间、订单等多维度筛选
3. **便捷的上传功能**：如果用户需要添加作品，那么支持拍照、相册选择、视频录制等多种上传方式
4. **丰富的作品操作**：如果用户需要管理作品，那么提供查看、下载、分享、编辑、删除等完整操作
5. **智能的模式切换**：如果用户需要批量操作，那么提供选择模式和普通查看模式的切换
6. **实时的数据统计**：如果用户需要了解创作情况，那么提供拍摄天数、存储空间、分享次数等统计信息

整个页面的设计严格遵循了"如果...那么..."的条件逻辑，确保在各种用户操作、数据状态和业务场景下都能提供合适的响应和体验，同时保证了作品数据的安全性和操作的便捷性。
