/* pages/location/location.wxss */

.location-container {
  background-color: #f9fafb;
  min-height: 100vh;
  padding-bottom: 32rpx;
}

/* 搜索栏 */
.search-section {
  background: #ffffff;
  padding: 32rpx;
  box-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.05);
}

.search-bar {
  background: #f3f4f6;
  border-radius: 48rpx;
  padding: 24rpx 32rpx;
  display: flex;
  align-items: center;
  gap: 16rpx;
}

.search-icon {
  font-size: 32rpx;
  color: #9ca3af;
}

.search-input {
  flex: 1;
  font-size: 32rpx;
  color: #1f2937;
  background: transparent;
  border: none;
}

.search-input::placeholder {
  color: #9ca3af;
}

/* 当前定位 */
.current-location {
  background: #ffffff;
  padding: 32rpx;
  margin-bottom: 16rpx;
  display: flex;
  align-items: center;
  gap: 16rpx;
  cursor: pointer;
  transition: all 0.2s ease;
}

.current-location:hover {
  background: #f9fafb;
}

.location-icon {
  font-size: 32rpx;
  color: #10a37f;
}

.location-text {
  flex: 1;
  font-size: 32rpx;
  color: #1f2937;
  font-weight: 500;
}

.location-action {
  font-size: 28rpx;
  color: #10a37f;
  font-weight: 500;
}

/* 地点列表 */
.locations-list {
  padding: 0 32rpx;
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.location-card {
  background: #ffffff;
  border-radius: 24rpx;
  padding: 32rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-shadow: 0 8rpx 16rpx rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  cursor: pointer;
}

.location-card:hover {
  transform: translateY(-4rpx);
  box-shadow: 0 16rpx 32rpx rgba(0, 0, 0, 0.15);
}

.location-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.location-name {
  font-size: 36rpx;
  font-weight: 600;
  color: #1f2937;
}

.location-address {
  font-size: 28rpx;
  color: #6b7280;
  line-height: 1.4;
}

.location-meta {
  display: flex;
  align-items: center;
  gap: 24rpx;
  margin-top: 8rpx;
}

.distance {
  font-size: 24rpx;
  color: #9ca3af;
}

.rating {
  font-size: 24rpx;
  color: #f59e0b;
}

.price {
  font-size: 28rpx;
  color: #10a37f;
  font-weight: 600;
}

.location-status {
  flex-shrink: 0;
  margin-left: 24rpx;
}

.status-text {
  border-radius: 20rpx;
  font-size: 24rpx;
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 80rpx;
  height: 56rpx;
  padding: 0;
  border: none;
  overflow: hidden;
  white-space: nowrap;
}

.status-text.available {
  background: rgba(16, 185, 129, 0.1);
  color: #10b981;
}

.status-text.unavailable {
  background: rgba(239, 68, 68, 0.1);
  color: #ef4444;
}

/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 120rpx 64rpx;
  text-align: center;
}

.empty-icon {
  font-size: 120rpx;
  margin-bottom: 32rpx;
  opacity: 0.6;
}

.empty-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 16rpx;
}

.empty-desc {
  font-size: 28rpx;
  color: #6b7280;
  line-height: 1.5;
}