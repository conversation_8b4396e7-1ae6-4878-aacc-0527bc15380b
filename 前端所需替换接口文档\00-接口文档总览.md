# 逍遥境无人机租赁平台 - 前端接口文档总览

## 项目概述
逍遥境是一个基于微信小程序的无人机租赁平台，用户可以租赁无人机设备、选择拍摄地点、进行飞行控制和作品管理。目前前端已完成全部功能开发，使用模拟数据运行，需要替换为真实后端接口。

## 技术架构
- **前端框架**: 微信小程序原生开发
- **数据管理**: 本地存储 + 模拟数据
- **网络请求**: 统一请求封装（`utils/request.js`）
- **认证方式**: Token认证，7天有效期
- **降级方案**: 接口失败时自动降级到本地模拟数据

## 接口文档列表

### 1. 登录页面接口文档
**文件**: `01-登录页面接口文档.md`
**页面**: `pages/login/login.js`
**核心接口**:
- 微信登录授权接口
- 手机号授权接口  
- 用户信息获取接口
- Token验证接口

### 2. 首页接口文档
**文件**: `02-首页接口文档.md`
**页面**: `pages/index/index.js`
**核心接口**:
- 用户信息获取接口
- 热门设备推荐接口
- 热门地点推荐接口
- 最近订单接口
- 用户统计数据接口
- 搜索建议接口

### 3. 设备页面接口文档
**文件**: `03-设备页面接口文档.md`
**页面**: `pages/equipment/equipment.js`, `pages/equipment-detail/equipment-detail.js`
**核心接口**:
- 设备列表接口
- 设备详情接口
- 设备收藏接口
- 设备可用性查询接口
- 设备评价接口

### 4. 地点页面接口文档
**文件**: `04-地点页面接口文档.md`
**页面**: `pages/location/location.js`
**核心接口**:
- 地点列表接口
- 地点搜索接口
- 地点详情接口
- 地点选择接口

### 5. 订单确认页面接口文档
**文件**: `05-订单确认页面接口文档.md`
**页面**: `pages/order-confirm/order-confirm.js`
**核心接口**:
- 时间段可用性查询接口
- 费用计算接口
- 优惠券查询接口
- 订单创建接口

### 6. 订单管理页面接口文档
**文件**: `06-订单管理页面接口文档.md`
**页面**: `pages/orders/orders.js`
**核心接口**:
- 订单列表接口
- 订单详情接口
- 订单取消接口
- 订单提前结束接口
- 订单评价接口

### 7. 用户中心页面接口文档
**文件**: `07-用户中心页面接口文档.md`
**页面**: `pages/profile/profile.js`
**核心接口**:
- 用户统计数据接口
- 收藏列表接口
- 优惠券列表接口
- 交易记录接口
- 用户注销接口

### 8. 无人机控制页面接口文档
**文件**: `08-无人机控制页面接口文档.md`
**页面**: `pages/drone-control/drone-control.js`
**核心接口**:
- 设备连接接口
- 实时飞行数据接口
- 飞行控制指令接口
- 拍摄控制接口
- 作品保存接口
- 设备断开接口

### 9. 作品集页面接口文档
**文件**: `09-作品集页面接口文档.md`
**页面**: `pages/gallery/gallery.js`
**核心接口**:
- 作品列表接口
- 作品统计接口
- 作品删除接口
- 作品分享接口
- 作品详情接口
- 批量操作接口

### 10. 充值页面接口文档
**文件**: `10-充值页面接口文档.md`
**页面**: `pages/recharge/recharge.js`
**核心接口**:
- 充值配置接口
- 优惠计算接口
- 充值订单创建接口
- 支付验证接口
- 充值记录接口

## 接口统计汇总

### 按功能模块分类
| 模块 | 接口数量 | 核心功能 |
|------|----------|----------|
| 用户认证 | 4 | 登录、授权、Token验证 |
| 首页展示 | 6 | 推荐内容、用户数据、搜索 |
| 设备管理 | 5 | 设备列表、详情、收藏、评价 |
| 地点管理 | 4 | 地点列表、搜索、选择 |
| 订单处理 | 8 | 订单创建、管理、支付、评价 |
| 用户中心 | 6 | 个人信息、收藏、优惠券、交易 |
| 设备控制 | 7 | 连接、控制、拍摄、数据传输 |
| 作品管理 | 6 | 作品展示、管理、分享、统计 |
| 支付充值 | 6 | 充值配置、支付、验证、记录 |
| **总计** | **52** | **完整业务流程覆盖** |

### 按请求方法分类
| 方法 | 数量 | 占比 | 主要用途 |
|------|------|------|----------|
| GET | 28 | 53.8% | 数据查询、列表获取 |
| POST | 18 | 34.6% | 数据创建、操作执行 |
| PUT | 4 | 7.7% | 数据更新、状态修改 |
| DELETE | 2 | 3.9% | 数据删除 |

### 按优先级分类
| 优先级 | 接口数量 | 说明 |
|--------|----------|------|
| 高优先级 | 20 | 核心业务流程，必须优先实现 |
| 中优先级 | 22 | 重要功能，影响用户体验 |
| 低优先级 | 10 | 辅助功能，可后期实现 |

## 核心业务流程接口

### 1. 用户注册登录流程
```
微信登录授权 → 手机号授权 → 用户信息获取 → Token验证
```

### 2. 设备租赁流程
```
设备列表 → 设备详情 → 地点选择 → 时间选择 → 费用计算 → 订单创建 → 支付处理
```

### 3. 设备控制流程
```
设备连接 → 实时数据获取 → 飞行控制 → 拍摄操作 → 作品保存 → 设备断开
```

### 4. 作品管理流程
```
作品列表 → 作品详情 → 作品操作（分享/删除） → 统计更新
```

## 技术要求

### 1. 认证机制
- 所有接口需要Token认证
- Token有效期7天，需要刷新机制
- 支持微信小程序授权登录

### 2. 数据格式
- 统一JSON格式响应
- 标准化错误码和消息
- 分页数据统一格式

### 3. 性能要求
- 列表接口支持分页
- 图片资源支持CDN
- 实时数据接口低延迟

### 4. 安全要求
- 支付接口严格验证
- 用户数据权限控制
- 设备控制安全机制

## 降级方案

每个页面都实现了完整的降级方案：
1. **接口失败时自动降级到本地模拟数据**
2. **保证基本功能可用性**
3. **用户无感知的错误处理**
4. **网络恢复后自动切换回真实接口**

## 开发建议

### 1. 实现顺序
1. **第一阶段**: 用户认证、设备管理、订单处理（核心流程）
2. **第二阶段**: 设备控制、作品管理（核心功能）
3. **第三阶段**: 支付充值、用户中心（完善功能）

### 2. 测试策略
- 每个接口都有详细的测试建议
- 重点测试核心业务流程
- 验证降级方案的有效性

### 3. 部署建议
- 分阶段部署，逐步替换模拟数据
- 保留降级方案作为容灾机制
- 监控接口性能和错误率

## 联系信息
如有疑问，请参考各个具体页面的接口文档，每个文档都包含详细的实现指导和代码示例。
