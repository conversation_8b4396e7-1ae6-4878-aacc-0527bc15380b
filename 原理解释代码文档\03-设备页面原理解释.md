# 设备页面原理解释文档

## 页面概述
设备页面是用户选择和租用无人机设备的核心页面，作为tabBar页面之一，提供设备浏览、筛选、排序和详情查看功能。用户可以通过此页面查看所有可用设备，根据类型筛选设备，并进入具体设备的详情页面进行租用。

## 文件结构
```
pages/equipment/
├── equipment.js      # 页面逻辑文件（258行）
├── equipment.wxml    # 页面结构文件（179行）
├── equipment.wxss    # 页面样式文件
└── equipment.json    # 页面配置文件
```

## 核心依赖模块
- `utils/auth.js` - 认证工具模块，提供登录状态检查和用户权限验证
- `utils/mockData.js` - 模拟数据模块，提供设备数据（mockEquipment）
- `app.js` - 全局应用实例

## 页面数据结构详解

### data 对象分析（第11-23行）
```javascript
data: {
  // 设备数据
  allEquipment: [],
  filteredEquipment: [],
  
  // 筛选和排序
  currentFilter: 'all',
  currentSort: 'rating',
  showSort: false,
  
  // 页面状态
  loading: true
}
```

**数据字段详细说明**：

### 1. 设备数据相关

#### allEquipment（全部设备数据）
- **作用**：存储从数据源加载的所有设备信息
- **默认值**：空数组[]
- **数据来源**：如果页面加载，那么从mockEquipment获取所有设备数据
- **数据结构**：如果有设备数据，那么每个元素包含id、name、price、category、rating、available等字段
- **更新时机**：如果页面初始化或下拉刷新，那么重新加载所有设备数据

#### filteredEquipment（筛选后的设备数据）
- **作用**：存储经过筛选和排序后的设备数据，用于页面显示
- **默认值**：空数组[]
- **数据来源**：如果用户选择筛选条件，那么从allEquipment中筛选符合条件的设备
- **显示逻辑**：如果filteredEquipment有数据，那么显示设备列表；如果为空，那么显示空状态提示
- **更新时机**：如果筛选条件或排序方式改变，那么重新计算filteredEquipment

### 2. 筛选和排序相关

#### currentFilter（当前筛选条件）
- **作用**：记录用户当前选择的设备类型筛选条件
- **默认值**：'all'（显示所有设备）
- **可选值**：
  - **'all'**：如果用户选择全部，那么显示所有可用设备
  - **'professional'**：如果用户选择专业级，那么只显示专业级设备
  - **'entry'**：如果用户选择入门级，那么只显示入门级设备
  - **'portable'**：如果用户选择便携式，那么只显示便携式设备
- **UI反馈**：如果某个筛选条件被选中，那么对应的筛选按钮显示激活状态

#### currentSort（当前排序方式）
- **作用**：记录用户当前选择的设备排序方式
- **默认值**：'rating'（按评分排序）
- **可选值**：
  - **'rating'**：如果用户选择按评分排序，那么评分高的设备排在前面
  - **'price_low'**：如果用户选择价格从低到高，那么价格低的设备排在前面
  - **'price_high'**：如果用户选择价格从高到低，那么价格高的设备排在前面
  - **'name'**：如果用户选择按名称排序，那么按设备名称字母顺序排列

#### showSort（排序选项显示状态）
- **作用**：控制排序选项弹窗的显示和隐藏
- **默认值**：false（不显示排序选项）
- **显示条件**：如果用户点击排序图标，那么设为true显示排序选项
- **隐藏条件**：如果用户选择排序方式或点击取消，那么设为false隐藏排序选项

### 3. 页面状态相关

#### loading（页面加载状态）
- **作用**：控制页面数据加载时的loading状态显示
- **默认值**：true（页面初始化时显示loading）
- **设为false的时机**：如果设备数据加载完成（成功或失败），那么隐藏loading状态
- **用户体验**：如果数据正在加载，那么显示loading动画，避免用户看到空白页面

## 页面生命周期详解

### 1. onLoad 生命周期（第28-32行）
```javascript
onLoad(options) {
  console.log('设备列表页面加载')
  this.checkAuth()
  this.loadEquipment()
}
```

**详细执行逻辑**：

1. **日志记录**：
   - **目的**：记录页面加载事件，便于开发调试
   - **如果**：在开发环境，那么可以通过控制台查看页面加载情况

2. **认证状态检查**：
   - **调用**：this.checkAuth()方法
   - **如果**：用户未登录，那么跳转到登录页面
   - **如果**：用户已登录，那么继续执行后续步骤
   - **重要性**：设备页面需要登录才能访问，确保用户身份验证

3. **设备数据加载**：
   - **调用**：this.loadEquipment()方法
   - **如果**：认证通过，那么开始加载设备数据
   - **数据处理**：加载、筛选、排序设备数据并更新页面显示

### 2. onShow 生命周期（第44-47行）
```javascript
onShow() {
  // 检查登录状态
  this.checkAuth()
}
```

**详细执行逻辑**：

1. **触发时机**：
   - **如果**：用户首次进入设备页面，那么在onLoad之后执行
   - **如果**：用户从其他页面切换回设备页面（tabBar切换），那么重新执行
   - **如果**：用户从设备详情页返回设备列表页，那么重新执行

2. **认证状态重新检查**：
   - **调用**：this.checkAuth()方法
   - **目的**：确保用户在页面显示时仍然处于登录状态
   - **如果**：用户在其他页面退出登录，那么返回设备页面时会被重定向到登录页

### 3. onPullDownRefresh 下拉刷新（第66-68行）
```javascript
onPullDownRefresh() {
  this.loadEquipment()
}
```

**详细执行逻辑**：

1. **触发条件**：
   - **如果**：用户在页面顶部下拉，那么触发刷新事件
   - **前提**：页面配置文件中启用了下拉刷新功能

2. **数据重新加载**：
   - **调用**：this.loadEquipment()方法
   - **如果**：用户想要获取最新设备数据，那么重新加载所有设备信息
   - **自动停止**：loadEquipment方法内部会调用wx.stopPullDownRefresh()停止刷新动画

## 核心功能详解

### 1. 认证状态检查（第87-93行）
```javascript
checkAuth() {
  if (!auth.checkLoginStatus()) {
    auth.redirectToLogin('/pages/equipment/equipment')
    return false
  }
  return true
}
```

**详细执行逻辑**：

1. **登录状态检查**：
   - **调用**：auth.checkLoginStatus()方法
   - **检查内容**：本地存储中的登录状态、token有效性、用户信息完整性
   - **如果**：用户未登录或token过期，那么返回false
   - **如果**：用户已登录且token有效，那么返回true

2. **未登录处理**：
   - **如果**：检查结果为false，那么执行跳转到登录页
   - **重定向参数**：传递当前页面路径'/pages/equipment/equipment'
   - **目的**：用户登录成功后能够回到设备页面
   - **返回值**：返回false，告知调用方认证失败

3. **已登录处理**：
   - **如果**：检查结果为true，那么直接返回true
   - **后续操作**：调用方可以继续执行需要登录的操作

### 2. 设备数据加载（第98-120行）
```javascript
async loadEquipment() {
  if (!this.checkAuth()) return

  this.setData({ loading: true })

  try {
    // 模拟网络延迟
    await new Promise(resolve => setTimeout(resolve, 500))

    // 加载设备数据，只显示可用的设备
    const allEquipment = mockEquipment
      .filter(item => item.available)
      .map(item => ({
        ...item,
        image: item.image || 'http://iph.href.lu/240x180'
      }))

    this.setData({ allEquipment })
    this.applyFiltersAndSort()

    console.log('设备数据加载完成，共', allEquipment.length, '个设备')

  } catch (error) {
    console.error('加载设备数据失败：', error)
    wx.showToast({
      title: '加载失败，请重试',
      icon: 'error'
    })
  } finally {
    this.setData({ loading: false })
    wx.stopPullDownRefresh()
  }
}
```

**详细执行逻辑**：

1. **认证检查**：
   - **如果**：用户未通过认证，那么直接返回，不执行数据加载
   - **目的**：避免未登录用户加载数据造成错误

2. **loading状态设置**：
   - **如果**：开始加载数据，那么设置loading为true
   - **用户体验**：显示loading动画，告知用户正在加载

3. **网络延迟模拟**：
   - **延迟时间**：500毫秒
   - **目的**：模拟真实网络请求的延迟，提供更真实的用户体验
   - **如果**：在真实项目中，那么这里会是实际的API请求

4. **数据筛选和处理**：
   - **可用性筛选**：如果设备的available字段为true，那么包含在结果中
   - **图片处理**：如果设备没有图片，那么使用240x180的占位图片
   - **数据增强**：保持原有设备数据的所有字段，只补充缺失的图片

5. **数据更新和筛选**：
   - **如果**：数据处理完成，那么更新allEquipment字段
   - **调用**：this.applyFiltersAndSort()应用当前的筛选和排序条件
   - **结果**：filteredEquipment会根据当前筛选条件更新

6. **成功处理**：
   - **如果**：数据加载成功，那么记录加载的设备数量
   - **日志信息**：便于开发调试，确认数据加载情况

7. **错误处理**：
   - **如果**：数据加载失败，那么显示错误提示
   - **用户体验**：明确告知用户加载失败，可以重试

8. **状态清理**：
   - **loading状态**：无论成功失败都设为false
   - **下拉刷新**：调用wx.stopPullDownRefresh()停止刷新动画

### 3. 筛选功能详解（第129-133行）
```javascript
onFilterChange(e) {
  const filter = e.currentTarget.dataset.filter
  this.setData({ currentFilter: filter })
  this.applyFilterAndSort()
}
```

**详细执行逻辑**：

1. **筛选条件获取**：
   - **如果**：用户点击筛选按钮，那么从dataset.filter获取筛选类型
   - **数据来源**：来自WXML中绑定的data-filter属性

2. **状态更新**：
   - **如果**：获取到新的筛选条件，那么更新currentFilter字段
   - **UI反馈**：筛选按钮的激活状态会根据currentFilter自动更新

3. **筛选应用**：
   - **如果**：筛选条件更新完成，那么立即调用applyFilterAndSort()
   - **结果**：页面显示的设备列表会根据新的筛选条件更新

### 4. 排序功能详解（第138-145行）
```javascript
onSortChange(e) {
  const sort = e.currentTarget.dataset.sort
  this.setData({
    currentSort: sort,
    showSort: false
  })
  this.applyFilterAndSort()
}
```

**详细执行逻辑**：

1. **排序方式获取**：
   - **如果**：用户选择排序选项，那么从dataset.sort获取排序类型
   - **可选值**：rating、price_low、price_high、popularity

2. **状态更新**：
   - **currentSort更新**：如果用户选择新的排序方式，那么更新当前排序状态
   - **showSort隐藏**：如果用户选择完成，那么隐藏排序选项弹窗

3. **排序应用**：
   - **如果**：排序方式更新完成，那么立即应用新的排序规则
   - **用户体验**：设备列表会按照新的排序方式重新排列

### 5. 筛选和排序应用逻辑（第150-196行）
```javascript
applyFilterAndSort() {
  let filtered = [...this.data.allEquipment]

  // 应用筛选
  switch (this.data.currentFilter) {
    case 'available':
      filtered = filtered.filter(item => item.available)
      break
    case 'professional':
      filtered = filtered.filter(item =>
        item.name.includes('Mavic') || item.name.includes('Air 3')
      )
      break
    case 'entry':
      filtered = filtered.filter(item =>
        item.name.includes('Mini')
      )
      break
    case 'sport':
      filtered = filtered.filter(item =>
        item.name.includes('FPV')
      )
      break
    default:
      // 全部，不筛选
      break
  }

  // 应用排序
  switch (this.data.currentSort) {
    case 'rating':
      filtered.sort((a, b) => b.rating - a.rating)
      break
    case 'price_low':
      filtered.sort((a, b) => a.price - b.price)
      break
    case 'price_high':
      filtered.sort((a, b) => b.price - a.price)
      break
    case 'popularity':
      filtered.sort((a, b) => b.reviewCount - a.reviewCount)
      break
  }

  this.setData({ filteredEquipment: filtered })
  console.log('筛选排序后设备数量：', filtered.length)
}
```

**详细执行逻辑**：

#### 筛选逻辑详解

1. **数据复制**：
   - **如果**：开始筛选，那么创建allEquipment的副本
   - **目的**：避免修改原始数据，保持数据完整性

2. **筛选条件判断**：

   **available（可用设备）**：
   - **如果**：用户选择可用设备，那么只显示available为true的设备
   - **使用场景**：用户只想看当前可租用的设备

   **professional（专业级设备）**：
   - **如果**：用户选择专业级，那么筛选包含'Mavic'或'Air 3'的设备
   - **判断依据**：根据设备名称判断是否为专业级设备
   - **目标用户**：有专业拍摄需求的用户

   **entry（入门级设备）**：
   - **如果**：用户选择入门级，那么筛选包含'Mini'的设备
   - **判断依据**：Mini系列通常为入门级设备
   - **目标用户**：初学者或预算有限的用户

   **sport（运动级设备）**：
   - **如果**：用户选择运动级，那么筛选包含'FPV'的设备
   - **判断依据**：FPV设备通常用于竞速和特技飞行
   - **目标用户**：喜欢刺激飞行体验的用户

   **default（全部设备）**：
   - **如果**：用户选择全部，那么不进行筛选
   - **结果**：显示所有设备

#### 排序逻辑详解

1. **rating（按评分排序）**：
   - **如果**：用户选择按评分排序，那么评分高的设备排在前面
   - **排序算法**：b.rating - a.rating（降序排列）
   - **用户价值**：优先看到用户评价最好的设备

2. **price_low（价格从低到高）**：
   - **如果**：用户选择价格从低到高，那么便宜的设备排在前面
   - **排序算法**：a.price - b.price（升序排列）
   - **用户价值**：预算有限的用户优先看到便宜的设备

3. **price_high（价格从高到低）**：
   - **如果**：用户选择价格从高到低，那么昂贵的设备排在前面
   - **排序算法**：b.price - a.price（降序排列）
   - **用户价值**：追求高端设备的用户优先看到专业设备

4. **popularity（按热度排序）**：
   - **如果**：用户选择按热度排序，那么评价数量多的设备排在前面
   - **排序算法**：b.reviewCount - a.reviewCount（降序排列）
   - **用户价值**：优先看到最受欢迎的设备

#### 结果更新

1. **数据更新**：
   - **如果**：筛选和排序完成，那么更新filteredEquipment字段
   - **页面响应**：设备列表会立即根据新数据重新渲染

2. **日志记录**：
   - **如果**：处理完成，那么记录筛选后的设备数量
   - **开发价值**：便于调试筛选逻辑是否正确

### 6. 筛选重置功能（第201-207行）
```javascript
resetFilter() {
  this.setData({
    currentFilter: 'all',
    currentSort: 'rating'
  })
  this.applyFilterAndSort()
}
```

**详细执行逻辑**：

1. **状态重置**：
   - **currentFilter重置**：如果用户点击重置，那么将筛选条件设为'all'
   - **currentSort重置**：如果用户点击重置，那么将排序方式设为'rating'

2. **筛选应用**：
   - **如果**：状态重置完成，那么立即应用默认的筛选和排序
   - **结果**：页面显示所有设备，按评分从高到低排序

3. **使用场景**：
   - **如果**：用户筛选后没有找到合适设备，那么可以点击重置查看所有设备
   - **如果**：用户想要回到默认状态，那么使用重置功能

### 7. 排序选项控制（第212-221行）
```javascript
showSortOptions() {
  this.setData({ showSort: true })
}

hideSortOptions() {
  this.setData({ showSort: false })
}
```

**详细执行逻辑**：

#### showSortOptions（显示排序选项）
- **触发时机**：如果用户点击排序图标⚡，那么调用此方法
- **状态变更**：设置showSort为true，显示排序选项弹窗
- **用户体验**：排序选项从底部弹出，覆盖在页面内容上方

#### hideSortOptions（隐藏排序选项）
- **触发时机**：
  - **如果**：用户点击弹窗外部区域，那么调用此方法
  - **如果**：用户点击关闭按钮✕，那么调用此方法
  - **如果**：用户选择排序选项，那么在onSortChange中自动调用
- **状态变更**：设置showSort为false，隐藏排序选项弹窗

### 8. 页面导航功能（第233-250行）

#### 设备详情跳转
```javascript
goToDetail(e) {
  const id = e.currentTarget.dataset.id
  wx.navigateTo({
    url: `/pages/equipment-detail/equipment-detail?id=${id}`
  })
}
```

**详细执行逻辑**：
- **触发时机**：如果用户点击设备卡片，那么调用此方法
- **参数获取**：从dataset.id获取设备的唯一标识
- **页面跳转**：跳转到设备详情页面，传递设备id参数

#### 设备租用功能
```javascript
rentEquipment(e) {
  const id = e.currentTarget.dataset.id
  console.log('租用设备:', id)

  wx.navigateTo({
    url: `/pages/equipment-detail/equipment-detail?id=${id}`
  })
}
```

**详细执行逻辑**：

1. **参数获取**：
   - **如果**：用户点击"查看详情"按钮，那么从dataset.id获取设备id
   - **日志记录**：记录用户的租用意图，便于数据分析

2. **页面跳转**：
   - **如果**：设备可用，那么跳转到设备详情页面
   - **如果**：设备不可用，那么按钮显示为"暂不可用"且禁用状态

3. **事件处理**：
   - **catchtap="stopPropagation"**：如果用户点击按钮，那么阻止事件冒泡到父元素
   - **目的**：避免同时触发设备卡片的点击事件

### 9. 事件冒泡控制（第226-228行，第255-257行）
```javascript
stopPropagation() {
  // 阻止事件冒泡
}
```

**详细说明**：
- **使用场景**：如果子元素的点击事件不应该触发父元素的点击事件，那么调用此方法
- **典型应用**：
  - **排序弹窗内容区域**：点击时不应该关闭弹窗
  - **租用按钮**：点击时不应该触发设备卡片的点击事件

## WXML结构详解

### 1. 页面整体结构（第2-12行）
```xml
<view class="equipment-container">
  <!-- 顶部导航 -->
  <view class="header">
    <view class="header-content">
      <text class="header-title">设备租赁</text>
      <view class="header-actions">
        <text class="action-icon" bindtap="showSortOptions">⚡</text>
      </view>
    </view>
  </view>
</view>
```

**结构说明**：

1. **equipment-container**：
   - **作用**：整个设备页面的容器
   - **如果**：页面需要统一的背景和布局，那么使用此容器

2. **header（顶部导航）**：
   - **header-title**：如果需要显示页面标题，那么显示"设备租赁"
   - **header-actions**：如果需要功能按钮，那么显示排序图标⚡
   - **点击事件**：如果用户点击排序图标，那么触发showSortOptions方法

### 2. 筛选栏结构（第14-55行）
```xml
<view class="filter-bar">
  <scroll-view class="filter-scroll" scroll-x="true">
    <view class="filter-list">
      <view
        class="filter-item {{currentFilter === 'all' ? 'active' : ''}}"
        bindtap="onFilterChange"
        data-filter="all"
      >
        全部
      </view>
      <!-- 其他筛选项... -->
    </view>
  </scroll-view>
</view>
```

**详细说明**：

1. **scroll-view配置**：
   - **scroll-x="true"**：如果筛选项过多，那么支持水平滚动
   - **目的**：确保所有筛选选项都能被用户访问

2. **筛选项状态**：
   - **class绑定**：如果currentFilter等于当前项的值，那么添加active类
   - **视觉反馈**：激活的筛选项会有不同的样式显示

3. **筛选项类型**：
   - **全部（all）**：如果用户选择，那么显示所有设备
   - **专业级（professional）**：如果用户选择，那么只显示专业级设备
   - **入门级（entry）**：如果用户选择，那么只显示入门级设备
   - **便携式（portable）**：如果用户选择，那么只显示便携式设备

### 3. 设备列表结构（第58-116行）
```xml
<view class="equipment-list" wx:if="{{filteredEquipment.length > 0}}">
  <view
    class="equipment-card"
    wx:for="{{filteredEquipment}}"
    wx:key="id"
    bindtap="goToDetail"
    data-id="{{item.id}}"
  >
    <view class="card-content">
      <view class="equipment-image-section">
        <image
          class="equipment-image"
          src="{{item.image || 'http://iph.href.lu/240x180'}}"
          mode="aspectFill"
        />
        <view class="price-section">
          <view class="price-info">
            <text class="price-amount">¥{{item.price}}</text>
            <text class="price-unit">/{{item.unit}}</text>
          </view>
        </view>
      </view>

      <view class="equipment-info">
        <view class="equipment-header">
          <text class="equipment-name">{{item.name}}</text>
          <view class="equipment-status {{item.available ? 'available' : 'unavailable'}}">
            {{item.available ? '可租用' : '使用中'}}
          </view>
        </view>

        <text class="equipment-desc">{{item.description}}</text>

        <view class="equipment-meta">
          <view class="rating-info">
            <text class="rating-star">⭐</text>
            <text class="rating-text">{{item.rating}} ({{item.reviewCount}})</text>
          </view>
          <view class="availability-info">
            <text class="availability-icon">🕐</text>
            <text class="availability-text">{{item.availabilityText}}</text>
          </view>
        </view>

        <view class="button-section">
          <button
            class="rent-btn {{item.available ? '' : 'disabled'}}"
            disabled="{{!item.available}}"
            bindtap="rentEquipment"
            data-id="{{item.id}}"
            catchtap="stopPropagation"
          >
            {{item.available ? '查看详情' : '暂不可用'}}
          </button>
        </view>
      </view>
    </view>
  </view>
</view>
```

**详细说明**：

#### 列表渲染逻辑
1. **显示条件**：
   - **wx:if="{{filteredEquipment.length > 0}}"**：如果有筛选后的设备数据，那么显示设备列表
   - **如果**：没有设备数据，那么显示空状态

2. **循环渲染**：
   - **wx:for="{{filteredEquipment}}"**：如果有设备数据，那么循环渲染设备卡片
   - **wx:key="id"**：如果需要唯一标识，那么使用设备id作为key

#### 设备卡片结构
1. **图片区域**：
   - **图片处理**：如果设备有图片，那么使用真实图片；如果没有，那么使用240x180的占位图片
   - **mode="aspectFill"**：如果图片尺寸不匹配，那么保持宽高比并填充容器
   - **价格显示**：在图片上方显示设备价格和计费单位

2. **信息区域**：
   - **设备名称**：显示设备的完整名称
   - **可用状态**：如果设备可用，那么显示"可租用"；如果不可用，那么显示"使用中"
   - **设备描述**：显示设备的详细描述信息

3. **元数据区域**：
   - **评分信息**：显示设备评分和评价数量
   - **可用性信息**：显示设备的可用时间信息

4. **操作按钮**：
   - **按钮状态**：如果设备可用，那么显示"查看详情"；如果不可用，那么显示"暂不可用"并禁用
   - **事件处理**：使用catchtap阻止事件冒泡，避免同时触发卡片点击事件

### 4. 空状态和加载状态（第118-131行）
```xml
<!-- 空状态 -->
<view class="empty-state" wx:if="{{filteredEquipment.length === 0 && !loading}}">
  <text class="empty-icon">🚁</text>
  <text class="empty-title">暂无设备</text>
  <text class="empty-desc">当前筛选条件下没有找到设备</text>
  <button class="btn btn-primary" bindtap="resetFilter">重置筛选</button>
</view>

<!-- 加载状态 -->
<view class="loading-state" wx:if="{{loading}}">
  <text class="loading-icon">⏳</text>
  <text class="loading-text">加载中...</text>
</view>
```

**详细说明**：

#### 空状态显示
1. **显示条件**：
   - **如果**：筛选后的设备数量为0且不在加载状态，那么显示空状态
   - **目的**：告知用户当前筛选条件下没有找到设备

2. **用户引导**：
   - **重置按钮**：如果用户想要查看所有设备，那么可以点击"重置筛选"
   - **用户体验**：提供明确的解决方案，而不是让用户困惑

#### 加载状态显示
1. **显示条件**：
   - **如果**：loading为true，那么显示加载状态
   - **目的**：告知用户数据正在加载，请耐心等待

2. **视觉反馈**：
   - **加载图标**：使用⏳图标表示正在加载
   - **加载文字**：明确告知用户当前状态

### 5. 排序选项弹窗（第133-179行）
```xml
<view class="sort-modal" wx:if="{{showSort}}" bindtap="hideSortOptions">
  <view class="sort-modal-content" catchtap="stopPropagation">
    <view class="sort-header">
      <text class="sort-title">排序方式</text>
      <text class="sort-close" bindtap="hideSortOptions">✕</text>
    </view>

    <view class="sort-options">
      <view
        class="sort-option {{currentSort === 'rating' ? 'active' : ''}}"
        bindtap="onSortChange"
        data-sort="rating"
      >
        <text class="sort-option-text">评分最高</text>
        <text class="sort-option-icon" wx:if="{{currentSort === 'rating'}}">✓</text>
      </view>
      <!-- 其他排序选项... -->
    </view>
  </view>
</view>
```

**详细说明**：

#### 弹窗控制逻辑
1. **显示条件**：
   - **wx:if="{{showSort}}"**：如果showSort为true，那么显示排序弹窗
   - **背景点击**：如果用户点击弹窗背景，那么调用hideSortOptions关闭弹窗

2. **内容区域**：
   - **catchtap="stopPropagation"**：如果用户点击内容区域，那么阻止事件冒泡，不关闭弹窗
   - **关闭按钮**：如果用户点击✕按钮，那么关闭弹窗

#### 排序选项结构
1. **选项状态**：
   - **class绑定**：如果currentSort等于当前选项的值，那么添加active类
   - **选中图标**：如果选项被选中，那么显示✓图标

2. **排序选项类型**：
   - **评分最高（rating）**：如果用户选择，那么按评分降序排列
   - **价格从低到高（price_low）**：如果用户选择，那么按价格升序排列
   - **价格从高到低（price_high）**：如果用户选择，那么按价格降序排列
   - **最受欢迎（popularity）**：如果用户选择，那么按评价数量降序排列

## 总结

设备页面作为用户选择租用设备的核心页面，实现了以下关键功能：

1. **设备展示系统**：如果用户需要查看设备，那么提供完整的设备信息展示，包括图片、价格、评分、可用状态等
2. **智能筛选功能**：如果用户有特定需求，那么可以按设备类型筛选，快速找到合适的设备
3. **灵活排序机制**：如果用户有不同的优先级，那么可以按评分、价格、热度等方式排序
4. **用户友好的交互**：如果没有找到设备，那么提供重置筛选的选项；如果数据加载中，那么显示明确的加载状态
5. **无缝页面跳转**：如果用户选中设备，那么可以直接跳转到设备详情页面进行租用

整个页面的设计遵循了"如果...那么..."的条件逻辑，确保在各种用户操作和数据状态下都能提供合适的响应和体验。
