<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>登录 - 逍遥境</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body { height: 852px; overflow: hidden; }
        .status-bar {
            background: black;
            color: white;
            height: 47px;
            font-size: 14px;
            font-weight: 600;
        }
        .gradient-bg {
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
        }
        .btn-primary {
            background: linear-gradient(135deg, #1f2937 0%, #374151 100%);
            transition: all 0.3s ease;
        }
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(31, 41, 55, 0.3);
        }
        .btn-secondary {
            border: 2px solid #1f2937;
            transition: all 0.3s ease;
        }
        .btn-secondary:hover {
            background: #1f2937;
            color: white;
        }
    </style>
</head>
<body class="gradient-bg">
    <!-- iOS 状态栏 -->
    <div class="status-bar flex justify-between items-center px-6">
        <div class="flex items-center space-x-1">
            <span>9:41</span>
        </div>
        <div class="flex items-center space-x-1">
            <i class="fas fa-signal text-xs"></i>
            <i class="fas fa-wifi text-xs"></i>
            <div class="w-6 h-3 border border-white rounded-sm">
                <div class="w-4 h-1 bg-white rounded-sm mt-0.5 ml-0.5"></div>
            </div>
        </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="flex flex-col h-full px-8 py-12">
        <!-- Logo 和标题区域 -->
        <div class="text-center mb-16 mt-16">
            <div class="w-24 h-24 mx-auto mb-6 bg-gradient-to-br from-gray-800 to-gray-600 rounded-2xl flex items-center justify-center shadow-xl">
                <i class="fas fa-helicopter text-white text-3xl"></i>
            </div>
            <h1 class="text-3xl font-bold text-gray-800 mb-2">逍遥境</h1>
            <p class="text-gray-500 text-lg">专业无人机租赁平台</p>
        </div>

        <!-- 登录按钮区域 -->
        <div class="space-y-4 mb-8">
            <!-- 微信登录按钮 -->
            <button onclick="wechatLogin()" class="btn-primary w-full py-4 rounded-2xl text-white font-semibold flex items-center justify-center space-x-3 shadow-lg">
                <i class="fab fa-weixin text-xl"></i>
                <span>微信一键登录</span>
            </button>

            <!-- 手机号登录按钮 -->
            <button onclick="phoneLogin()" class="btn-secondary w-full py-4 rounded-2xl text-gray-800 font-semibold flex items-center justify-center space-x-3">
                <i class="fas fa-mobile-alt text-xl"></i>
                <span>微信手机号授权登录</span>
            </button>
        </div>

        <!-- 服务协议 -->
        <div class="text-center text-sm text-gray-400 mt-auto mb-8">
            <p>登录即表示同意</p>
            <div class="flex justify-center space-x-4 mt-2">
                <span class="text-gray-600 underline">《服务协议》</span>
                <span class="text-gray-600 underline">《隐私政策》</span>
            </div>
        </div>
    </div>

    <script>
        function wechatLogin() {
            alert('微信登录成功！正在跳转到首页...');
            setTimeout(() => {
                window.parent.postMessage({type: 'navigate', page: 'home'}, '*');
            }, 1000);
        }

        function phoneLogin() {
            const phone = prompt('请输入手机号码:');
            if (phone && phone.length === 11) {
                const code = prompt('请输入验证码:');
                if (code) {
                    alert('手机号登录成功！正在跳转到首页...');
                    setTimeout(() => {
                        window.parent.postMessage({type: 'navigate', page: 'home'}, '*');
                    }, 1000);
                }
            }
        }
    </script>
</body>
</html> 