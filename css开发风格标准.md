# ChatGPT 高级线条·简约·丝滑 CSS 开发文档

> 版本：v1.1  ｜  最后更新：2025‑07‑03

---

## 目录

1. [概述](#概述)
2. [设计理念](#设计理念)
3. [语义化命名](#语义化命名)
4. [变量与主题](#变量与主题)
5. [排版](#排版)
6. [颜色系统](#颜色系统)
7. [线条与边框](#线条与边框)
8. [布局与网格](#布局与网格)
9. [组件样式](#组件样式)
10. [动效与过渡](#动效与过渡)
11. [实用工具类](#实用工具类)
12. [可访问性](#可访问性)
13. [性能与构建](#性能与构建)
14. [版本控制与贡献指南](#版本控制与贡献指南)
15. [常见问题](#常见问题)
16. [附录：代码示例](#附录代码示例)

---

## 概述

本指南定义了符合 **ChatGPT** 产品体验的 CSS 风格与工程规范，特别强调“高级线条大厂简约”视觉风格与“丝滑”微交互体验，旨在：

* 统一视觉与交互体验，确保品牌一致性
* 降低设计‑开发沟通成本，加快交付
* 提升可维护性与可扩展性，易于多品牌/多主题

## 设计理念

* **高级线条**：以 1 px 物理像素线为骨架，配合恰到好处的阴影与留白，构建轻量但结构清晰的层次。
* **简约克制**：以内容为中心，移除冗余装饰，遵循“少即是多”。
* **丝滑交互**：过渡流畅、反馈及时，无感加载，60 fps 作为基线。
* **可达性优先**：遵循 WCAG 2.2 AA 级标准，兼顾视觉与听觉辅助。
* **移动先行**：自小及大，逐步增强。
* **可主题化**：基于 CSS 变量实现亮/暗、多品牌主题切换。
* **现代标准**：原生 CSS、Flex、Grid、View Transition API；尽量避免 JS DOM 强依赖。

## 语义化命名

| 类别 | 前缀             | 示例                          |
| -- | -------------- | --------------------------- |
| 组件 | `c-`           | `c-button`, `c-card`        |
| 布局 | `l-`           | `l-container`, `l-stack`    |
| 工具 | `u-`           | `u-hide`, `u-text-truncate` |
| 状态 | `is-` / `has-` | `is-active`, `has-error`    |

遵循 BEM，可读性 > 简短性，避免缩写造成歧义。

## 变量与主题

CSS 变量集中在 `:root` (或 `[data-theme="dark"]`)。命名使用 **kebab-case**，统一前缀 `--gpt-`。

```css
:root {
  /* 色彩 */
  --gpt-color-base-0: #ffffff;
  --gpt-color-base-100: #0b0f19;
  --gpt-color-accent: #10a37f;
  --gpt-color-danger: #ff5d5d;
  --gpt-color-warning: #ffb020;
  --gpt-color-success: #10a37f;

  /* 边框与线条 */
  --gpt-border-color-strong: #d0d5dd;
  --gpt-border-color-subtle: color-mix(in srgb, var(--gpt-color-base-100) 8%, #fff 92%);
  --gpt-border-width: 1px; /* 物理像素 */
  --gpt-border-width-hairline: 0.5px; /* Retina Hairline */

  /* 排版 */
  --gpt-font-family-sans: "Inter", "Helvetica Neue", Arial, sans-serif;
  --gpt-font-size-0: 0.75rem; /* 12px */
  --gpt-font-size-1: 0.875rem; /* 14px */
  --gpt-font-size-2: 1rem; /* 16px */
  --gpt-font-size-3: 1.25rem; /* 20px */

  /* 间距 (4‑系数) */
  --gpt-space-0: 0.25rem; /* 4px */
  --gpt-space-1: 0.5rem;  /* 8px */
  --gpt-space-2: 1rem;    /* 16px */
  --gpt-space-3: 1.5rem;  /* 24px */

  /* 圆角与阴影 */
  --gpt-radius-sm: 0.25rem;
  --gpt-radius-md: 0.5rem;
  --gpt-radius-xl: 1rem;
  --gpt-shadow-xs: 0 1px 2px rgba(0,0,0,.05);
}

[data-theme="dark"] {
  --gpt-color-base-0: #121417;
  --gpt-color-base-100: #e4e4e7;
  --gpt-border-color-subtle: color-mix(in srgb, var(--gpt-color-base-100) 16%, #000 84%);
}
```

## 排版

### 字体栈

* 主字体：`Inter` (回退 `Roboto`, `Helvetica Neue`, `Arial`)
* 等宽字体：`SFMono-Regular`, `Menlo`, `Monaco`

### 字号刻度 & 行高

| 级别                  | 字号   | 行高   |
| ------------------- | ---- | ---- |
| `--gpt-font-size-0` | 12px | 18px |
| `--gpt-font-size-1` | 14px | 20px |
| `--gpt-font-size-2` | 16px | 24px |
| `--gpt-font-size-3` | 20px | 28px |

### 字重

* 400：正文
* 500：按钮、子标题
* 600：主标题

## 颜色系统

### 基础色板

| Token                  | 说明  | HEX     |
| ---------------------- | --- | ------- |
| `--gpt-color-base-0`   | 背景  | #FFFFFF |
| `--gpt-color-base-100` | 主文本 | #0B0F19 |
| `--gpt-color-accent`   | 品牌  | #10A37F |

### 功能色

* 成功：`--gpt-color-success`
* 警告：`--gpt-color-warning`
* 危险：`--gpt-color-danger`

### 暗黑模式映射

使用 `[data-theme="dark"]` 重新定义变量，无需额外类。

## 线条与边框

### 设计原则

1. **最小可视化**：使用 1 px 物理像素，避免 2 px 以上粗线，保持 UI 精致。
2. **分层表达**：强边框用于卡片、输入框；弱线条用于分隔符、表格网格。
3. **留白优先**：线条只是辅助，以足够留白划分区块。

### Token 定义

| Token                         | 用途               |
| ----------------------------- | ---------------- |
| `--gpt-border-color-strong`   | 主边框、输入框、按钮边框     |
| `--gpt-border-color-subtle`   | 分隔线、表格网格         |
| `--gpt-border-width`          | 默认 1 px 物理像素     |
| `--gpt-border-width-hairline` | 0.5 px Retina 细线 |

### 使用示例

```css
.c-divider {
  width: 100%;
  height: var(--gpt-border-width);
  background: var(--gpt-border-color-subtle);
}
@media (min-resolution: 2dppx) {
  .c-divider {
    transform: scaleY(0.5);
    transform-origin: 0 0;
  }
}
```

> **提示**：暗黑模式下请调整线条颜色而非透明度，以避免灰背景上的发灰现象。

## 布局与网格

### 响应式断点

| 名称     | 尺寸       |
| ------ | -------- |
| `--sm` | ≥ 640px  |
| `--md` | ≥ 768px  |
| `--lg` | ≥ 1024px |
| `--xl` | ≥ 1280px |

### Flex & Grid 约定

```css
.l-stack {
  display: flex;
  flex-direction: column;
  gap: var(--gpt-space-2);
}

.l-grid-12 {
  display: grid;
  grid-template-columns: repeat(12, 1fr);
  gap: var(--gpt-space-2);
}
```

## 组件样式

### 按钮 `c-button`

```css
.c-button {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: var(--gpt-space-1) var(--gpt-space-2);
  border-radius: var(--gpt-radius-md);
  font-size: var(--gpt-font-size-1);
  font-weight: 500;
  background-color: var(--gpt-color-accent);
  color: var(--gpt-color-base-0);
  border: none;
  transition: background-color .2s ease, transform .1s ease;
}
.c-button:hover {
  background-color: color-mix(in srgb, var(--gpt-color-accent) 90%, #000 10%);
  transform: translateY(-2px);
}
.c-button:active {
  transform: scale(.97);
}
.c-button:disabled {
  opacity: .5;
  cursor: not-allowed;
}
```

### 表单输入 `c-input`

```css
.c-input {
  width: 100%;
  height: 2.5rem;
  padding: 0 var(--gpt-space-2);
  border: var(--gpt-border-width) solid var(--gpt-border-color-strong);
  border-radius: var(--gpt-radius-md);
  font-size: var(--gpt-font-size-2);
  transition: border-color .2s ease, box-shadow .2s ease;
}
.c-input:focus {
  outline: none;
  border-color: var(--gpt-color-accent);
  box-shadow: 0 0 0 2px color-mix(in srgb, var(--gpt-color-accent) 40%, transparent);
}
```

### 卡片 `c-card`

```css
.c-card {
  background: var(--gpt-color-base-0);
  border: var(--gpt-border-width) solid var(--gpt-border-color-subtle);
  border-radius: var(--gpt-radius-lg, var(--gpt-radius-md));
  box-shadow: var(--gpt-shadow-xs);
  padding: var(--gpt-space-3);
}
```

> 其余组件（对话框、标签、表格等）请结合组件库目录中的 demo 实现。

## 动效与过渡

### 原则

1. **即时反馈**：<100 ms 的交互反馈被视为即时。
2. **节奏一致**：《基准时长》+《统一缓动曲线》保障体验连贯。
3. **性能优先**：仅动画 `opacity` 与 `transform`，避免布局抖动。

### 时长层级

| 场景                | 时长         |
| ----------------- | ---------- |
| 微反馈（hover/active） | \~100 ms   |
| 状态切换（展开/折叠）       | 200–300 ms |
| 页面级转场             | 300–500 ms |

### 缓动曲线

* 默认：`cubic-bezier(.16,1,.3,1)`
* 强调：`cubic-bezier(.4,0,.2,1)`

### 交互动效示例

```css
@keyframes fade-slide {
  from { opacity: 0; transform: translateY(4px); }
  to { opacity: 1; transform: translateY(0); }
}

[data-animate="fade-slide"] {
  animation: fade-slide .25s both;
}
```

> **进阶**：结合 View Transition API 实现跨页面丝滑转场，详见附录示例。

## 实用工具类

| 名称                | 功能                          |
| ----------------- | --------------------------- |
| `u-hide`          | `display: none !important;` |
| `u-text-truncate` | 单行省略号                       |
| \`u-sr            |                             |



其他的请你自己想象！