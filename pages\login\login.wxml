<!--pages/login/login.wxml-->
<view class="login-container">
  <!-- 背景装饰 -->
  <view class="bg-gradient"></view>

  <!-- 主要内容区域 -->
  <view class="main-content">
    <!-- 顶部Logo区域 -->
    <view class="logo-section">
      <view class="logo-icon">
        <text class="icon">✈️</text>
      </view>
      <view class="logo-text">
        <text class="app-name">逍遥境</text>
        <text class="app-desc">专业无人机租赁平台</text>
      </view>
    </view>

    <!-- 欢迎文案 -->
    <view class="welcome-section">
      <text class="welcome-title">欢迎来到逍遥境</text>
      <text class="welcome-desc">让每一次飞行都充满无限可能</text>
    </view>

    <!-- 登录方式选择 -->
    <view class="login-methods">
    <!-- 微信登录 -->
    <button
      class="btn login-btn wx-login-btn"
      bindtap="handleWechatLogin"
      loading="{{wxLoading}}"
    >
      <text class="btn-icon">🚀</text>
      <text class="btn-text">微信一键登录</text>
    </button>

    <!-- 手机号授权登录 -->
    <button
      class="btn login-btn phone-auth-btn"
      open-type="getPhoneNumber"
      bindgetphonenumber="handlePhoneAuthLogin"
      loading="{{phoneLoading}}"
    >
      <text class="btn-icon">📱</text>
      <text class="btn-text">手机号授权登录</text>
    </button>
  </view>
  </view>

  <!-- 装饰元素 -->
  <view class="decoration-dots">
    <view class="dot dot-1"></view>
    <view class="dot dot-2"></view>
    <view class="dot dot-3"></view>
  </view>

  <!-- 协议条款 - 底部固定 -->
  <view class="agreement-section">
    <view class="agreement-wrapper">
      <checkbox
        class="agreement-checkbox"
        checked="{{agreeTerms}}"
        bindchange="onAgreeChange"
      />
      <text class="agreement-text">
        我已阅读并同意<text class="link-text" bindtap="viewTerms">《用户协议》</text>和<text class="link-text" bindtap="viewPrivacy">《隐私政策》</text>
      </text>
    </view>
  </view>
</view>

<!-- 加载遮罩 -->
<view class="loading-mask" wx:if="{{isLoading}}">
  <view class="loading-content">
    <view class="loading-spinner"></view>
    <text class="loading-text">{{loadingText}}</text>
  </view>
</view>   