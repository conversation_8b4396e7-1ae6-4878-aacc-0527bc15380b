/* pages/gallery/gallery.wxss */

.gallery-container {
  background-color: #f9fafb;
  min-height: 100vh;
  width: 100%;
  overflow-x: hidden;
}

/* 顶部导航 */
.header {
  background: #ffffff;
  padding: 32rpx 48rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

.header-content {
  display: flex;
  align-items: flex-start;
  justify-content: flex-start;
  padding: 32rpx 48rpx;
}

.header-info {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 8rpx;
  text-align: left;
}

.header-title {
  font-size: 48rpx;
  font-weight: 700;
  color: #1f2937;
  white-space: nowrap;
  display: block;
  line-height: 1.2;
  writing-mode: horizontal-tb;
  text-orientation: mixed;
}

.header-subtitle {
  font-size: 28rpx;
  color: #6b7280;
  display: block;
  line-height: 1.2;
  white-space: nowrap;
  writing-mode: horizontal-tb;
  text-orientation: mixed;
}

/* 筛选栏 */
.filter-bar {
  background: #ffffff;
  padding: 32rpx 0;
  border-bottom: 2rpx solid #f3f4f6;
}

.filter-scroll {
  white-space: nowrap;
}

.filter-list {
  display: flex;
  padding: 0 48rpx;
  gap: 24rpx;
}

.filter-item {
  padding: 16rpx 32rpx;
  border-radius: 50rpx;
  font-size: 28rpx;
  color: #6b7280;
  background: transparent;
  border: 2rpx solid #e5e7eb;
  transition: all 0.2s ease;
  flex-shrink: 0;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  white-space: nowrap;
}

.filter-item.active {
  background: #1f2937;
  color: #ffffff;
  border-color: #1f2937;
  font-weight: 500;
}

/* 统计信息 */
.stats-section {
  padding: 32rpx 48rpx;
  background: linear-gradient(135deg, #dbeafe 0%, #e0e7ff 100%);
}

.stats-grid {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  gap: 32rpx;
}

.stats-item {
  text-align: center;
}

.stats-number {
  display: block;
  font-size: 36rpx;
  font-weight: 700;
  color: #1f2937;
  margin-bottom: 8rpx;
}

.stats-label {
  font-size: 24rpx;
  color: #6b7280;
}

/* 媒体网格 */
.media-grid {
  padding: 32rpx;
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 16rpx;
  width: 100%;
  box-sizing: border-box;
}

.media-item {
  position: relative;
  aspect-ratio: 1;
  overflow: hidden;
  border-radius: 24rpx;
  cursor: pointer;
  transition: all 0.3s ease;
}

.media-item:hover {
  transform: scale(1.05);
  box-shadow: 0 20rpx 40rpx rgba(0, 0, 0, 0.2);
}

.select-checkbox {
  position: absolute;
  top: 16rpx;
  left: 16rpx;
  width: 48rpx;
  height: 48rpx;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10;
  border: 2rpx solid #e5e7eb;
}

.checkbox-icon {
  font-size: 24rpx;
  color: #10b981;
  font-weight: 600;
}

.media-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.video-overlay {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 80rpx;
  height: 80rpx;
  background: rgba(0, 0, 0, 0.7);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 5;
}

.play-icon {
  font-size: 32rpx;
  color: #ffffff;
  margin-left: 4rpx;
}

.media-info {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(transparent, rgba(0, 0, 0, 0.8));
  color: #ffffff;
  padding: 32rpx 24rpx 16rpx;
}

.info-content {
  display: flex;
  justify-content: space-between;
  align-items: flex-end;
}

.media-location {
  font-size: 24rpx;
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.location-icon {
  font-size: 20rpx;
}

.media-date {
  font-size: 24rpx;
  opacity: 0.8;
}

.work-stats {
  display: flex;
  gap: 16rpx;
}

.work-stat {
  display: flex;
  align-items: center;
  gap: 4rpx;
}

.work-type-badge {
  position: absolute;
  top: 16rpx;
  right: 16rpx;
  background: rgba(0, 0, 0, 0.6);
  color: #ffffff;
  padding: 8rpx 16rpx;
  border-radius: 16rpx;
  font-size: 24rpx;
  backdrop-filter: blur(10rpx);
}

/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 120rpx 64rpx;
  text-align: center;
}

.empty-icon {
  font-size: 120rpx;
  margin-bottom: 32rpx;
  opacity: 0.6;
}

.empty-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 16rpx;
}

.empty-desc {
  font-size: 28rpx;
  color: #6b7280;
  margin-bottom: 48rpx;
  line-height: 1.5;
}

.empty-action {
  padding: 24rpx 48rpx;
  background: #10a37f;
  color: #ffffff;
  border-radius: 24rpx;
  font-size: 32rpx;
  font-weight: 500;
  border: none;
  cursor: pointer;
  transition: all 0.2s ease;
}

.empty-action:hover {
  background: #059669;
  transform: translateY(-2rpx);
}

/* 加载更多 */
.load-more-item {
  aspect-ratio: 1;
  background: #f3f4f6;
  border-radius: 24rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
}

.load-more-item:hover {
  background: #e5e7eb;
}

.load-more-content {
  text-align: center;
  color: #6b7280;
}

.load-more-icon {
  font-size: 48rpx;
  display: block;
  margin-bottom: 8rpx;
}

.load-more-text {
  font-size: 24rpx;
}

/* 媒体查看模态框 */
.media-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.9);
  z-index: 1000;
  display: none;
  align-items: center;
  justify-content: center;
}

.media-modal.show {
  display: flex;
}

.modal-content {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 32rpx;
}

.media-container {
  position: relative;
  max-width: 100%;
  max-height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.modal-image,
.modal-video {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
  border-radius: 16rpx;
}

.modal-close {
  position: absolute;
  top: -70rpx;
  right: -100rpx;
  z-index: 10;
  width: 60rpx;
  height: 60rpx;
  background: transparent;
  border: none;
  color: #ffffff;
  font-size: 36rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  text-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.8);
}

.modal-close:hover {
  transform: scale(1.2);
  color: #ff4444;
}

.modal-close:active {
  transform: scale(0.9);
}

.close-icon {
  font-weight: 300;
  line-height: 1;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  text-shadow: inherit;
}

.modal-image,
.modal-video {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
  border-radius: 16rpx;
}

.modal-actions {
  position: absolute;
  bottom: 64rpx;
  left: 50%;
  transform: translateX(-50%);
  background: rgba(0, 0, 0, 0.7);
  border-radius: 50rpx;
  padding: 24rpx 48rpx;
  display: flex;
  gap: 48rpx;
}

.action-btn {
  background: transparent;
  border: none;
  color: #ffffff;
  font-size: 40rpx;
  padding: 16rpx;
  cursor: pointer;
  transition: all 0.3s ease;
}

.action-btn:hover {
  transform: scale(1.1);
}

.modal-info {
  position: absolute;
  bottom: 192rpx;
  left: 64rpx;
  color: #ffffff;
}

.modal-title {
  font-size: 36rpx;
  font-weight: 600;
  margin-bottom: 16rpx;
  display: block;
}

.modal-details {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.detail-item {
  font-size: 28rpx;
  opacity: 0.8;
}