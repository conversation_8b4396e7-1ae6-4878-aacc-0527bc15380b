<!--pages/equipment/equipment.wxml-->
<view class="equipment-container">
  <!-- 顶部导航 -->
  <view class="header">
    <view class="header-content">
      <text class="header-title">设备租赁</text>
      <view class="header-actions">
        <!-- <text class="action-icon" bindtap="showFilterOptions">🔍</text> -->
        <text class="action-icon" bindtap="showSortOptions">⚡</text>
      </view>
    </view>
  </view>

  <!-- 筛选栏 -->
  <view class="filter-bar">
    <scroll-view class="filter-scroll" scroll-x="true">
      <view class="filter-list">
        <view
          class="filter-item {{currentFilter === 'all' ? 'active' : ''}}"
          bindtap="onFilterChange"
          data-filter="all"
        >
          全部
        </view>
        <view
          class="filter-item {{currentFilter === 'professional' ? 'active' : ''}}"
          bindtap="onFilterChange"
          data-filter="professional"
        >
          专业级
        </view>
        <view
          class="filter-item {{currentFilter === 'entry' ? 'active' : ''}}"
          bindtap="onFilterChange"
          data-filter="entry"
        >
          入门级
        </view>
        <view
          class="filter-item {{currentFilter === 'portable' ? 'active' : ''}}"
          bindtap="onFilterChange"
          data-filter="portable"
        >
          便携式
        </view>
        <!-- <view
          class="filter-item {{currentFilter === 'price' ? 'active' : ''}}"
          bindtap="onFilterChange"
          data-filter="price"
        >
          ¥50-80
        </view> -->
      </view>
    </scroll-view>
  </view>

  <!-- 设备列表 -->
  <view class="equipment-list" wx:if="{{filteredEquipment.length > 0}}">
    <view
      class="equipment-card"
      wx:for="{{filteredEquipment}}"
      wx:key="id"
      bindtap="goToDetail"
      data-id="{{item.id}}"
    >
      <view class="card-content">
        <view class="equipment-image-section">
          <image
            class="equipment-image"
            src="{{item.image || 'http://iph.href.lu/240x180'}}"
            mode="aspectFill"
          />
          <view class="price-section">
            <view class="price-info">
              <text class="price-amount">¥{{item.price}}</text>
              <text class="price-unit">/{{item.unit}}</text>
            </view>
          </view>
        </view>

        <view class="equipment-info">
          <view class="equipment-header">
            <text class="equipment-name">{{item.name}}</text>
            <view class="equipment-status {{item.available ? 'available' : 'unavailable'}}">
              {{item.available ? '可租用' : '使用中'}}
            </view>
          </view>

          <text class="equipment-desc">{{item.description}}</text>

          <view class="equipment-meta">
            <view class="rating-info">
              <text class="rating-star">⭐</text>
              <text class="rating-text">{{item.rating}} ({{item.reviewCount}})</text>
            </view>
            <view class="availability-info">
              <text class="availability-icon">🕐</text>
              <text class="availability-text">{{item.availabilityText}}</text>
            </view>
          </view>

          <view class="button-section">
            <button
              class="rent-btn {{item.available ? '' : 'disabled'}}"
              disabled="{{!item.available}}"
              bindtap="rentEquipment"
              data-id="{{item.id}}"
              catchtap="stopPropagation"
            >
              {{item.available ? '查看详情' : '暂不可用'}}
            </button>
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 空状态 -->
  <view class="empty-state" wx:if="{{filteredEquipment.length === 0 && !loading}}">
    <text class="empty-icon">🚁</text>
    <text class="empty-title">暂无设备</text>
    <text class="empty-desc">当前筛选条件下没有找到设备</text>
    <button class="btn btn-primary" bindtap="resetFilter">重置筛选</button>
  </view>

  <!-- 加载状态 -->
  <view class="loading-state" wx:if="{{loading}}">
    <text class="loading-icon">⏳</text>
    <text class="loading-text">加载中...</text>
  </view>
</view>

<!-- 排序选项弹窗 -->
<view class="sort-modal" wx:if="{{showSort}}" bindtap="hideSortOptions">
  <view class="sort-modal-content" catchtap="stopPropagation">
    <view class="sort-header">
      <text class="sort-title">排序方式</text>
      <text class="sort-close" bindtap="hideSortOptions">✕</text>
    </view>
    
    <view class="sort-options">
      <view 
        class="sort-option {{currentSort === 'rating' ? 'active' : ''}}"
        bindtap="onSortChange"
        data-sort="rating"
      >
        <text class="sort-option-text">评分最高</text>
        <text class="sort-option-icon" wx:if="{{currentSort === 'rating'}}">✓</text>
      </view>
      
      <view 
        class="sort-option {{currentSort === 'price_low' ? 'active' : ''}}"
        bindtap="onSortChange"
        data-sort="price_low"
      >
        <text class="sort-option-text">价格从低到高</text>
        <text class="sort-option-icon" wx:if="{{currentSort === 'price_low'}}">✓</text>
      </view>
      
      <view 
        class="sort-option {{currentSort === 'price_high' ? 'active' : ''}}"
        bindtap="onSortChange"
        data-sort="price_high"
      >
        <text class="sort-option-text">价格从高到低</text>
        <text class="sort-option-icon" wx:if="{{currentSort === 'price_high'}}">✓</text>
      </view>
      
      <view 
        class="sort-option {{currentSort === 'popularity' ? 'active' : ''}}"
        bindtap="onSortChange"
        data-sort="popularity"
      >
        <text class="sort-option-text">最受欢迎</text>
        <text class="sort-option-icon" wx:if="{{currentSort === 'popularity'}}">✓</text>
      </view>
    </view>
  </view>
</view>