<!DOCTYPE html>
<html>
<head>
    <title>微信登录参数获取工具</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .container { max-width: 800px; margin: 0 auto; }
        .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .code-block { background: #f5f5f5; padding: 10px; border-radius: 3px; font-family: monospace; white-space: pre-wrap; }
        button { padding: 10px 20px; margin: 5px; background: #007cba; color: white; border: none; border-radius: 3px; cursor: pointer; }
        button:hover { background: #005a87; }
        input, textarea { width: 100%; padding: 8px; margin: 5px 0; border: 1px solid #ddd; border-radius: 3px; }
        textarea { height: 100px; }
        .highlight { background: #fff3cd; padding: 10px; border-radius: 3px; margin: 10px 0; }
    </style>
</head>
<body>
    <div class="container">
        <h1>微信登录参数获取工具</h1>
        
        <div class="highlight">
            <strong>说明：</strong>这个工具帮助你获取微信小程序登录所需的三个参数，用于后端接口测试。
        </div>

        <div class="section">
            <h2>方法1：从小程序控制台获取</h2>
            <p>1. 在微信开发者工具中打开你的小程序</p>
            <p>2. 点击登录按钮，在控制台查看日志</p>
            <p>3. 找到类似这样的日志：</p>
            <div class="code-block">getUserProfile成功: {
  code: "0d3cV5100lFKAU1ijt100xpneM1cV51J",
  encryptedData: "E2e0Roww59X+TUBKI11FWFgNPEACb67PnFFIZgERgNuIaAc5SJ...",
  iv: "Q+LJrAPAelaCn92r6H2WWw=="
}</div>
            <p>4. 复制这些参数到下面的表单中</p>
        </div>

        <div class="section">
            <h2>参数输入</h2>
            <label>Code (微信登录凭证):</label>
            <input type="text" id="code" placeholder="例如: 0d3cV5100lFKAU1ijt100xpneM1cV51J">
            
            <label>EncryptedData (加密用户数据):</label>
            <textarea id="encryptedData" placeholder="例如: E2e0Roww59X+TUBKI11FWFgNPEACb67PnFFIZgERgNuIaAc5SJ..."></textarea>
            
            <label>IV (加密算法初始向量):</label>
            <input type="text" id="iv" placeholder="例如: Q+LJrAPAelaCn92r6H2WWw==">
            
            <button onclick="generateCurl()">生成 cURL 命令</button>
            <button onclick="generatePostman()">生成 Postman JSON</button>
            <button onclick="useTestData()">使用测试数据</button>
        </div>

        <div class="section">
            <h2>生成的 cURL 命令</h2>
            <div class="code-block" id="curlOutput">点击上面的按钮生成 cURL 命令</div>
            <button onclick="copyCurl()">复制 cURL</button>
        </div>

        <div class="section">
            <h2>Postman 测试数据</h2>
            <div class="code-block" id="postmanOutput">点击上面的按钮生成 Postman 数据</div>
            <button onclick="copyPostman()">复制 Postman JSON</button>
        </div>

        <div class="section">
            <h2>预设测试数据</h2>
            <p>如果后端支持测试模式，可以使用以下预设数据：</p>
            <div class="code-block">curl -X POST http://volcanoes.cc:46783/api/auth/wechat/login \
  -H "Content-Type: application/json" \
  -d '{
    "code": "test_code_for_development",
    "encryptedData": "test_encrypted_data_string",
    "iv": "test_iv_vector_string"
  }'</div>
        </div>
    </div>

    <script>
        function generateCurl() {
            const code = document.getElementById('code').value || 'your_code_here';
            const encryptedData = document.getElementById('encryptedData').value || 'your_encrypted_data_here';
            const iv = document.getElementById('iv').value || 'your_iv_here';

            const curlCommand = `curl -X POST http://volcanoes.cc:46783/api/auth/wechat/login \\
  -H "Content-Type: application/json" \\
  -H "Accept: application/json" \\
  -d '{
    "code": "${code}",
    "encryptedData": "${encryptedData}",
    "iv": "${iv}"
  }'`;

            document.getElementById('curlOutput').textContent = curlCommand;
        }

        function generatePostman() {
            const code = document.getElementById('code').value || 'your_code_here';
            const encryptedData = document.getElementById('encryptedData').value || 'your_encrypted_data_here';
            const iv = document.getElementById('iv').value || 'your_iv_here';

            const postmanData = {
                "method": "POST",
                "url": "http://volcanoes.cc:46783/api/auth/wechat/login",
                "headers": {
                    "Content-Type": "application/json",
                    "Accept": "application/json"
                },
                "body": {
                    "code": code,
                    "encryptedData": encryptedData,
                    "iv": iv
                }
            };

            document.getElementById('postmanOutput').textContent = JSON.stringify(postmanData, null, 2);
        }

        function useTestData() {
            document.getElementById('code').value = 'test_code_for_development_' + Date.now();
            document.getElementById('encryptedData').value = 'test_encrypted_data_' + Math.random().toString(36).substring(7);
            document.getElementById('iv').value = 'test_iv_' + Math.random().toString(36).substring(7);
            
            generateCurl();
            generatePostman();
        }

        function copyCurl() {
            const curlText = document.getElementById('curlOutput').textContent;
            navigator.clipboard.writeText(curlText).then(() => {
                alert('cURL 命令已复制到剪贴板！');
            });
        }

        function copyPostman() {
            const postmanText = document.getElementById('postmanOutput').textContent;
            navigator.clipboard.writeText(postmanText).then(() => {
                alert('Postman JSON 已复制到剪贴板！');
            });
        }

        // 页面加载时生成示例
        window.onload = function() {
            useTestData();
        };
    </script>
</body>
</html>
