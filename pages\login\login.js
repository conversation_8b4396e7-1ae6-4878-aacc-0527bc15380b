const app = getApp()
const auth = require('../../utils/auth.js')
const { mockUser } = require('../../utils/mockData.js')

Page({
  data: {
    // 表单数据
    agreeTerms: true,

    // 按钮状态
    wxLoading: false,
    phoneLoading: false,

    // 全局加载
    isLoading: false,
    loadingText: '登录中...',

    // 登录状态标记
    isLoginInProgress: false
  },

  onLoad(options) {
    console.log('登录页面加载，参数：', options)
    
    // 检查是否已经登录
    if (auth.checkLoginStatus()) {
      console.log('用户已登录，跳转首页')
      this.redirectToHome()
      return
    }
    
    // 保存重定向参数
    if (options.redirect) {
      wx.setStorageSync('redirectUrl', decodeURIComponent(options.redirect))
    }
  },

  onShow() {
    // 每次显示页面时检查登录状态
    if (auth.checkLoginStatus()) {
      this.redirectToHome()
    }
  },

  /**
   * 微信登录处理
   */
  handleWechatLogin() {
    if (!this.data.agreeTerms) {
      wx.showToast({
        title: '请先同意用户协议',
        icon: 'none'
      })
      return
    }

    // 防止重复登录
    if (this.data.wxLoading || this.data.isLoginInProgress) {
      console.log('登录中，请勿重复点击')
      return
    }

    console.log('开始微信登录流程')
    this.setData({
      wxLoading: true,
      isLoginInProgress: true
    })

    // 直接在用户点击事件中获取用户信息
    if (wx.getUserProfile) {
      wx.getUserProfile({
        desc: '用于完善用户资料',
        success: (userRes) => {
          console.log('getUserProfile成功:', userRes)
          this.loginWithUserInfo(userRes)
        },
        fail: (error) => {
          console.error('getUserProfile失败:', error)
          if (error.errMsg && error.errMsg.includes('deny')) {
            this.handleLoginError(new Error('用户拒绝授权'))
          } else {
            // 如果getUserProfile失败，尝试只用code登录
            this.loginWithCodeOnly()
          }
        }
      })
    } else {
      // 兼容旧版本
      wx.getUserInfo({
        success: (userRes) => {
          console.log('getUserInfo成功:', userRes)
          this.loginWithUserInfo(userRes)
        },
        fail: (error) => {
          console.error('getUserInfo失败:', error)
          this.loginWithCodeOnly()
        }
      })
    }
  },

  /**
   * 使用用户信息登录
   */
  async loginWithUserInfo(userRes) {
    try {
      // 检查是否已在登录中
      if (this.data.isLoginInProgress) {
        console.log('登录已在进行中，跳过重复请求')
        return
      }

      // 获取微信登录code
      const loginRes = await this.getWechatCode()
      console.log('获取微信code成功:', loginRes.code)

      // 调用后端登录接口
      const request = require('../../utils/request.js')
      const loginData = {
        code: loginRes.code,
        encryptedData: userRes.encryptedData || '',
        iv: userRes.iv || ''
      }

      console.log('发送登录请求:', loginData)
      const response = await request.post('/api/auth/wechat/login', loginData)

      console.log('后端登录响应:', response)
      this.handleLoginSuccess(response, userRes.userInfo)

    } catch (error) {
      console.error('用户信息登录失败:', error)
      this.handleLoginError(error)
    }
  },

  /**
   * 仅使用code登录
   */
  async loginWithCodeOnly() {
    try {
      // 检查是否已在登录中
      if (this.data.isLoginInProgress) {
        console.log('登录已在进行中，跳过重复请求')
        return
      }

      console.log('使用code登录')
      // 获取微信登录code
      const loginRes = await this.getWechatCode()
      console.log('获取微信code成功:', loginRes.code)

      // 调用后端登录接口
      const request = require('../../utils/request.js')
      const loginData = {
        code: loginRes.code,
        encryptedData: '',
        iv: ''
      }

      console.log('发送code登录请求:', loginData)
      const response = await request.post('/api/auth/wechat/login', loginData)

      console.log('后端登录响应:', response)
      this.handleLoginSuccess(response, null)

    } catch (error) {
      console.error('code登录失败:', error)
      this.handleLoginError(error)
    }
  },

  /**
   * 获取微信登录code
   */
  getWechatCode() {
    return new Promise((resolve, reject) => {
      wx.login({
        success: resolve,
        fail: reject
      })
    })
  },

  /**
   * 处理登录成功
   */
  handleLoginSuccess(response, userInfo) {
    try {
      console.log('处理登录响应:', response)

      // 后端返回格式: {code: 0, message: string, data: {token: string, ...}}
      if (response && response.token) {
        // 直接从response中获取token（模拟数据格式）
        const userData = {
          id: response.userId || 'user_' + Date.now(),
          nickname: userInfo?.nickName || response.nickname || '微信用户',
          avatar: userInfo?.avatarUrl || response.avatar || '',
          phone: response.phone || '',
          balance: response.balance || 0,
          level: response.level || 'Normal',
          creditScore: response.creditScore || 0,
          token: response.token,
          registerTime: response.registerTime || new Date().toISOString(),
          loginType: userInfo ? 'wechat' : 'wechat_code_only',
          loginTime: new Date().toISOString()
        }

        // 保存登录信息
        auth.setUserInfo(userData)

        wx.showToast({
          title: '登录成功',
          icon: 'success',
          duration: 1500
        })

        setTimeout(() => {
          auth.handleLoginRedirect()
        }, 1500)

        // 重置登录状态
        this.setData({ isLoginInProgress: false })
        return // 登录成功，停止执行
      } else if (response && response.data && response.data.token) {
        // 真实后端返回格式: {code: 0, data: {token: string}}
        const backendData = response.data
        const userData = {
          id: backendData.userId || 'user_' + Date.now(),
          nickname: userInfo?.nickName || backendData.nickname || '微信用户',
          avatar: userInfo?.avatarUrl || backendData.avatar || '',
          phone: backendData.phone || '',
          balance: backendData.balance || 0,
          level: backendData.level || 'Normal',
          creditScore: backendData.creditScore || 0,
          token: backendData.token,
          registerTime: backendData.registerTime || new Date().toISOString(),
          loginType: userInfo ? 'wechat' : 'wechat_code_only',
          loginTime: new Date().toISOString()
        }

        // 保存登录信息
        auth.setUserInfo(userData)

        wx.showToast({
          title: '登录成功',
          icon: 'success',
          duration: 1500
        })

        setTimeout(() => {
          auth.handleLoginRedirect()
        }, 1500)

        // 重置登录状态
        this.setData({ isLoginInProgress: false })
        return // 登录成功，停止执行
      } else {
        console.error('登录响应格式异常:', response)
        throw new Error('登录响应数据异常')
      }
    } catch (error) {
      console.error('处理登录成功失败:', error)
      this.handleLoginError(error)
    } finally {
      this.setData({
        wxLoading: false,
        isLoginInProgress: false
      })
    }
  },

  /**
   * 处理登录错误
   */
  handleLoginError(error) {
    console.error('登录错误:', error)

    // 处理不同类型的错误
    let errorMessage = '登录失败，请重试'
    let showRetry = true

    if (error.message) {
      if (error.message.includes('用户拒绝') || error.message.includes('deny')) {
        errorMessage = '需要授权才能登录'
        showRetry = false
      } else if (error.message.includes('网络') || error.message.includes('network')) {
        errorMessage = '网络连接失败，请检查网络'
      } else if (error.message.includes('timeout')) {
        errorMessage = '登录超时，请重试'
      } else if (error.code === 401) {
        errorMessage = '登录凭证无效，请重试'
      } else if (error.code === 500) {
        errorMessage = '服务器错误，请稍后重试'
      } else {
        errorMessage = error.message
      }
    }

    if (showRetry) {
      wx.showModal({
        title: '登录失败',
        content: errorMessage,
        confirmText: '重试',
        cancelText: '取消',
        success: (res) => {
          if (res.confirm) {
            this.handleWechatLogin()
          }
        }
      })
    } else {
      wx.showToast({
        title: errorMessage,
        icon: 'none',
        duration: 2000
      })
    }

    // 重置登录状态
    this.setData({
      wxLoading: false,
      isLoginInProgress: false
    })
  },

  /**
   * 微信手机号授权登录处理
   */
  async handlePhoneAuthLogin(e) {
    console.log('手机号授权事件触发，详细信息：', e.detail)

    if (!this.data.agreeTerms) {
      wx.showToast({
        title: '请先同意用户协议',
        icon: 'none'
      })
      return
    }

    // 检查用户是否授权
    if (e.detail.errMsg !== 'getPhoneNumber:ok') {
      console.log('用户授权失败，错误信息：', e.detail.errMsg)

      // 处理不同类型的错误
      if (e.detail.errMsg === 'getPhoneNumber:fail no permission') {
        // 没有权限 - 小程序未认证或个人开发者
        wx.showModal({
          title: '功能限制',
          content: '手机号授权功能需要企业认证小程序才能使用。请使用微信授权登录。',
          confirmText: '确定',
          showCancel: false
        })
      } else if (e.detail.errMsg === 'getPhoneNumber:fail user deny') {
        // 用户拒绝授权
        wx.showModal({
          title: '授权提示',
          content: '需要手机号授权才能使用此功能。您可以使用微信授权登录。',
          confirmText: '确定',
          showCancel: false
        })
      } else if (e.detail.errMsg.includes('1400001')) {
        // 额度不足
        wx.showModal({
          title: '额度不足',
          content: '手机号验证次数已达上限。请使用微信授权登录。',
          confirmText: '确定',
          showCancel: false
        })
      } else {
        // 其他错误
        wx.showModal({
          title: '授权失败',
          content: `授权失败：${e.detail.errMsg}。请使用微信授权登录。`,
          confirmText: '确定',
          showCancel: false
        })
      }
      return
    }

    this.setData({ phoneLoading: true })

    try {
      console.log('开始手机号授权登录流程')

      // 获取微信登录code
      const loginResult = await auth.getWechatCode()
      console.log('微信登录code：', loginResult.code)

      // 获取手机号授权数据
      const { encryptedData, iv } = e.detail
      console.log('手机号授权数据获取成功')

      // 调用后端手机号登录接口（如果后端支持）
      // 这里可以扩展为专门的手机号登录接口
      const request = require('../../utils/request.js')
      const response = await request.post('/api/auth/wechat/login', {
        code: loginResult.code,
        encryptedData: encryptedData,
        iv: iv
      })

      if (response && response.token) {
        const userData = {
          id: response.userId || 'user_' + Date.now(),
          nickname: response.nickname || '手机用户',
          avatar: response.avatar || '',
          phone: response.phone || '',
          balance: response.balance || 0,
          level: response.level || 'Normal',
          creditScore: response.creditScore || 0,
          token: response.token,
          registerTime: response.registerTime || new Date().toISOString(),
          loginType: 'phone_auth',
          loginTime: new Date().toISOString()
        }

        // 保存登录信息
        auth.setUserInfo(userData)

        wx.showToast({
          title: '登录成功',
          icon: 'success',
          duration: 1500
        })

        setTimeout(() => {
          auth.handleLoginRedirect()
        }, 1500)
      } else {
        throw new Error('登录响应数据异常')
      }

    } catch (error) {
      console.error('手机号授权登录失败：', error)

      let errorMessage = '登录失败，请重试'
      if (error.message) {
        if (error.message.includes('网络')) {
          errorMessage = '网络连接失败，请检查网络'
        } else if (error.message.includes('timeout')) {
          errorMessage = '登录超时，请重试'
        } else {
          errorMessage = error.message
        }
      }

      wx.showToast({
        title: errorMessage,
        icon: 'error',
        duration: 2000
      })
    } finally {
      this.setData({ phoneLoading: false })
    }
  },





  /**
   * 检查网络连接状态
   */
  checkNetworkStatus() {
    return new Promise((resolve) => {
      wx.getNetworkType({
        success: (res) => {
          const networkType = res.networkType
          if (networkType === 'none') {
            wx.showModal({
              title: '网络连接失败',
              content: '请检查网络连接后重试',
              showCancel: false,
              confirmText: '我知道了'
            })
            resolve(false)
          } else {
            resolve(true)
          }
        },
        fail: () => {
          resolve(true) // 获取网络状态失败时，假设网络正常
        }
      })
    })
  },

  /**
   * 显示登录错误处理选项
   */
  showErrorOptions(error) {
    const errorMessage = error.message || '登录失败'

    wx.showModal({
      title: '登录失败',
      content: `${errorMessage}\n\n请选择处理方式：`,
      confirmText: '重试',
      cancelText: '取消',
      success: (res) => {
        if (res.confirm) {
          // 用户选择重试
          this.handleWechatLogin()
        }
      }
    })
  },

  /**
   * 检查登录环境
   */
  async checkLoginEnvironment() {
    try {
      // 检查网络状态
      const networkOk = await this.checkNetworkStatus()
      if (!networkOk) {
        return false
      }

      // 检查微信版本
      const systemInfo = wx.getAppBaseInfo()
      console.log('应用信息:', systemInfo)

      // 检查是否支持必要的API
      if (!wx.login) {
        wx.showModal({
          title: '版本过低',
          content: '当前微信版本过低，请升级微信后重试',
          showCancel: false
        })
        return false
      }

      return true
    } catch (error) {
      console.error('检查登录环境失败:', error)
      return true // 检查失败时允许继续
    }
  },

  /**
   * 跳转到首页
   */
  redirectToHome() {
    wx.switchTab({
      url: '/pages/home/<USER>'
    })
  },

  /**
   * 表单输入处理
   */
  onAgreeChange(e) {
    this.setData({
      agreeTerms: e.detail.value.length > 0
    })
  },

  /**
   * 查看协议
   */
  viewTerms() {
    wx.showModal({
      title: '用户协议',
      content: '这里是用户协议的具体内容...',
      showCancel: false,
      confirmText: '我知道了'
    })
  },

  viewPrivacy() {
    wx.showModal({
      title: '隐私政策',
      content: '这里是隐私政策的具体内容...',
      showCancel: false,
      confirmText: '我知道了'
    })
  },


}) 