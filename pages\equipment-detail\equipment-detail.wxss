/* pages/equipment-detail/equipment-detail.wxss */

.detail-container {
  background-color: #f9fafb;
  min-height: 100vh;
  padding-bottom: 160rpx;
}

/* 设备图片区域 */
.image-section {
  position: relative;
  height: 640rpx;
}

.equipment-image {
  width: 100%;
  height: 100%;
}

.back-btn {
  position: absolute;
  top: 32rpx;
  left: 32rpx;
  width: 80rpx;
  height: 80rpx;
  background: rgba(0, 0, 0, 0.5);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.back-icon {
  font-size: 32rpx;
  color: #ffffff;
  font-weight: bold;
}

.favorite-btn {
  position: absolute;
  top: 32rpx;
  right: 32rpx;
  width: 80rpx;
  height: 80rpx;
  background: rgba(0, 0, 0, 0.5);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.favorite-icon {
  font-size: 32rpx;
  color: #ffffff;
}

.status-badge {
  position: absolute;
  bottom: 32rpx;
  left: 32rpx;
  padding: 12rpx 24rpx;
  border-radius: 24rpx;
  font-size: 28rpx;
  font-weight: 500;
}

.status-badge.available {
  background: #10b981;
  color: #ffffff;
}

.status-badge.unavailable {
  background: #f59e0b;
  color: #ffffff;
}

/* 设备信息区域 */
.info-section {
  background: #ffffff;
  border-radius: 48rpx 48rpx 0 0;
  margin-top: -48rpx;
  position: relative;
  z-index: 10;
  padding: 48rpx;
}

.basic-info {
  margin-bottom: 48rpx;
}

.equipment-name {
  font-size: 48rpx;
  font-weight: bold;
  color: #1f2937;
  margin-bottom: 16rpx;
  line-height: 1.2;
}

.equipment-desc {
  font-size: 32rpx;
  color: #6b7280;
  margin-bottom: 32rpx;
  line-height: 1.4;
}

.rating-price-row {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.rating-section {
  display: flex;
  align-items: center;
  gap: 32rpx;
}

.stars {
  display: flex;
  gap: 4rpx;
}

.star {
  font-size: 28rpx;
  color: #fbbf24;
}

.rating-text {
  font-size: 28rpx;
  color: #6b7280;
}

.price-amount {
  font-size: 60rpx;
  font-weight: bold;
  color: #1f2937;
}

.price-unit {
  font-size: 36rpx;
  color: #6b7280;
}

.price-section {
  display: flex;
  align-items: center;
  gap: 16rpx;
}

.price-current {
  font-size: 48rpx;
  color: #10a37f;
  font-weight: 700;
}

.price-original {
  font-size: 32rpx;
  color: #9ca3af;
  text-decoration: line-through;
}

/* 特色功能 */
.features-section {
  background: #ffffff;
  padding: 32rpx;
  margin-bottom: 16rpx;
}

.section-title {
  display: block;
  font-size: 36rpx;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 32rpx;
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16rpx;
}

.feature-item {
  background: #f0f9ff;
  border: 2rpx solid #e0f2fe;
  border-radius: 16rpx;
  padding: 24rpx;
  text-align: center;
}

.feature-text {
  font-size: 28rpx;
  color: #0369a1;
  font-weight: 500;
}

/* 技术规格 */
.specs-section {
  background: #ffffff;
  padding: 32rpx;
  margin-bottom: 16rpx;
}

.specs-list {
  display: flex;
  flex-direction: column;
  gap: 24rpx;
}

.spec-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24rpx 0;
  border-bottom: 2rpx solid #f3f4f6;
}

.spec-item:last-child {
  border-bottom: none;
}

.spec-label {
  font-size: 32rpx;
  color: #6b7280;
}

.spec-value {
  font-size: 32rpx;
  color: #1f2937;
  font-weight: 500;
}

/* 详细描述 */
.description-section {
  background: #ffffff;
  padding: 32rpx;
  margin-bottom: 16rpx;
}

.description-text {
  font-size: 32rpx;
  color: #4b5563;
  line-height: 1.6;
}

/* 租赁须知 */
.notice-section {
  background: #ffffff;
  padding: 32rpx;
  margin-bottom: 16rpx;
}

.notice-list {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.notice-item {
  font-size: 28rpx;
  color: #6b7280;
  line-height: 1.5;
  padding-left: 24rpx;
  position: relative;
}

.notice-item::before {
  content: '•';
  position: absolute;
  left: 0;
  color: #10a37f;
  font-weight: bold;
}

/* 底部操作栏 */
.bottom-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: #ffffff;
  padding: 32rpx 48rpx;
  padding-bottom: calc(32rpx + env(safe-area-inset-bottom));
  border-top: 2rpx solid #f3f4f6;
  box-shadow: 0 -8rpx 16rpx rgba(0, 0, 0, 0.1);
  z-index: 100;
}

.contact-section {
  flex-shrink: 0;
}

.contact-btn {
  width: 120rpx;
  height: 96rpx;
  background: #f3f4f6;
  border: none;
  border-radius: 24rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 8rpx;
  transition: all 0.2s ease;
}

.contact-btn:hover {
  background: #e5e7eb;
  transform: scale(1.05);
}

.contact-icon {
  font-size: 32rpx;
}

.contact-text {
  font-size: 24rpx;
  color: #6b7280;
  font-weight: 500;
}

.action-section {
  flex: 1;
  display: flex;
  gap: 16rpx;
}

.action-btn {
  flex: 1;
  height: 96rpx;
  border-radius: 24rpx;
  font-size: 32rpx;
  font-weight: 600;
  border: none;
  transition: all 0.2s ease;
  cursor: pointer;
}

.action-btn.secondary {
  background: #f3f4f6;
  color: #6b7280;
}

.action-btn.secondary:hover {
  background: #e5e7eb;
  color: #1f2937;
}

.action-btn.primary {
  background: #10a37f;
  color: #ffffff;
}

.action-btn.primary:hover {
  background: #059669;
  transform: translateY(-2rpx);
}

/* 新的高保真样式 */
.feature-card {
  background: #f9fafb;
  border-radius: 24rpx;
  padding: 24rpx;
  text-align: center;
}

.feature-icon {
  font-size: 40rpx;
  margin-bottom: 16rpx;
  display: block;
}

.feature-title {
  font-size: 28rpx;
  font-weight: 500;
  color: #1f2937;
  margin-bottom: 8rpx;
  display: block;
}

.feature-subtitle {
  font-size: 24rpx;
  color: #6b7280;
  display: block;
}

.accessories-section {
  margin-bottom: 48rpx;
}

.accessories-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16rpx;
}

.accessory-item {
  display: flex;
  align-items: center;
  gap: 16rpx;
}

.check-icon {
  font-size: 24rpx;
  color: #10b981;
}

.accessory-text {
  font-size: 28rpx;
  color: #6b7280;
}

.reviews-section {
  margin-bottom: 48rpx;
}

.review-card {
  background: #f9fafb;
  border-radius: 24rpx;
  padding: 32rpx;
}

.review-header {
  display: flex;
  align-items: center;
  gap: 24rpx;
  margin-bottom: 16rpx;
}

.user-avatar {
  width: 64rpx;
  height: 64rpx;
  background: #d1d5db;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.avatar-icon {
  font-size: 28rpx;
  color: #6b7280;
}

.user-name {
  font-size: 28rpx;
  font-weight: 500;
  color: #1f2937;
  margin-bottom: 8rpx;
  display: block;
}

.review-stars {
  display: flex;
  gap: 4rpx;
}

.review-text {
  font-size: 28rpx;
  color: #6b7280;
  line-height: 1.5;
}

.bottom-actions {
  display: flex;
  gap: 24rpx;
  width: 100%;
}

.favorite-action-btn {
  width: 160rpx;
  height: 88rpx;
  background: #f3f4f6;
  color: #6b7280;
  padding: 0;
  border-radius: 20rpx;
  font-size: 32rpx;
  font-weight: 500;
  border: none;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  gap: 12rpx;
  flex-shrink: 0;
}

.rent-action-btn {
  flex: 1;
  height: 88rpx;
  background: linear-gradient(135deg, #1f2937 0%, #374151 100%);
  color: #ffffff;
  padding: 0 32rpx;
  border-radius: 20rpx;
  font-size: 32rpx;
  font-weight: 500;
  border: none;
  transition: all 0.3s ease;
  min-width: 0;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  display: flex;
  align-items: center;
  justify-content: center;
}

.rent-action-btn.disabled {
  background: #d1d5db;
  color: #9ca3af;
}

.action-icon {
  font-size: 28rpx;
}

.action-text {
  font-size: 32rpx;
}

.action-btn[disabled] {
  opacity: 0.5;
  cursor: not-allowed;
  background: #f3f4f6;
  color: #9ca3af;
}

.action-btn[disabled]:hover {
  transform: none;
  background: #f3f4f6;
}