/**
 * 逍遥境小程序测试数据模块
 * 提供开发阶段使用的完整模拟数据
 */

// 用户测试数据
const mockUser = {
  id: 'test_user_001',
  nickname: '逍遥测试用户',
  avatar: 'icon-user',
  phone: '138****8888',
  balance: 299.50,
  level: 'VIP',
  registerTime: '2024-01-15',
  totalFlightTime: 120, // 总飞行时间(分钟)
  totalOrders: 25,
  creditScore: 850
}

// 设备测试数据
const mockEquipment = [
  {
    id: 'dji_air3',
    name: 'DJI Air 3',
    brand: 'DJI',
    model: 'Air 3',
    price: 80,
    unit: '小时',
    originalPrice: 100,
    features: ['4K双摄', '46分钟续航', '全向避障', '智能跟随'],
    icon: 'icon-drone',
    available: true,
    battery: 95,
    location: '设备点A',
    rating: 4.9,
    reviewCount: 127,
    availabilityText: '今日可租',
    description: '双主摄 · 4K/60fps HDR · 46分钟续航 · 障碍物感知',
    image: 'http://iph.href.lu/192x160',
    specs: {
      weight: '720g',
      maxFlightTime: '46分钟',
      maxSpeed: '21m/s',
      cameraResolution: '4K/60fps',
      transmission: '20公里',
      obstacle: '全向避障'
    }
  },
  {
    id: 'dji_mini4',
    name: 'DJI Mini 4 Pro',
    brand: 'DJI',
    model: 'Mini 4 Pro',
    price: 60,
    unit: '小时',
    originalPrice: 80,
    features: ['4K/60fps', '34分钟续航', '轻至249g', '智能模式'],
    icon: 'icon-drone',
    available: true,
    battery: 88,
    location: '设备点B',
    rating: 4.8,
    reviewCount: 89,
    availabilityText: '今日可租',
    description: '4K HDR · 249g轻量 · 34分钟续航 · 智能跟随',
    image: 'http://iph.href.lu/192x160',
    specs: {
      weight: '249g',
      maxFlightTime: '34分钟',
      maxSpeed: '16m/s',
      cameraResolution: '4K/60fps',
      transmission: '20公里',
      obstacle: '三向避障'
    }
  },
  {
    id: 'dji_mavic3',
    name: 'DJI Mavic 3',
    brand: 'DJI',
    model: 'Mavic 3',
    price: 120,
    unit: '小时',
    originalPrice: 150,
    features: ['哈苏相机', '46分钟续航', '5.1K视频', '15公里图传'],
    icon: 'icon-drone',
    available: false,
    battery: 0,
    location: '设备点C',
    rating: 4.9,
    reviewCount: 203,
    availabilityText: '预计17:30可用',
    description: '哈苏相机 · 5.1K视频 · 46分钟续航 · 专业航拍',
    image: 'http://iph.href.lu/192x160',
    specs: {
      weight: '895g',
      maxFlightTime: '46分钟',
      maxSpeed: '19m/s',
      cameraResolution: '5.1K/50fps',
      transmission: '15公里',
      obstacle: '全向避障'
    }
  },
  {
    id: 'dji_phantom4',
    name: 'DJI Phantom 4 Pro',
    brand: 'DJI',
    model: 'Phantom 4 Pro',
    price: 100,
    unit: '小时',
    originalPrice: 120,
    features: ['1英寸CMOS', '4K视频', '30分钟续航', '机械云台'],
    icon: 'icon-drone',
    available: true,
    battery: 75,
    location: '设备点D',
    rating: 4.7,
    reviewCount: 156,
    availabilityText: '今日可租',
    description: '1英寸CMOS · 4K视频 · 30分钟续航 · 机械云台',
    image: 'http://iph.href.lu/192x160',
    specs: {
      weight: '1388g',
      maxFlightTime: '30分钟',
      maxSpeed: '20m/s',
      cameraResolution: '4K/60fps',
      transmission: '7公里',
      obstacle: '五向避障'
    }
  }
]

// 地点测试数据
const mockLocations = [
  {
    id: 'shusheng_01',
    name: '书圣故里',
    address: '绍兴市越城区蕺山街道',
    distance: 2.3,
    rating: 4.9,
    reviewCount: 328,
    price: 20,
    unit: '次',
    features: ['古典园林', '适合航拍', '白天开放', '免费停车'],
    latitude: 30.0041,
    longitude: 120.5804,
    openTime: '08:00-17:30',
    difficulty: '简单',
    recommended: true,
    images: ['http://iph.href.lu/128x128', 'http://iph.href.lu/128x128', 'http://iph.href.lu/128x128'],
    description: '书圣故里历史文化街区，古色古香的建筑群，是航拍古建筑的绝佳地点。'
  },
  {
    id: 'keyan_01',
    name: '柯岩风景区',
    address: '绍兴市柯桥区柯岩大道',
    distance: 8.7,
    rating: 4.8,
    reviewCount: 256,
    price: 30,
    unit: '次',
    features: ['奇石景观', '湖光山色', '全天开放', '收费停车'],
    latitude: 30.0234,
    longitude: 120.4567,
    openTime: '08:00-16:30',
    difficulty: '中等',
    recommended: true,
    images: ['http://iph.href.lu/128x128', 'http://iph.href.lu/128x128'],
    description: '柯岩风景区以石景文化著称，奇石、古桥、湖泊交相辉映，是拍摄自然风光的理想场所。'
  },
  {
    id: 'wuzhen_01',
    name: '乌镇西栅',
    address: '嘉兴市桐乡市乌镇镇',
    distance: 45.2,
    rating: 4.9,
    reviewCount: 567,
    price: 50,
    unit: '次',
    features: ['水乡古镇', '夜景迷人', '需要门票', '导游服务'],
    latitude: 30.7456,
    longitude: 120.4891,
    openTime: '09:00-22:00',
    difficulty: '困难',
    recommended: false,
    images: ['http://iph.href.lu/128x128', 'http://iph.href.lu/128x128', 'http://iph.href.lu/128x128', 'http://iph.href.lu/128x128'],
    description: '乌镇西栅保持着古朴的水乡风貌，夜晚灯火辉煌，是拍摄江南水乡最美的地方。'
  },
  {
    id: 'donghu_01',
    name: '东湖风景区',
    address: '绍兴市越城区东湖街道',
    distance: 12.1,
    rating: 4.7,
    reviewCount: 198,
    price: 25,
    unit: '次',
    features: ['山水结合', '溶洞奇观', '游船服务', '免费WIFI'],
    latitude: 30.0123,
    longitude: 120.6234,
    openTime: '08:30-16:30',
    difficulty: '简单',
    recommended: true,
    images: ['http://iph.href.lu/128x128', 'http://iph.href.lu/128x128'],
    description: '东湖风景区山清水秀，溶洞景观独特，乘船游湖别有一番情趣。'
  }
]

// 订单测试数据
const mockOrders = [
  {
    id: 'order_001',
    equipmentId: 'dji_air3',
    equipmentName: 'DJI Air 3',
    locationId: 'shusheng_01',
    locationName: '书圣故里',
    userId: 'test_user_001',
    status: 'active', // pending, paid, active, completed, cancelled
    statusText: '使用中',
    orderTime: '2024-01-27 09:30:00',
    startTime: '2024-01-27 10:00:00',
    endTime: '2024-01-27 12:00:00',
    duration: 2,
    price: 80,
    totalAmount: 160,
    payment: {
      method: 'balance',
      methodText: '余额支付',
      transactionId: 'tx_001'
    },
    equipment: {
      icon: 'icon-drone',
      battery: 95,
      specs: ['4K双摄', '46分钟续航']
    },
    location: {
      address: '绍兴市越城区蕺山街道',
      distance: 2.3
    },
    canControl: true,
    canCancel: false,
    canReview: false
  },
  {
    id: 'order_002',
    equipmentId: 'dji_mini4',
    equipmentName: 'DJI Mini 4 Pro',
    locationId: 'keyan_01',
    locationName: '柯岩风景区',
    userId: 'test_user_001',
    status: 'completed',
    statusText: '已完成',
    orderTime: '2024-01-26 14:20:00',
    startTime: '2024-01-26 15:00:00',
    endTime: '2024-01-26 17:00:00',
    duration: 2,
    price: 60,
    totalAmount: 120,
    payment: {
      method: 'wechat',
      methodText: '微信支付',
      transactionId: 'tx_002'
    },
    equipment: {
      icon: 'icon-drone',
      battery: 88,
      specs: ['4K/60fps', '轻至249g']
    },
    location: {
      address: '绍兴市柯桥区柯岩大道',
      distance: 8.7
    },
    canControl: false,
    canCancel: false,
    canReview: true,
    review: {
      rating: 5,
      comment: '设备状态很好，航拍效果超棒！',
      time: '2024-01-26 17:30:00'
    }
  },
  {
    id: 'order_003',
    equipmentId: 'dji_mavic3',
    equipmentName: 'DJI Mavic 3',
    locationId: 'donghu_01',
    locationName: '东湖风景区',
    userId: 'test_user_001',
    status: 'pending',
    statusText: '待支付',
    orderTime: '2024-01-27 16:45:00',
    startTime: '2024-01-28 09:00:00',
    endTime: '2024-01-28 11:00:00',
    duration: 2,
    price: 120,
    totalAmount: 240,
    payment: null,
    equipment: {
      icon: 'icon-drone',
      battery: 0,
      specs: ['哈苏相机', '5.1K视频']
    },
    location: {
      address: '绍兴市越城区东湖街道',
      distance: 12.1
    },
    canControl: false,
    canCancel: true,
    canReview: false
  }
]

// 作品集测试数据
const mockGallery = [
  {
    id: 'work_001',
    title: '书圣故里晨光',
    type: 'image',
    url: 'http://iph.href.lu/400x300',
    thumbnail: 'http://iph.href.lu/200x150',
    createTime: '2024-01-26 08:30:00',
    equipmentId: 'dji_mini4',
    equipmentName: 'DJI Mini 4 Pro',
    locationId: 'shusheng_01',
    locationName: '书圣故里',
    size: '4.2MB',
    resolution: '4000x3000',
    tags: ['古建筑', '晨光', '航拍'],
    likes: 128,
    shares: 23,
    description: '清晨的书圣故里，古典建筑在晨光中格外美丽'
  },
  {
    id: 'work_002',
    title: '柯岩奇石全景',
    type: 'video',
    url: 'icon-video',
    thumbnail: 'http://iph.href.lu/200x150',
    createTime: '2024-01-25 15:20:00',
    equipmentId: 'dji_air3',
    equipmentName: 'DJI Air 3',
    locationId: 'keyan_01',
    locationName: '柯岩风景区',
    size: '156MB',
    duration: '02:34',
    resolution: '4K',
    tags: ['自然风光', '奇石', '4K视频'],
    likes: 89,
    shares: 12,
    description: '柯岩风景区的奇石景观，大自然的鬼斧神工'
  },
  {
    id: 'work_003',
    title: '东湖夕阳',
    type: 'image',
    url: 'http://iph.href.lu/400x300',
    thumbnail: 'http://iph.href.lu/200x150',
    createTime: '2024-01-24 17:45:00',
    equipmentId: 'dji_mavic3',
    equipmentName: 'DJI Mavic 3',
    locationId: 'donghu_01',
    locationName: '东湖风景区',
    size: '8.1MB',
    resolution: '5120x3840',
    tags: ['夕阳', '湖景', '5K拍摄'],
    likes: 256,
    shares: 45,
    description: '东湖的夕阳西下，湖光山色尽收眼底'
  }
]

// 充值套餐数据
const mockRechargePackages = [
  {
    id: 'package_01',
    amount: 50,
    bonus: 0,
    total: 50,
    popular: false,
    description: '体验套餐'
  },
  {
    id: 'package_02',
    amount: 100,
    bonus: 10,
    total: 110,
    popular: false,
    description: '基础套餐'
  },
  {
    id: 'package_03',
    amount: 200,
    bonus: 30,
    total: 230,
    popular: true,
    description: '推荐套餐'
  },
  {
    id: 'package_04',
    amount: 500,
    bonus: 100,
    total: 600,
    popular: false,
    description: '超值套餐'
  },
  {
    id: 'package_05',
    amount: 1000,
    bonus: 250,
    total: 1250,
    popular: false,
    description: 'VIP套餐'
  }
]

// 充值记录数据
const mockRechargeHistory = [
  {
    id: 'recharge_001',
    amount: 200,
    bonus: 30,
    total: 230,
    method: 'wechat',
    methodText: '微信支付',
    status: 'success',
    statusText: '成功',
    time: '2024-01-26 14:30:00',
    transactionId: 'wx_tx_001'
  },
  {
    id: 'recharge_002',
    amount: 100,
    bonus: 10,
    total: 110,
    method: 'alipay',
    methodText: '支付宝',
    status: 'success',
    statusText: '成功',
    time: '2024-01-20 09:15:00',
    transactionId: 'ali_tx_002'
  }
]

// 系统配置数据
const mockConfig = {
  app: {
    name: '逍遥境',
    version: '1.0.0',
    description: '专业无人机租赁平台'
  },
  service: {
    phone: '************',
    email: '<EMAIL>',
    workTime: '周一至周日 8:00-20:00'
  },
  rules: {
    minAge: 18,
    maxFlightTime: 8,
    refundTime: 24,
    cancelTime: 2
  }
}

module.exports = {
  mockUser,
  mockEquipment,
  mockLocations,
  mockOrders,
  mockGallery,
  mockRechargePackages,
  mockRechargeHistory,
  mockConfig
}