/* pages/profile/profile.wxss */

.profile-container {
  background-color: #f9fafb;
  min-height: 100vh;
  padding-bottom: 32rpx;
}

/* 用户信息卡片 */
.user-info-card {
  background: linear-gradient(135deg,
    rgba(31, 41, 55, 0.85) 0%,
    rgba(55, 65, 81, 0.85) 100%
  );
  backdrop-filter: blur(20rpx);
  margin: 32rpx;
  border-radius: 28rpx;
  padding: 40rpx;
  color: #ffffff;
  border: 1rpx solid rgba(255, 255, 255, 0.1);
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.12);
  position: relative;
  overflow: hidden;
}

.user-info-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1rpx;
  background: linear-gradient(90deg,
    rgba(255, 255, 255, 0) 0%,
    rgba(255, 255, 255, 0.3) 50%,
    rgba(255, 255, 255, 0) 100%
  );
}

.user-content {
  display: flex;
  align-items: center;
  gap: 28rpx;
  position: relative;
}

.user-avatar {
  width: 100rpx;
  height: 100rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  transition: all 0.3s ease;
}

.user-avatar:active {
  transform: scale(0.95);
}

.avatar-icon {
  font-size: 64rpx;
  opacity: 0.9;
}

.user-details {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 6rpx;
}

.user-name {
  font-size: 36rpx;
  font-weight: 600;
  color: #ffffff;
  line-height: 1.2;
  letter-spacing: 0.5rpx;
}

.user-id {
  font-size: 26rpx;
  opacity: 0.7;
  color: #ffffff;
  line-height: 1.2;
  font-weight: 300;
}

.user-badges {
  display: flex;
  align-items: center;
  gap: 12rpx;
  margin-top: 6rpx;
}

.level-badge {
  background: rgba(245, 158, 11, 0.2);
  backdrop-filter: blur(10rpx);
  border: 1rpx solid rgba(245, 158, 11, 0.3);
  padding: 4rpx 16rpx;
  border-radius: 20rpx;
  font-size: 22rpx;
  font-weight: 400;
  display: flex;
  align-items: center;
  gap: 6rpx;
  color: #fbbf24;
  transition: all 0.3s ease;
}

.level-badge:active {
  background: rgba(245, 158, 11, 0.3);
  transform: scale(0.98);
}

.crown-icon {
  font-size: 18rpx;
}

.flight-time {
  font-size: 22rpx;
  opacity: 0.6;
  color: #ffffff;
  font-weight: 300;
}

.edit-icon-container {
  flex-shrink: 0;
  width: 80rpx;
  height: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.edit-icon-container:active {
  transform: scale(0.9);
}

.edit-icon {
  font-size: 64rpx;
  color: #ffffff;
  opacity: 0.8;
}

/* 余额卡片 */
.balance-card {
  background: linear-gradient(135deg,
    rgba(68, 71, 70, 0.85) 0%,
    rgba(80, 100, 94, 0.85) 100%
  );
  backdrop-filter: blur(20rpx);
  margin: 32rpx;
  border-radius: 28rpx;
  padding: 36rpx;
  color: #ffffff;
  position: relative;
  border: 1rpx solid rgba(255, 255, 255, 0.1);
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.12);
  overflow: hidden;
}

.balance-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1rpx;
  background: linear-gradient(90deg,
    rgba(255, 255, 255, 0) 0%,
    rgba(255, 255, 255, 0.3) 50%,
    rgba(255, 255, 255, 0) 100%
  );
}

.balance-content {
  margin-bottom: 28rpx;
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
}

.balance-info {
  flex: 1;
}

.balance-title {
  font-size: 32rpx;
  font-weight: 500;
  margin-bottom: 8rpx;
  display: block;
  opacity: 0.9;
  letter-spacing: 0.5rpx;
}

.balance-amount {
  font-size: 64rpx;
  font-weight: 600;
  margin-bottom: 6rpx;
  display: block;
  letter-spacing: -1rpx;
  line-height: 1.1;
}

.balance-desc {
  font-size: 26rpx;
  opacity: 0.7;
  display: block;
  font-weight: 300;
}

.wallet-icon {
  flex-shrink: 0;
  width: 80rpx;
  height: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.wallet-icon:active {
  transform: scale(0.9);
}

.wallet-emoji {
  font-size: 64rpx;
  opacity: 0.8;
}

.balance-actions {
  display: flex;
  gap: 16rpx;
}

.balance-btn {
  flex: 1;
  background: rgba(255, 255, 255, 0.15);
  backdrop-filter: blur(10rpx);
  border: 1rpx solid rgba(255, 255, 255, 0.2);
  border-radius: 20rpx;
  padding: 14rpx 0;
  color: #ffffff;
  font-size: 26rpx;
  font-weight: 400;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 6rpx;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.balance-btn:active {
  background: rgba(255, 255, 255, 0.25);
  transform: scale(0.98);
}

.balance-btn.secondary {
  flex: 0 0 auto;
  padding: 14rpx 24rpx;
}

.btn-icon {
  font-size: 22rpx;
  opacity: 0.9;
}

/* 统计信息 */
.stats-section {
  margin: 0 32rpx 32rpx;
  background: rgba(255, 255, 255, 0.7);
  backdrop-filter: blur(20rpx);
  border-radius: 28rpx;
  padding: 24rpx;
  border: 1rpx solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.04);
}

.stats-grid {
  display: flex;
  gap: 20rpx;
}

.stats-item {
  flex: 1;
  background: rgba(255, 255, 255, 0.6);
  backdrop-filter: blur(10rpx);
  border-radius: 20rpx;
  padding: 24rpx 16rpx;
  text-align: center;
  border: 1rpx solid rgba(255, 255, 255, 0.3);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.stats-item::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg,
    rgba(255, 255, 255, 0.1) 0%,
    rgba(255, 255, 255, 0.05) 100%
  );
  opacity: 0;
  transition: opacity 0.4s ease;
}

.stats-item:active {
  transform: scale(0.98);
  background: rgba(255, 255, 255, 0.8);
}

.stats-item:active::before {
  opacity: 1;
}

.stats-number {
  font-size: 40rpx;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 6rpx;
  display: block;
  line-height: 1.1;
  letter-spacing: -0.5rpx;
}

.stats-label {
  font-size: 22rpx;
  color: #64748b;
  font-weight: 400;
  line-height: 1.2;
  opacity: 0.8;
}

/* 服务和设置菜单 */
.service-section,
.settings-section {
  background: #ffffff;
  margin: 32rpx;
  border-radius: 32rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.04);
}

.section-header {
  padding: 32rpx 32rpx 0;
  border-bottom: 1rpx solid #f3f4f6;
}

.section-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #1f2937;
}

.menu-list {
  padding: 8rpx 0;
}

.menu-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 32rpx;
  transition: all 0.3s ease;
}

.menu-item:active {
  background: #f9fafb;
  transform: translateX(8rpx);
}

.menu-left {
  display: flex;
  align-items: center;
  gap: 24rpx;
}

.menu-icon-wrapper {
  width: 80rpx;
  height: 80rpx;
  border-radius: 24rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.menu-icon-wrapper.blue {
  background: #dbeafe;
}

.menu-icon-wrapper.red {
  background: #fee2e2;
}

.menu-icon-wrapper.yellow {
  background: #fef3c7;
}

.menu-icon-wrapper.purple {
  background: #f3e8ff;
}

.menu-icon-wrapper.gray {
  background: #f3f4f6;
}

.menu-icon-wrapper.green {
  background: #dcfce7;
}

.menu-icon-wrapper.indigo {
  background: #e0e7ff;
}

.menu-icon-wrapper.orange {
  background: #fed7aa;
}

.menu-icon-wrapper.teal {
  background: #ccfbf1;
}

.menu-icon {
  font-size: 32rpx;
}

.menu-text {
  font-size: 32rpx;
  font-weight: 500;
  color: #1f2937;
}

.menu-right {
  display: flex;
  align-items: center;
  gap: 16rpx;
}

.menu-desc {
  font-size: 28rpx;
  color: #6b7280;
}

.menu-arrow {
  font-size: 32rpx;
  color: #9ca3af;
}

/* 退出登录按钮 */
.logout-section {
  margin: 32rpx;
  padding-bottom: 32rpx;
}

.logout-btn {
  width: 100%;
  background: #ffffff;
  border: 2rpx solid #ef4444;
  border-radius: 32rpx;
  padding: 32rpx 0;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  box-shadow: 0 2rpx 8rpx rgba(239, 68, 68, 0.1);
}

.logout-btn:active {
  background: #ef4444;
  transform: translateY(2rpx);
  box-shadow: 0 4rpx 12rpx rgba(239, 68, 68, 0.2);
}

.logout-btn:active .logout-text {
  color: #ffffff;
}

.logout-text {
  font-size: 32rpx;
  font-weight: 600;
  color: #ef4444;
  transition: color 0.3s ease;
}