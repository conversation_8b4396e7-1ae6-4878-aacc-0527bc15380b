// pages/gallery/gallery.js
const app = getApp()
const auth = require('../../utils/auth.js')
const galleryManager = require('../../utils/galleryManager.js')

Page({

  /**
   * 页面的初始数据
   */
  data: {
    // 作品数据
    allWorks: [],
    filteredWorks: [],

    // 统计数据 - 将在loadGalleryData中动态计算
    galleryStats: {
      totalWorks: 0,
      photoCount: 0,
      videoCount: 0,
      shootingDays: 0,
      storageSize: '0MB',
      shareCount: 0
    },

    // 筛选状态
    currentCategory: 'all',

    // 页面状态
    loading: true,
    selectMode: false,
    showModal: false,
    currentMedia: {}
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    console.log('作品集页面加载', options)

    // 保存订单ID用于筛选
    if (options.orderId) {
      this.setData({
        filterOrderId: options.orderId,
        currentCategory: 'order' // 设置为订单筛选模式
      })
    }

    this.checkAuth()
    this.loadGalleryData()
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    this.checkAuth()
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {

  },

  /**
   * 检查认证状态
   */
  checkAuth() {
    if (!auth.checkLoginStatus()) {
      auth.redirectToLogin('/pages/gallery/gallery')
      return false
    }
    return true
  },

  /**
   * 加载作品集数据
   */
  async loadGalleryData() {
    if (!this.checkAuth()) return

    this.setData({ loading: true })

    try {
      // 获取用户作品集数据
      let works = galleryManager.getUserGallery()

      // 如果指定了订单ID，只显示该订单的作品
      if (this.data.filterOrderId) {
        works = galleryManager.getWorksByOrderId(this.data.filterOrderId)
        console.log(`筛选订单 ${this.data.filterOrderId} 的作品：`, works.length, '个')
      }

      // 如果没有作品，添加一些示例作品（仅用于演示）
      if (works.length === 0 && !this.data.filterOrderId) {
        works = [
          {
            id: 'demo_001',
            type: 'photo',
            location: '书圣故里',
            date: '12-01',
            createTime: '2024-12-01 14:30:00',
            title: '书圣故里 - 古典园林',
            device: 'DJI Air 3',
            resolution: '4000×3000',
            fileSize: '2.5MB',
            url: 'http://iph.href.lu/400x400',
            thumbnail: 'http://iph.href.lu/400x400',
            selected: false,
            orderId: 'demo_order'
          }
        ]
      }

      // 计算统计数据
      const stats = galleryManager.calculateGalleryStats(works)

      this.setData({
        allWorks: works,
        galleryStats: stats
      })

      // 应用筛选
      this.applyFilter()

      console.log('作品集数据加载完成：', works.length, '个作品')
    } catch (error) {
      console.error('加载作品集失败：', error)
      wx.showToast({
        title: '加载失败',
        icon: 'error'
      })
    } finally {
      this.setData({ loading: false })
    }
  },

  /**
   * 分类筛选变更
   */
  onCategoryChange(e) {
    const category = e.currentTarget.dataset.category
    this.setData({ currentCategory: category })
    this.applyFilter()
  },

  /**
   * 安全的日期解析函数
   */
  parseDate(dateString) {
    // 如果已经是标准ISO格式，直接使用
    if (dateString.includes('T')) {
      return new Date(dateString)
    }
    // 如果是 "2024-11-30 09:15" 格式，转换为ISO格式
    if (dateString.includes(' ')) {
      return new Date(dateString.replace(' ', 'T') + ':00')
    }
    // 其他格式直接尝试解析
    return new Date(dateString)
  },

  /**
   * 应用筛选
   */
  applyFilter() {
    let filtered = [...this.data.allWorks]

    // 如果是订单筛选模式，不需要额外筛选（已经在loadGalleryData中筛选过了）
    if (this.data.currentCategory === 'order') {
      // 订单筛选模式，直接使用所有作品
    } else if (this.data.currentCategory !== 'all') {
      if (this.data.currentCategory === 'recent') {
        // 最近7天的作品
        const sevenDaysAgo = new Date()
        sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 7)
        filtered = filtered.filter(work => this.parseDate(work.createTime) >= sevenDaysAgo)
      } else {
        // 按类型筛选
        filtered = filtered.filter(work => work.type === this.data.currentCategory)
      }
    }

    // 按创建时间排序（最新的在前）
    filtered.sort((a, b) => this.parseDate(b.createTime) - this.parseDate(a.createTime))

    this.setData({ filteredWorks: filtered })
    console.log('筛选后作品数量：', filtered.length)
  },

  /**
   * 查看作品详情
   */
  viewWork(e) {
    const work = e.currentTarget.dataset.work
    
    wx.showModal({
      title: work.title,
      content: `地点：${work.location}\n创建时间：${work.createTime}\n浏览量：${work.views}\n点赞数：${work.likes}`,
      confirmText: '查看详情',
      success: (res) => {
        if (res.confirm) {
          console.log('查看作品详情：', work.title)
          // 这里可以跳转到作品详情页面
        }
      }
    })
  },

  /**
   * 上传作品
   */
  uploadWork() {
    wx.showActionSheet({
      itemList: ['拍照上传', '从相册选择', '录制视频'],
      success: (res) => {
        switch (res.tapIndex) {
          case 0:
            this.takePhoto()
            break
          case 1:
            this.chooseImage()
            break
          case 2:
            this.recordVideo()
            break
        }
      }
    })
  },

  /**
   * 拍照上传
   */
  takePhoto() {
    wx.chooseMedia({
      count: 1,
      mediaType: ['image'],
      sourceType: ['camera'],
      success: (res) => {
        console.log('拍照成功：', res.tempFiles)
        this.uploadMedia(res.tempFiles[0])
      }
    })
  },

  /**
   * 选择图片
   */
  chooseImage() {
    wx.chooseMedia({
      count: 9,
      mediaType: ['image'],
      sourceType: ['album'],
      success: (res) => {
        console.log('选择图片成功：', res.tempFiles)
        // 可以批量上传
        res.tempFiles.forEach(file => this.uploadMedia(file))
      }
    })
  },

  /**
   * 录制视频
   */
  recordVideo() {
    wx.chooseMedia({
      count: 1,
      mediaType: ['video'],
      sourceType: ['camera'],
      success: (res) => {
        console.log('录制视频成功：', res.tempFiles)
        this.uploadMedia(res.tempFiles[0])
      }
    })
  },

  /**
   * 上传媒体文件
   */
  uploadMedia(file) {
    wx.showLoading({ title: '上传中...' })
    
    // 模拟上传过程
    setTimeout(() => {
      wx.hideLoading()
      wx.showToast({
        title: '上传成功',
        icon: 'success'
      })
      
      // 刷新作品列表
      this.loadGalleryData()
    }, 1500)
  },

  /**
   * 跳转到设备页面
   */
  goToEquipment() {
    wx.switchTab({
      url: '/pages/equipment/equipment'
    })
  },

  /**
   * 返回上一页
   */
  goBack() {
    wx.navigateBack()
  },

  /**
   * 切换选择模式
   */
  toggleSelectMode() {
    const selectMode = !this.data.selectMode
    this.setData({ selectMode })

    if (!selectMode) {
      // 退出选择模式时清除所有选择
      const allWorks = this.data.allWorks.map(work => ({
        ...work,
        selected: false
      }))
      this.setData({ allWorks })
      this.filterWorks()
    }
  },

  /**
   * 处理媒体点击
   */
  handleMediaTap(e) {
    const { work, index } = e.currentTarget.dataset

    if (this.data.selectMode) {
      // 选择模式下切换选中状态
      const allWorks = [...this.data.allWorks]
      const workIndex = allWorks.findIndex(w => w.id === work.id)
      if (workIndex !== -1) {
        allWorks[workIndex].selected = !allWorks[workIndex].selected
        this.setData({ allWorks })
        this.filterWorks()
      }
    } else {
      // 普通模式下查看媒体
      this.viewMedia(work)
    }
  },

  /**
   * 查看媒体
   */
  viewMedia(media) {
    this.setData({
      currentMedia: media,
      showModal: true
    })
  },

  /**
   * 关闭模态框
   */
  closeModal() {
    this.setData({ showModal: false })
  },

  /**
   * 阻止事件冒泡
   */
  stopPropagation() {
    // 阻止事件冒泡
  },

  /**
   * 下载媒体
   */
  downloadMedia() {
    wx.showToast({
      title: '下载成功！作品已保存到手机相册',
      icon: 'success',
      duration: 2000
    })
  },

  /**
   * 分享媒体
   */
  shareMedia() {
    wx.showActionSheet({
      itemList: ['微信好友', '朋友圈', 'QQ空间', '新浪微博', '复制链接'],
      success: (res) => {
        const options = ['微信好友', '朋友圈', 'QQ空间', '新浪微博', '复制链接']
        wx.showToast({
          title: `分享到${options[res.tapIndex]}`,
          icon: 'success'
        })
      }
    })
  },

  /**
   * 编辑媒体
   */
  editMedia() {
    wx.showActionSheet({
      itemList: ['裁剪', '滤镜', '调色', '添加水印'],
      success: (res) => {
        const options = ['裁剪', '滤镜', '调色', '添加水印']
        wx.showToast({
          title: `${options[res.tapIndex]}功能`,
          icon: 'none'
        })
      }
    })
  },

  /**
   * 删除媒体
   */
  deleteMedia() {
    wx.showModal({
      title: '删除作品',
      content: '确认删除这个作品？删除后不可恢复',
      success: (res) => {
        if (res.confirm) {
          wx.showToast({
            title: '作品已删除',
            icon: 'success'
          })
          this.closeModal()
          // TODO: 实际删除逻辑
        }
      }
    })
  },

  /**
   * 加载更多
   */
  loadMore() {
    wx.showToast({
      title: '加载更多作品...',
      icon: 'none'
    })
    // TODO: 实际加载更多逻辑
  }
})