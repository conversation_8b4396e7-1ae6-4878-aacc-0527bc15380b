<!--pages/home/<USER>
<view class="home-container">
  <!-- 顶部导航 -->
  <view class="navbar">
    <view class="navbar-content">
      <view class="brand-section">
        <text class="brand-title">逍遥境</text>
        <text class="brand-subtitle">发现美好瞬间</text>
      </view>

      <view class="nav-icons">
        <!-- <text class="nav-icon" bindtap="showSearchModal">🔍</text> -->
        <text class="nav-icon" bindtap="showNotifications">🔔</text>
      </view>
    </view>
  </view>

  <!-- 搜索栏 -->
  <!-- <view class="search-section">
    <view class="search-bar" bindtap="showSearchModal">
      <text class="search-icon">🔍</text>
      <input class="search-input" placeholder="搜索设备或拍摄地点..." disabled />
    </view>
  </view> -->

  <!-- 热门推荐 -->
  <view class="section">
    <view class="section-header">
      <text class="section-title">热门推荐</text>
    </view>

    <swiper
      class="equipment-swiper"
      display-multiple-items="{{1}}"
      circular="true"
      autoplay="true"
      interval="3000"
      duration="500"
    >
      <swiper-item
        wx:for="{{hotEquipment}}"
        wx:key="id"
        class="swiper-item"
      >
        <view
          class="equipment-card"
          bindtap="goToEquipmentDetail"
          data-id="{{item.id}}"
        >
          <view class="equipment-image">
            <image class="equipment-photo" src="{{item.image || 'http://iph.href.lu/300x200'}}" mode="aspectFill" />
          </view>
          <view class="equipment-info">
            <text class="equipment-name">{{item.name}}</text>
            <text class="equipment-desc">{{item.description}}</text>
            <view class="equipment-price-row">
              <text class="equipment-price">¥{{item.price}}<text class="price-unit">/{{item.unit}}</text></text>
              <button class="rent-btn" bindtap="rentEquipment" data-id="{{item.id}}" catchtap="stopPropagation">租用</button>
            </view>
          </view>
        </view>
      </swiper-item>
    </swiper>
  </view>

  <!-- 热门拍摄地点 -->
  <view class="section">
    <view class="section-header">
      <text class="section-title">热门拍摄地点</text>
    </view>

    <view class="location-list">
      <view
        class="location-card"
        wx:for="{{hotLocations}}"
        wx:key="id"
        bindtap="goToLocationDetail"
        data-id="{{item.id}}"
      >
        <image class="location-image" src="{{item.image || 'http://iph.href.lu/128x128'}}" mode="aspectFill" />
        <view class="location-info">
          <text class="location-name">{{item.name}}</text>
          <text class="location-desc">距离您 {{item.distance}}km · {{item.category}}</text>
          <view class="location-rating">
            <view class="rating-stars">
              <text class="star" wx:for="{{5}}" wx:key="*this">⭐</text>
            </view>
            <text class="rating-text">{{item.rating}} ({{item.reviewCount}})</text>
          </view>
        </view>
        <text class="location-arrow">></text>
      </view>
    </view>
  </view>

  <!-- 快速操作 -->
  <view class="section">
    <view class="section-header">
      <text class="section-title">快速操作</text>
    </view>

    <view class="quick-actions">
      <view class="action-item" bindtap="goToEquipment">
        <view class="action-icon">
          <text class="icon">🚁</text>
        </view>
        <text class="action-text">浏览设备</text>
      </view>
      <view class="action-item" bindtap="goToLocation">
        <view class="action-icon">
          <text class="icon">📍</text>
        </view>
        <text class="action-text">选择地点</text>
      </view>
      <view class="action-item" bindtap="goToGallery">
        <view class="action-icon">
          <text class="icon">📸</text>
        </view>
        <text class="action-text">我的作品</text>
      </view>
      <view class="action-item" bindtap="showCustomerService">
        <view class="action-icon">
          <text class="icon">🎧</text>
        </view>
        <text class="action-text">客服支持</text>
      </view>
    </view>
  </view>
</view>

<!-- 搜索弹窗 -->
<view class="search-modal" wx:if="{{showSearch}}" bindtap="hideSearchModal">
  <view class="search-modal-content" catchtap="stopPropagation">
    <view class="search-input-wrapper">
      <input 
        class="search-input" 
        placeholder="搜索设备或地点"
        value="{{searchKeyword}}"
        bindinput="onSearchInput"
        auto-focus="true"
      />
      <button class="search-btn" bindtap="performSearch">搜索</button>
    </view>
    
    <view class="search-suggestions" wx:if="{{searchSuggestions.length > 0}}">
      <text class="suggestions-title">热门搜索</text>
      <view class="suggestions-list">
        <text 
          class="suggestion-item" 
          wx:for="{{searchSuggestions}}" 
          wx:key="*this"
          bindtap="selectSuggestion"
          data-keyword="{{item}}"
        >
          {{item}}
        </text>
      </view>
    </view>
  </view>
</view>