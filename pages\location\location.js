// pages/location/location.js
const app = getApp()
const auth = require('../../utils/auth.js')
const { mockLocations } = require('../../utils/mockData.js')

Page({

  /**
   * 页面的初始数据
   */
  data: {
    // 地点数据
    allLocations: [],
    filteredLocations: [],
    
    // 搜索和定位
    searchKeyword: '',
    currentLocation: null,
    
    // 页面参数
    equipmentId: null,
    equipmentName: null,
    
    // 页面状态
    loading: true
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    console.log('地点选择页面加载', options)
    this.setData({
      equipmentId: options.equipmentId,
      equipmentName: decodeURIComponent(options.equipmentName || '')
    })
    this.checkAuth()
    this.loadLocations()
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {

  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {

  },

  /**
   * 检查认证状态
   */
  checkAuth() {
    if (!auth.checkLoginStatus()) {
      auth.redirectToLogin('/pages/location/location')
      return false
    }
    return true
  },

  /**
   * 加载地点数据
   */
  async loadLocations() {
    if (!this.checkAuth()) return

    this.setData({ loading: true })

    try {
      const allLocations = [...mockLocations]
      this.setData({
        allLocations,
        filteredLocations: allLocations
      })
      
      console.log('地点数据加载完成：', allLocations.length, '个')
    } catch (error) {
      console.error('加载地点失败：', error)
      wx.showToast({
        title: '加载失败',
        icon: 'error'
      })
    } finally {
      this.setData({ loading: false })
    }
  },

  /**
   * 搜索输入
   */
  onSearchInput(e) {
    const keyword = e.detail.value
    this.setData({ searchKeyword: keyword })
    this.filterLocations(keyword)
  },

  /**
   * 过滤地点
   */
  filterLocations(keyword) {
    let filtered = [...this.data.allLocations]

    if (keyword.trim()) {
      filtered = filtered.filter(loc =>
        loc.name.includes(keyword) ||
        loc.address.includes(keyword) ||
        (loc.features && loc.features.some(feature => feature.includes(keyword)))
      )
    }

    this.setData({ filteredLocations: filtered })
  },

  /**
   * 获取当前位置
   */
  getCurrentLocation() {
    wx.showLoading({ title: '定位中...' })
    
    wx.getLocation({
      type: 'wgs84',
      success: (res) => {
        console.log('获取位置成功：', res.latitude, res.longitude)
        this.setData({ currentLocation: '当前位置' })
        
        // 模拟根据位置排序地点
        const sortedLocations = this.data.filteredLocations.sort((a, b) => {
          return a.distance - b.distance
        })
        
        this.setData({ filteredLocations: sortedLocations })
        
        wx.hideLoading()
        wx.showToast({
          title: '定位成功',
          icon: 'success'
        })
      },
      fail: (error) => {
        console.error('定位失败：', error)
        wx.hideLoading()
        wx.showToast({
          title: '定位失败',
          icon: 'error'
        })
      }
    })
  },

  /**
   * 选择地点
   */
  selectLocation(e) {
    const location = e.currentTarget.dataset.location

    // 所有地点都可用，移除可用性检查

    console.log('选择地点：', location.name)
    console.log('当前设备信息：', { equipmentId: this.data.equipmentId, equipmentName: this.data.equipmentName })

    // 检查是否已选择设备
    if (!this.data.equipmentId || !this.data.equipmentName || this.data.equipmentName === '') {
      console.log('未选择设备，显示提示框')

      // 阻止事件冒泡
      e.stopPropagation && e.stopPropagation()

      wx.showModal({
        title: '请先选择设备',
        content: '您需要先选择租用的无人机设备，然后再选择拍摄地点',
        confirmText: '去选择',
        cancelText: '取消',
        success: (res) => {
          console.log('模态框回调：', res)
          if (res.confirm) {
            console.log('用户确认，跳转到设备页面')
            // 跳转到设备页面
            wx.switchTab({
              url: '/pages/equipment/equipment'
            })
          }
        },
        fail: (err) => {
          console.error('显示模态框失败：', err)
        }
      })
      return
    }

    console.log('设备已选择，跳转到订单确认页面')
    // 跳转到订单确认页面
    const url = `/pages/order-confirm/order-confirm?equipmentId=${this.data.equipmentId}&locationId=${location.id}&equipmentName=${encodeURIComponent(this.data.equipmentName)}&locationName=${encodeURIComponent(location.name)}`

    wx.navigateTo({
      url: url
    })
  }
})