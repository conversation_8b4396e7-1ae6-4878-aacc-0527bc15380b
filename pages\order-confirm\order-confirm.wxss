/* pages/order-confirm/order-confirm.wxss */

.confirm-container {
  background-color: #f9fafb;
  min-height: 100vh;
  padding: 32rpx;
  padding-bottom: 300rpx;
}

/* 重新预订提示 */
.reorder-tip {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 24rpx;
  padding: 32rpx;
  margin-bottom: 32rpx;
  display: flex;
  align-items: center;
  box-shadow: 0 8rpx 24rpx rgba(102, 126, 234, 0.2);
}

.tip-icon {
  font-size: 48rpx;
  margin-right: 24rpx;
  animation: rotate 2s linear infinite;
}

@keyframes rotate {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.tip-content {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.tip-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #ffffff;
  margin-bottom: 8rpx;
}

.tip-desc {
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.9);
  line-height: 1.4;
}

/* 信息卡片 */
.info-card {
  background: #ffffff;
  border-radius: 32rpx;
  padding: 40rpx;
  margin-bottom: 32rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.04);
}

.card-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 24rpx;
  display: block;
}

/* 设备信息 */
.equipment-info {
  display: flex;
  align-items: center;
  gap: 32rpx;
}

.equipment-image {
  width: 160rpx;
  height: 128rpx;
  border-radius: 24rpx;
  flex-shrink: 0;
}

.equipment-details {
  flex: 1;
}

.equipment-name {
  font-size: 32rpx;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 8rpx;
  display: block;
}

.equipment-desc {
  font-size: 28rpx;
  color: #6b7280;
  margin-bottom: 16rpx;
  display: block;
}

.equipment-price {
  font-size: 36rpx;
  font-weight: bold;
  color: #1f2937;
  display: block;
}

.price-unit {
  font-size: 28rpx;
  font-weight: normal;
  color: #6b7280;
}

/* 地点信息 */
.location-info {
  display: flex;
  align-items: center;
  gap: 32rpx;
}

.location-image {
  width: 160rpx;
  height: 128rpx;
  border-radius: 24rpx;
  flex-shrink: 0;
}

.location-details {
  flex: 1;
}

.location-name {
  font-size: 32rpx;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 8rpx;
  display: block;
}

.location-desc {
  font-size: 28rpx;
  color: #6b7280;
  margin-bottom: 16rpx;
  display: block;
}

.location-meta {
  display: flex;
  gap: 32rpx;
}

.location-distance,
.location-rating {
  font-size: 24rpx;
  color: #6b7280;
}

/* 表单标签 */
.form-label {
  font-size: 28rpx;
  font-weight: 500;
  color: #6b7280;
  margin-bottom: 16rpx;
  display: block;
}

/* 日期选择 */
.date-section {
  margin-bottom: 32rpx;
}

.date-picker {
  width: 100%;
  border: 2rpx solid #d1d5db;
  border-radius: 24rpx;
  padding: 24rpx 32rpx;
  font-size: 32rpx;
  color: #1f2937;
  background: #ffffff;
}

/* 时间段选择 */
.time-section {
  margin-bottom: 32rpx;
}

.time-slots {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 24rpx;
}

.time-slot {
  background: #f9fafb;
  border: 2rpx solid #e5e7eb;
  border-radius: 24rpx;
  padding: 24rpx;
  text-align: center;
  transition: all 0.3s ease;
}

.time-slot.selected {
  background: #1f2937;
  border-color: #1f2937;
  color: #ffffff;
  transform: scale(1.05);
}

.time-slot.disabled {
  background: #fef2f2;
  border-color: #fecaca;
  opacity: 0.5;
}

.time-value {
  font-size: 28rpx;
  font-weight: 500;
  margin-bottom: 8rpx;
  display: block;
}

.time-slot.selected .time-value {
  color: #ffffff;
}

.time-slot.disabled .time-value {
  color: #ef4444;
}

.time-status {
  font-size: 24rpx;
  color: #6b7280;
  display: block;
}

.time-slot.selected .time-status {
  color: #ffffff;
}

.time-slot.disabled .time-status {
  color: #ef4444;
}

/* 时长选择 */
.duration-section {
  margin-bottom: 0;
}

.duration-options {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 24rpx;
}

.duration-btn {
  background: #f9fafb;
  border: 2rpx solid #e5e7eb;
  border-radius: 24rpx;
  padding: 24rpx;
  text-align: center;
  font-size: 28rpx;
  font-weight: 500;
  color: #1f2937;
  transition: all 0.3s ease;
}

.duration-btn.selected {
  background: #1f2937;
  border-color: #1f2937;
  color: #ffffff;
}

/* 费用明细 */
.fee-list {
  margin-top: 0;
}

.fee-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24rpx 0;
}

.fee-label {
  font-size: 32rpx;
  color: #6b7280;
}

.fee-value {
  font-size: 32rpx;
  font-weight: 500;
  color: #1f2937;
}

.fee-divider {
  height: 2rpx;
  background: #e5e7eb;
  margin: 24rpx 0;
}

.fee-item.total {
  padding-top: 24rpx;
}

.fee-item.total .fee-label {
  font-size: 36rpx;
  font-weight: 600;
  color: #1f2937;
}

.fee-item.total .fee-value {
  font-size: 48rpx;
  font-weight: bold;
  color: #1f2937;
}

/* 余额卡片 */
.balance-card {
  background: #f3f4f6;
  border: 2rpx solid #e5e7eb;
  border-radius: 32rpx;
  padding: 32rpx;
  margin-bottom: 32rpx;
  display: flex;
  align-items: center;
  gap: 24rpx;
}

.balance-card.insufficient {
  background: #fef2f2;
  border-color: #fecaca;
}

.balance-icon {
  font-size: 48rpx;
}

.balance-info {
  flex: 1;
}

.balance-title {
  font-size: 28rpx;
  font-weight: 500;
  color:rgb(104, 106, 116);
  margin-bottom: 8rpx;
  display: block;
}

.balance-card.insufficient .balance-title {
  color: #dc2626;
}

.balance-amount {
  font-size: 36rpx;
  font-weight: bold;
  color: #1f2937;
  display: block;
}

.balance-card.insufficient .balance-amount {
  color: #b91c1c;
}

.balance-status {
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.status-icon {
  font-size: 32rpx;
  color: #10b981;
}

.balance-card.insufficient .status-icon {
  color: #dc2626;
}

.status-text {
  font-size: 28rpx;
  font-weight: 500;
  color: #10b981;
}

.balance-card.insufficient .status-text {
  color: #dc2626;
}

/* 底部操作栏 */
.bottom-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: #ffffff;
  border-top: 2rpx solid #e5e7eb;
  padding: 32rpx 48rpx;
  padding-bottom: calc(32rpx + env(safe-area-inset-bottom));
  box-shadow: 0 -8rpx 16rpx rgba(0, 0, 0, 0.1);
  z-index: 100;
}

.bottom-total {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 24rpx;
}

.total-label {
  font-size: 32rpx;
  color: #6b7280;
}

.total-amount {
  font-size: 48rpx;
  font-weight: bold;
  color: #1f2937;
}

.confirm-btn {
  width: 100%;
  background: linear-gradient(135deg, #1f2937 0%, #374151 100%);
  color: #ffffff;
  padding: 24rpx;
  border-radius: 24rpx;
  font-size: 32rpx;
  font-weight: 500;
  border: none;
  transition: all 0.3s ease;
}

.confirm-btn.disabled {
  background: #d1d5db;
  color: #9ca3af;
}

.equipment-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.equipment-name {
  font-size: 32rpx;
  font-weight: 600;
  color: #1f2937;
}

.equipment-brand {
  font-size: 28rpx;
  color: #6b7280;
}

.equipment-price {
  font-size: 28rpx;
  color: #10a37f;
  font-weight: 600;
}

/* 地点信息 */
.location-section {
  background: #ffffff;
  margin-bottom: 16rpx;
}

.location-card {
  padding: 32rpx;
}

.location-info {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.location-name {
  font-size: 32rpx;
  font-weight: 600;
  color: #1f2937;
}

.location-address {
  font-size: 28rpx;
  color: #6b7280;
  line-height: 1.4;
}

.location-distance {
  font-size: 24rpx;
  color: #9ca3af;
}

/* 租赁时间 */
.datetime-section {
  background: #ffffff;
  margin-bottom: 16rpx;
}

.datetime-form {
  padding: 0 32rpx 32rpx;
}

.form-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 32rpx 0;
  border-bottom: 2rpx solid #f3f4f6;
}

.form-item:last-child {
  border-bottom: none;
}

.form-label {
  font-size: 32rpx;
  color: #1f2937;
  font-weight: 500;
}

.picker-value {
  font-size: 32rpx;
  color: #6b7280;
  padding: 16rpx 24rpx;
  background: #f9fafb;
  border-radius: 12rpx;
  min-width: 200rpx;
  text-align: center;
}

/* 费用明细 */
.fee-section {
  background: #ffffff;
  margin-bottom: 16rpx;
}

.fee-list {
  padding: 0 32rpx 32rpx;
}

.fee-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24rpx 0;
  border-bottom: 2rpx solid #f3f4f6;
}

.fee-item:last-child {
  border-bottom: none;
}

.fee-item.total {
  padding-top: 32rpx;
  border-top: 4rpx solid #f3f4f6;
  margin-top: 16rpx;
}

.fee-label {
  font-size: 32rpx;
  color: #6b7280;
}

.fee-item.total .fee-label {
  font-size: 36rpx;
  color: #1f2937;
  font-weight: 600;
}

.fee-value {
  font-size: 32rpx;
  color: #1f2937;
  font-weight: 500;
}

.fee-item.total .fee-value {
  font-size: 36rpx;
  color: #10a37f;
  font-weight: 700;
}

/* 用户余额 */
.balance-section {
  background: #ffffff;
  padding: 32rpx;
  margin-bottom: 16rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.balance-info {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.balance-label {
  font-size: 28rpx;
  color: #6b7280;
}

.balance-amount {
  font-size: 36rpx;
  color: #1f2937;
  font-weight: 600;
}

.balance-status {
  padding: 12rpx 24rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
  font-weight: 500;
}

.balance-status.sufficient {
  background: rgba(16, 185, 129, 0.1);
  color: #10b981;
}

.balance-status.insufficient {
  background: rgba(239, 68, 68, 0.1);
  color: #ef4444;
}

/* 底部提交栏 */
.submit-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: #ffffff;
  padding: 24rpx 32rpx;
  padding-bottom: calc(24rpx + env(safe-area-inset-bottom));
  display: flex;
  align-items: center;
  gap: 32rpx;
  box-shadow: 0 -8rpx 16rpx rgba(0, 0, 0, 0.1);
  z-index: 100;
}

.total-info {
  display: flex;
  flex-direction: column;
  gap: 4rpx;
}

.total-label {
  font-size: 24rpx;
  color: #6b7280;
}

.total-amount {
  font-size: 36rpx;
  color: #10a37f;
  font-weight: 700;
}

.submit-btn {
  flex: 1;
  height: 96rpx;
  background: #10a37f;
  color: #ffffff;
  border-radius: 24rpx;
  font-size: 32rpx;
  font-weight: 600;
  border: none;
  transition: all 0.2s ease;
}

.submit-btn:hover {
  background: #059669;
  transform: translateY(-2rpx);
}

.submit-btn[disabled] {
  opacity: 0.5;
  background: #f3f4f6;
  color: #9ca3af;
  cursor: not-allowed;
}

.submit-btn[disabled]:hover {
  transform: none;
  background: #f3f4f6;
}