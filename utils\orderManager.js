/**
 * 订单管理工具模块
 * 提供订单创建、余额扣除、订单查询等功能
 * 为后端接口预留调用结构
 */

const auth = require('./auth.js')

/**
 * 创建订单并处理支付
 * @param {Object} orderData 订单数据
 * @returns {Promise<Object>} 处理结果
 */
async function createOrder(orderData) {
  try {
    console.log('开始创建订单：', orderData)

    // 1. 验证用户登录状态
    const currentUser = auth.getCurrentUser()
    if (!currentUser) {
      throw new Error('用户未登录')
    }

    // 2. 验证余额
    if (currentUser.balance < orderData.totalAmount) {
      throw new Error('余额不足')
    }

    // 3. 调用后端API创建订单
    const createResult = await callCreateOrderAPI(orderData)
    if (!createResult.success) {
      throw new Error(createResult.message)
    }

    // 4. 扣除余额
    const deductResult = await deductBalance(orderData.totalAmount)
    if (!deductResult.success) {
      // 实际项目中需要回滚订单
      console.error('扣除余额失败，需要回滚订单')
      throw new Error(deductResult.message)
    }

    // 5. 保存订单到本地
    await saveOrderToLocal(createResult.order)

    return {
      success: true,
      orderId: createResult.order.id,
      order: createResult.order,
      message: '订单创建成功'
    }

  } catch (error) {
    console.error('创建订单失败：', error)
    return {
      success: false,
      message: error.message || '订单创建失败'
    }
  }
}

/**
 * 调用后端API创建订单（模拟）
 * @param {Object} orderData 订单数据
 * @returns {Promise<Object>} API响应
 */
async function callCreateOrderAPI(orderData) {
  try {
    // 模拟网络延迟
    await new Promise(resolve => setTimeout(resolve, 1000))

    // 生成订单ID
    const orderId = 'order_' + Date.now()
    const now = new Date()

    // 构造完整订单对象
    const order = {
      id: orderId,
      equipmentId: orderData.equipmentId,
      equipmentName: orderData.equipmentName,
      locationId: orderData.locationId,
      locationName: orderData.locationName,
      userId: orderData.userId,
      status: 'ongoing',
      statusText: '进行中',
      orderTime: now.toISOString().replace('T', ' ').substring(0, 19),
      startTime: orderData.startTime,
      endTime: calculateEndTime(orderData.startTime, orderData.duration),
      duration: orderData.duration,
      price: orderData.price || 0,
      totalAmount: orderData.totalAmount,
      payment: {
        method: 'balance',
        methodText: '余额支付',
        transactionId: 'tx_' + Date.now()
      },
      equipment: {
        icon: 'icon-drone',
        battery: 100,
        specs: orderData.equipmentSpecs || []
      },
      location: {
        address: orderData.locationAddress || '',
        distance: orderData.locationDistance || 0
      },
      canControl: true,
      canCancel: true,
      canReview: false,
      remainingTime: calculateRemainingTime(orderData.startTime, orderData.duration),
      progress: 0 // 新订单进度为0
    }

    // TODO: 实际项目中替换为真实的API调用
    /*
    const response = await wx.request({
      url: 'https://api.yourdomain.com/orders',
      method: 'POST',
      data: orderData,
      header: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${wx.getStorageSync('token')}`
      }
    })

    if (response.statusCode !== 200) {
      throw new Error('API调用失败')
    }

    return {
      success: true,
      order: response.data.order,
      message: '订单创建成功'
    }
    */

    return {
      success: true,
      order: order,
      message: '订单创建成功'
    }

  } catch (error) {
    console.error('创建订单API调用失败：', error)
    return {
      success: false,
      message: '网络错误，请重试'
    }
  }
}

/**
 * 扣除用户余额
 * @param {Number} amount 扣除金额
 * @returns {Promise<Object>} 扣除结果
 */
async function deductBalance(amount) {
  try {
    console.log('扣除用户余额：', amount)

    const currentUser = auth.getCurrentUser()
    if (!currentUser) {
      throw new Error('用户信息获取失败')
    }

    // 检查余额
    if (currentUser.balance < amount) {
      throw new Error('余额不足')
    }

    // 计算新余额
    const newBalance = Math.round((currentUser.balance - amount) * 100) / 100 // 避免浮点数精度问题

    // 更新用户信息
    const updatedUser = {
      ...currentUser,
      balance: newBalance
    }

    // 更新本地存储
    const updateResult = auth.updateUserInfo(updatedUser)
    if (!updateResult) {
      throw new Error('更新用户信息失败')
    }

    // TODO: 实际项目中调用后端API扣除余额
    /*
    const response = await wx.request({
      url: 'https://api.yourdomain.com/users/balance/deduct',
      method: 'POST',
      data: { amount },
      header: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${wx.getStorageSync('token')}`
      }
    })

    if (response.statusCode !== 200) {
      throw new Error('余额扣除失败')
    }
    */

    console.log('余额扣除成功，新余额：', newBalance)

    return {
      success: true,
      newBalance: newBalance,
      message: '余额扣除成功'
    }

  } catch (error) {
    console.error('扣除余额失败：', error)
    return {
      success: false,
      message: error.message || '余额扣除失败'
    }
  }
}

/**
 * 保存订单到本地存储
 * @param {Object} order 订单对象
 */
async function saveOrderToLocal(order) {
  try {
    // 获取现有订单列表
    let orders = wx.getStorageSync('userOrders') || []

    // 添加新订单到列表开头
    orders.unshift(order)

    // 限制本地存储的订单数量（可选）
    if (orders.length > 100) {
      orders = orders.slice(0, 100)
    }

    // 保存到本地存储
    wx.setStorageSync('userOrders', orders)

    console.log('订单已保存到本地存储：', order.id)

  } catch (error) {
    console.error('保存订单到本地失败：', error)
    throw error
  }
}

/**
 * 获取用户订单列表
 * @returns {Array} 订单列表
 */
function getUserOrders() {
  try {
    return wx.getStorageSync('userOrders') || []
  } catch (error) {
    console.error('获取用户订单失败：', error)
    return []
  }
}

/**
 * 更新订单状态
 * @param {string} orderId - 订单ID
 * @param {string} status - 新状态
 * @param {string} statusText - 状态文本
 * @returns {Promise<Object>} 更新结果
 */
async function updateOrderStatus(orderId, status, statusText) {
  try {
    // TODO: 调用后端API更新订单状态
    /*
    const response = await wx.request({
      url: `https://api.yourdomain.com/orders/${orderId}/status`,
      method: 'PUT',
      data: { status, statusText },
      header: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${wx.getStorageSync('token')}`
      }
    })

    if (response.statusCode !== 200) {
      throw new Error('订单状态更新失败')
    }
    */

    // 更新本地存储
    const userOrders = getUserOrders()
    const updatedOrders = userOrders.map(order => {
      if (order.id === orderId) {
        return { ...order, status, statusText }
      }
      return order
    })

    wx.setStorageSync('userOrders', updatedOrders)

    console.log('订单状态更新成功：', orderId, status)
    return {
      success: true,
      message: '订单状态更新成功'
    }
  } catch (error) {
    console.error('更新订单状态失败：', error)
    return {
      success: false,
      message: error.message || '更新订单状态失败'
    }
  }
}

/**
 * 根据ID获取订单详情
 * @param {String} orderId 订单ID
 * @returns {Object|null} 订单详情
 */
function getOrderById(orderId) {
  try {
    const orders = getUserOrders()
    return orders.find(order => order.id === orderId) || null
  } catch (error) {
    console.error('获取订单详情失败：', error)
    return null
  }
}

/**
 * 计算结束时间
 * @param {String} startTime 开始时间
 * @param {Number} duration 持续时长（小时）
 * @returns {String} 结束时间
 */
function calculateEndTime(startTime, duration) {
  const start = new Date(startTime)
  const end = new Date(start.getTime() + duration * 60 * 60 * 1000)
  return end.toISOString().replace('T', ' ').substring(0, 19)
}

/**
 * 计算剩余时间显示文本
 * @param {string} startTime - 开始时间
 * @param {number} duration - 时长（小时）
 * @returns {string} 剩余时间文本
 */
function calculateRemainingTime(startTime, duration) {
  const start = new Date(startTime)
  const end = new Date(start.getTime() + duration * 60 * 60 * 1000)
  const now = new Date()

  // 如果还没开始，显示总时长
  if (now < start) {
    return `${duration}小时`
  }

  // 如果已经结束，显示0
  if (now >= end) {
    return '0分钟'
  }

  // 计算剩余时间
  const remainingMs = end.getTime() - now.getTime()
  const remainingHours = Math.floor(remainingMs / (1000 * 60 * 60))
  const remainingMinutes = Math.floor((remainingMs % (1000 * 60 * 60)) / (1000 * 60))

  if (remainingHours > 0) {
    return `${remainingHours}小时${remainingMinutes}分钟`
  } else {
    return `${remainingMinutes}分钟`
  }
}

/**
 * 更新订单拍摄统计
 * @param {string} orderId - 订单ID
 * @param {Object} shootingStats - 拍摄统计数据
 * @returns {Promise<Object>} 更新结果
 */
async function updateOrderShootingStats(orderId, shootingStats) {
  try {
    // 获取用户订单
    const userOrders = getUserOrders()
    const orderIndex = userOrders.findIndex(order => order.id === orderId)

    if (orderIndex === -1) {
      throw new Error('订单不存在')
    }

    // 更新拍摄统计
    userOrders[orderIndex] = {
      ...userOrders[orderIndex],
      photoCount: shootingStats.photoCount || 0,
      videoCount: shootingStats.videoCount || 0,
      totalWorks: shootingStats.totalWorks || 0,
      lastShootingTime: new Date().toISOString().replace('T', ' ').substring(0, 19)
    }

    // 保存到本地存储
    wx.setStorageSync('userOrders', userOrders)

    // TODO: 同步到后端
    /*
    const response = await wx.request({
      url: `https://api.yourdomain.com/orders/${orderId}/shooting-stats`,
      method: 'PUT',
      data: shootingStats,
      header: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${wx.getStorageSync('token')}`
      }
    })

    if (response.statusCode !== 200) {
      throw new Error('更新订单拍摄统计失败')
    }
    */

    console.log(`订单 ${orderId} 拍摄统计更新成功`)
    return {
      success: true,
      message: '拍摄统计更新成功'
    }
  } catch (error) {
    console.error('更新订单拍摄统计失败：', error)
    return {
      success: false,
      message: error.message || '更新失败'
    }
  }
}

module.exports = {
  createOrder,
  deductBalance,
  getUserOrders,
  updateOrderStatus,
  getOrderById,
  saveOrderToLocal,
  calculateEndTime,
  calculateRemainingTime,
  updateOrderShootingStats
}
