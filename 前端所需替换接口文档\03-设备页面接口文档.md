# 设备页面接口文档

## 页面概述
设备页面包括设备列表页和设备详情页，用户可以浏览、筛选、排序设备，查看详细信息并开始租赁流程。

## 当前实现分析

### 页面文件位置
- `pages/equipment/equipment.js` - 设备列表页逻辑
- `pages/equipment-detail/equipment-detail.js` - 设备详情页逻辑
- `utils/mockData.js` - 模拟设备数据

### 当前功能流程
1. **设备列表页**：
   - 加载所有设备数据
   - 支持筛选（全部/可用/专业级/入门级/运动型）
   - 支持排序（评分/价格低到高/价格高到低/热度）
   - 点击设备跳转到详情页

2. **设备详情页**：
   - 根据设备ID加载详情
   - 显示设备规格、配件、租赁须知
   - 支持收藏功能
   - 开始租赁跳转到地点选择

## 需要替换的接口

### 1. 获取设备列表接口

#### 接口信息
- **接口名称**: 获取设备列表
- **请求方法**: GET
- **接口路径**: `/api/equipment/list`
- **当前模拟位置**: `pages/equipment/equipment.js` 第98-124行 `loadEquipment` 方法

#### 请求参数
```
page=1          // 页码，默认1
limit=20        // 每页数量，默认20
filter=all      // 筛选条件：all/available/professional/entry/sport
sort=rating     // 排序方式：rating/price_low/price_high/popularity
search=关键词   // 搜索关键词，可选
brand=品牌      // 品牌筛选，可选
```

#### 响应数据
```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "list": [
      {
        "id": "dji_air3",
        "name": "DJI Air 3",
        "brand": "DJI",
        "model": "Air 3",
        "price": 80,
        "unit": "小时",
        "originalPrice": 100,
        "features": ["4K双摄", "46分钟续航", "全向避障", "智能跟随"],
        "icon": "icon-drone",
        "available": true,
        "battery": 95,
        "location": "设备点A",
        "rating": 4.9,
        "reviewCount": 127,
        "availabilityText": "今日可租",
        "description": "双主摄 · 4K/60fps HDR · 46分钟续航 · 障碍物感知",
        "image": "设备图片URL",
        "category": "professional",
        "specs": {
          "weight": "720g",
          "maxFlightTime": "46分钟",
          "maxSpeed": "21m/s",
          "cameraResolution": "4K/60fps",
          "transmission": "20公里",
          "obstacle": "全向避障"
        }
      }
    ],
    "pagination": {
      "page": 1,
      "limit": 20,
      "total": 50,
      "totalPages": 3,
      "hasNext": true,
      "hasPrev": false
    }
  }
}
```

### 2. 获取设备详情接口

#### 接口信息
- **接口名称**: 获取设备详情
- **请求方法**: GET
- **接口路径**: `/api/equipment/detail/{id}`
- **当前模拟位置**: `pages/equipment-detail/equipment-detail.js` 第110-150行 `loadEquipmentDetail` 方法

#### 请求参数
```
id: 设备ID（路径参数）
```

#### 响应数据
```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "id": "dji_air3",
    "name": "DJI Air 3",
    "brand": "DJI",
    "model": "Air 3",
    "price": 80,
    "unit": "小时",
    "originalPrice": 100,
    "features": ["4K双摄", "46分钟续航", "全向避障", "智能跟随"],
    "icon": "icon-drone",
    "available": true,
    "battery": 95,
    "location": "设备点A",
    "rating": 4.9,
    "reviewCount": 127,
    "availabilityText": "今日可租",
    "description": "双主摄 · 4K/60fps HDR · 46分钟续航 · 障碍物感知",
    "image": "设备图片URL",
    "images": ["图片URL1", "图片URL2", "图片URL3"],
    "category": "professional",
    "specs": {
      "weight": "720g",
      "maxFlightTime": "46分钟",
      "maxSpeed": "21m/s",
      "cameraResolution": "4K/60fps",
      "transmission": "20公里",
      "obstacle": "全向避障"
    },
    "accessories": [
      "无人机主体",
      "遥控器",
      "电池×3",
      "充电器",
      "螺旋桨×4",
      "携带箱"
    ],
    "rentalNotices": [
      "1. 租赁前需完成实名认证",
      "2. 设备使用需遵守当地法规",
      "3. 禁止在禁飞区域使用",
      "4. 损坏设备需照价赔偿",
      "5. 归还时需检查设备完整性"
    ],
    "reviews": [
      {
        "id": "review_001",
        "userId": "user_001",
        "userName": "用户昵称",
        "userAvatar": "头像URL",
        "rating": 5,
        "comment": "设备状态很好，航拍效果超棒！",
        "createTime": "2024-01-26T17:30:00.000Z",
        "images": ["评价图片URL1", "评价图片URL2"]
      }
    ],
    "relatedEquipment": [
      {
        "id": "dji_mini4",
        "name": "DJI Mini 4 Pro",
        "price": 60,
        "image": "图片URL",
        "rating": 4.8
      }
    ]
  }
}
```

### 3. 设备收藏接口

#### 接口信息
- **接口名称**: 添加/取消设备收藏
- **请求方法**: POST
- **接口路径**: `/api/equipment/favorite`
- **当前模拟位置**: `pages/equipment-detail/equipment-detail.js` 第231-240行 `toggleFavorite` 方法

#### 请求头
```
Authorization: Bearer {token}
```

#### 请求参数
```json
{
  "equipmentId": "dji_air3",
  "action": "add"  // add: 添加收藏, remove: 取消收藏
}
```

#### 响应数据
```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    "isFavorite": true,
    "favoriteCount": 128
  }
}
```

### 4. 获取用户收藏设备接口

#### 接口信息
- **接口名称**: 获取用户收藏的设备列表
- **请求方法**: GET
- **接口路径**: `/api/user/favorites`
- **当前模拟位置**: 暂无，需要新增

#### 请求头
```
Authorization: Bearer {token}
```

#### 请求参数
```
page=1     // 页码
limit=20   // 每页数量
```

#### 响应数据
```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "list": [
      {
        "id": "dji_air3",
        "name": "DJI Air 3",
        "price": 80,
        "image": "图片URL",
        "rating": 4.9,
        "available": true,
        "favoriteTime": "2024-01-26T10:00:00.000Z"
      }
    ],
    "pagination": {
      "page": 1,
      "limit": 20,
      "total": 5,
      "totalPages": 1
    }
  }
}
```

### 5. 设备可用性检查接口

#### 接口信息
- **接口名称**: 检查设备在指定时间的可用性
- **请求方法**: GET
- **接口路径**: `/api/equipment/availability/{id}`
- **当前模拟位置**: 暂无，需要新增

#### 请求参数
```
id: 设备ID（路径参数）
startTime=2024-01-27T10:00:00.000Z  // 开始时间
endTime=2024-01-27T12:00:00.000Z    // 结束时间
```

#### 响应数据
```json
{
  "code": 200,
  "message": "检查完成",
  "data": {
    "available": true,
    "availableTimeSlots": [
      {
        "startTime": "2024-01-27T09:00:00.000Z",
        "endTime": "2024-01-27T18:00:00.000Z"
      }
    ],
    "unavailableTimeSlots": [
      {
        "startTime": "2024-01-27T14:00:00.000Z",
        "endTime": "2024-01-27T16:00:00.000Z",
        "reason": "已被预订"
      }
    ],
    "nextAvailableTime": "2024-01-27T16:00:00.000Z"
  }
}
```

## 替换指导

### 1. 修改设备列表加载
**文件**: `pages/equipment/equipment.js`
**位置**: 第98-124行 `loadEquipment` 方法

**当前代码**:
```javascript
async loadEquipment() {
  if (!this.checkAuth()) return

  this.setData({ loading: true })

  try {
    // 模拟API调用延迟
    await new Promise(resolve => setTimeout(resolve, 500))
    
    const allEquipment = [...mockEquipment]
    this.setData({ allEquipment })
    
    // 应用当前筛选和排序
    this.applyFilterAndSort()
    
    console.log('设备数据加载完成：', allEquipment.length, '个')
  } catch (error) {
    console.error('加载设备失败：', error)
    wx.showToast({
      title: '加载失败',
      icon: 'error'
    })
  } finally {
    this.setData({ loading: false })
    wx.stopPullDownRefresh()
  }
}
```

**替换为**:
```javascript
async loadEquipment() {
  if (!this.checkAuth()) return

  this.setData({ loading: true })

  try {
    const params = {
      page: 1,
      limit: 50,
      filter: this.data.currentFilter,
      sort: this.data.currentSort
    }
    
    const response = await request.get('/api/equipment/list', params)
    
    if (response && response.data) {
      this.setData({ 
        allEquipment: response.data.list,
        filteredEquipment: response.data.list
      })
      console.log('设备数据加载完成：', response.data.list.length, '个')
    }
  } catch (error) {
    console.error('加载设备失败：', error)
    wx.showToast({
      title: '加载失败',
      icon: 'error'
    })
    // 使用本地模拟数据作为降级方案
    this.loadEquipmentFallback()
  } finally {
    this.setData({ loading: false })
    wx.stopPullDownRefresh()
  }
}
```

### 2. 修改设备详情加载
**文件**: `pages/equipment-detail/equipment-detail.js`
**位置**: 第110-150行 `loadEquipmentDetail` 方法

**替换为**:
```javascript
async loadEquipmentDetail(equipmentId) {
  if (!this.checkAuth()) return

  this.setData({ loading: true })

  try {
    const response = await request.get(`/api/equipment/detail/${equipmentId}`)
    
    if (!response || !response.data) {
      throw new Error('设备不存在')
    }

    const equipment = response.data
    
    // 转换规格数据为数组格式
    const specsArray = Object.entries(equipment.specs || {}).map(([key, value]) => ({
      key,
      label: this.getSpecLabel(key),
      value
    }))

    this.setData({
      equipmentDetail: equipment,
      specsArray,
      accessories: equipment.accessories || this.data.accessories,
      rentalNotices: equipment.rentalNotices || this.data.rentalNotices
    })

    console.log('设备详情加载完成：', equipment.name)
  } catch (error) {
    console.error('加载设备详情失败：', error)
    wx.showToast({
      title: '加载失败',
      icon: 'error'
    })
    
    // 返回上一页
    setTimeout(() => {
      wx.navigateBack()
    }, 1500)
  } finally {
    this.setData({ loading: false })
  }
}
```

### 3. 修改收藏功能
**文件**: `pages/equipment-detail/equipment-detail.js`
**位置**: 第231-240行 `toggleFavorite` 方法

**替换为**:
```javascript
async toggleFavorite() {
  if (!this.checkAuth()) return

  const equipment = this.data.equipmentDetail
  if (!equipment) return

  try {
    const action = this.data.isFavorite ? 'remove' : 'add'
    
    const response = await request.post('/api/equipment/favorite', {
      equipmentId: equipment.id,
      action: action
    })

    if (response && response.data) {
      this.setData({
        isFavorite: response.data.isFavorite
      })

      wx.showToast({
        title: response.data.isFavorite ? '已收藏' : '已取消收藏',
        icon: 'success'
      })
    }
  } catch (error) {
    console.error('收藏操作失败：', error)
    wx.showToast({
      title: '操作失败',
      icon: 'error'
    })
  }
}
```

### 4. 添加筛选和排序的服务端支持
**文件**: `pages/equipment/equipment.js`
**需要修改**: `applyFilterAndSort` 方法改为重新请求接口

**替换为**:
```javascript
async applyFilterAndSort() {
  this.setData({ loading: true })
  
  try {
    const params = {
      page: 1,
      limit: 50,
      filter: this.data.currentFilter,
      sort: this.data.currentSort
    }
    
    const response = await request.get('/api/equipment/list', params)
    
    if (response && response.data) {
      this.setData({ 
        allEquipment: response.data.list,
        filteredEquipment: response.data.list
      })
    }
  } catch (error) {
    console.error('筛选排序失败：', error)
    // 使用本地筛选作为降级方案
    this.applyFilterAndSortLocal()
  } finally {
    this.setData({ loading: false })
  }
}
```

## 错误处理和降级方案

### 降级方案实现
```javascript
// 设备列表降级方案
loadEquipmentFallback() {
  const fallbackData = mockEquipment
  this.setData({ 
    allEquipment: fallbackData,
    filteredEquipment: fallbackData
  })
}

// 本地筛选排序降级方案
applyFilterAndSortLocal() {
  let filtered = [...this.data.allEquipment]
  
  // 应用筛选逻辑...
  // 应用排序逻辑...
  
  this.setData({ filteredEquipment: filtered })
}
```

## 注意事项

1. **分页加载**: 设备列表支持分页，避免一次加载过多数据
2. **图片优化**: 设备图片需要支持多尺寸和懒加载
3. **实时库存**: 设备可用性需要实时更新
4. **缓存策略**: 设备详情可以适当缓存，减少重复请求
5. **搜索优化**: 支持设备名称、品牌、型号等多字段搜索
6. **收藏同步**: 收藏状态需要在列表页和详情页之间同步

## 测试建议

1. 测试设备列表的加载、筛选、排序功能
2. 测试设备详情页的数据展示
3. 测试收藏功能的添加和取消
4. 测试网络异常时的降级方案
5. 测试设备可用性检查
6. 测试从设备详情到租赁流程的跳转
