// pages/orders/orders.js
const app = getApp()
const auth = require('../../utils/auth.js')
const orderManager = require('../../utils/orderManager.js')

Page({

  /**
   * 页面的初始数据
   */
  data: {
    // 订单列表
    orders: [],
    filteredOrders: [],

    // 筛选状态
    currentTab: 'all', // all, ongoing, completed, cancelled
    tabs: [
      { key: 'all', name: '全部' },
      { key: 'ongoing', name: '进行中' },
      { key: 'completed', name: '已完成' },
      { key: 'cancelled', name: '已取消' }
    ],

    // 页面状态
    loading: true,
    refreshing: false
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    console.log('订单列表页面加载')
    this.checkAuth()
    this.loadOrders()
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    // 刷新订单状态
    this.refreshOrders()
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {
    this.refreshOrders()
  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {

  },

  /**
   * 检查认证状态
   */
  checkAuth() {
    if (!auth.checkLoginStatus()) {
      auth.redirectToLogin('/pages/orders/orders')
      return false
    }
    return true
  },

  /**
   * 加载订单列表
   */
  async loadOrders() {
    this.setData({ loading: true })

    try {
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 1000))

      // 获取用户实际创建的订单
      const userOrders = orderManager.getUserOrders()

      // 合并模拟订单数据（用于演示）
      const mockOrders = [
        {
          id: 'XY20241201001',
          equipmentName: 'DJI Air 3 租赁',
          equipmentImage: 'http://iph.href.lu/300x200',
          locationName: '书圣故里',
          startTime: '2024-12-01 14:00',
          duration: 2,
          status: 'ongoing',
          statusText: '进行中',
          totalAmount: 175,
          createTime: '2024-01-14 20:30',
          remainingTime: '1小时15分',
          progress: 35
        },
        {
          id: 'XY20241130002',
          equipmentName: 'DJI Mini 4 Pro 租赁',
          equipmentImage: 'http://iph.href.lu/300x200',
          locationName: '鲁迅故里',
          startTime: '2024-11-30 09:00',
          duration: 3,
          status: 'completed',
          statusText: '已完成',
          totalAmount: 195,
          createTime: '2024-01-11 16:20',
          photoCount: 15,
          videoCount: 3,
          rated: false
        },
        {
          id: 'XY20241128003',
          equipmentName: 'DJI Air 3 租赁',
          equipmentImage: 'http://iph.href.lu/300x200',
          locationName: '兰亭景区',
          startTime: '2024-11-28 16:00',
          duration: 1,
          status: 'completed',
          statusText: '已完成',
          totalAmount: 95,
          createTime: '2024-01-09 19:45',
          photoCount: 8,
          videoCount: 1,
          rated: true
        },
        {
          id: 'XY20241125004',
          equipmentName: 'DJI Mavic 3 租赁',
          equipmentImage: 'http://iph.href.lu/300x200',
          locationName: '东湖景区',
          startTime: '2024-11-25 10:00',
          duration: 2,
          status: 'cancelled',
          statusText: '已取消',
          totalAmount: 255,
          createTime: '2024-01-08 15:30',
          cancelReason: '因天气原因取消，已全额退款'
        }
      ]

      // 转换用户订单格式以匹配页面显示需求
      const formattedUserOrders = userOrders.map(order => ({
        id: order.id,
        equipmentId: order.equipmentId, // 添加设备ID
        equipmentName: `${order.equipmentName} 租赁`,
        equipmentImage: 'http://iph.href.lu/300x200',
        locationName: order.locationName,
        startTime: order.startTime.substring(0, 16), // 格式化时间显示
        duration: order.duration,
        status: order.status,
        statusText: order.statusText,
        totalAmount: order.totalAmount,
        createTime: order.orderTime.substring(0, 16),
        canControl: order.canControl,
        canCancel: order.canCancel,
        canReview: order.canReview,
        remainingTime: order.remainingTime,
        progress: order.progress,
        // 拍摄统计
        photoCount: order.photoCount || 0,
        videoCount: order.videoCount || 0,
        totalWorks: order.totalWorks || 0
      }))

      // 合并用户订单和模拟订单，用户订单在前
      const allOrders = [...formattedUserOrders, ...mockOrders]

      console.log('加载订单完成：', {
        userOrders: formattedUserOrders.length,
        mockOrders: mockOrders.length,
        total: allOrders.length
      })

      this.setData({ orders: allOrders })
      this.filterOrders()

    } catch (error) {
      console.error('加载订单失败：', error)
      wx.showToast({
        title: '加载失败',
        icon: 'error'
      })
    } finally {
      this.setData({ loading: false })
    }
  },

  /**
   * 刷新订单
   */
  async refreshOrders() {
    this.setData({ refreshing: true })
    await this.loadOrders()
    this.setData({ refreshing: false })
    wx.stopPullDownRefresh()
  },

  /**
   * 切换标签
   */
  switchTab(e) {
    const tab = e.currentTarget.dataset.tab
    this.setData({ currentTab: tab })
    this.filterOrders()
  },

  /**
   * 筛选订单
   */
  filterOrders() {
    const { orders, currentTab } = this.data
    let filteredOrders = [...orders]

    if (currentTab !== 'all') {
      filteredOrders = orders.filter(order => order.status === currentTab)
    }

    this.setData({ filteredOrders })
  },

  /**
   * 查看订单详情
   */
  viewOrderDetail(e) {
    const orderId = e.currentTarget.dataset.orderId
    wx.navigateTo({
      url: `/pages/order-detail/order-detail?orderId=${orderId}`
    })
  },

  /**
   * 取消订单
   */
  cancelOrder(e) {
    const orderId = e.currentTarget.dataset.orderId

    wx.showModal({
      title: '取消订单',
      content: '确定要取消这个订单吗？',
      success: (res) => {
        if (res.confirm) {
          // 模拟取消订单
          const orders = this.data.orders.map(order => {
            if (order.id === orderId) {
              return { ...order, status: 'cancelled', statusText: '已取消' }
            }
            return order
          })

          // 同步更新本地存储中的用户订单
          this.updateUserOrderStatus(orderId, 'cancelled', '已取消')

          this.setData({ orders })
          this.filterOrders()

          wx.showToast({
            title: '订单已取消',
            icon: 'success'
          })
        }
      }
    })
  },

  /**
   * 更新本地存储中的用户订单状态
   */
  updateUserOrderStatus(orderId, status, statusText) {
    try {
      const userOrders = wx.getStorageSync('userOrders') || []
      const updatedOrders = userOrders.map(order => {
        if (order.id === orderId) {
          return { ...order, status, statusText }
        }
        return order
      })
      wx.setStorageSync('userOrders', updatedOrders)
      console.log('用户订单状态更新成功：', orderId, status)
    } catch (error) {
      console.error('更新用户订单状态失败：', error)
    }
  },

  /**
   * 继续控制
   */
  continueControl(e) {
    const order = e.currentTarget.dataset.order
    wx.navigateTo({
      url: `/pages/drone-control/drone-control?orderId=${order.id}&equipmentId=${order.equipmentId}`
    })
  },

  /**
   * 提前结束
   */
  endEarly(e) {
    const orderId = e.currentTarget.dataset.orderId

    wx.showModal({
      title: '提前结束',
      content: '确认提前结束租赁？剩余时间将按比例退款',
      success: (res) => {
        if (res.confirm) {
          wx.showToast({
            title: '租赁已结束，退款将在3-5个工作日内到账',
            icon: 'success',
            duration: 3000
          })

          // 更新订单状态
          const orders = this.data.orders.map(order => {
            if (order.id === orderId) {
              return { ...order, status: 'completed', statusText: '已完成' }
            }
            return order
          })

          // 同步更新本地存储中的用户订单
          this.updateUserOrderStatus(orderId, 'completed', '已完成')

          this.setData({ orders })
          this.filterOrders()
        }
      }
    })
  },

  /**
   * 查看作品集
   */
  viewGallery(e) {
    const orderId = e.currentTarget.dataset.orderId

    // 跳转到作品集页面，并传递订单ID进行筛选
    wx.navigateTo({
      url: `/pages/gallery/gallery?orderId=${orderId}`
    })
  },

  /**
   * 评价订单
   */
  rateOrder(e) {
    const orderId = e.currentTarget.dataset.orderId

    wx.showModal({
      title: '订单评价',
      content: '请为本次租赁体验评分',
      success: (res) => {
        if (res.confirm) {
          // 更新订单评价状态
          const orders = this.data.orders.map(order => {
            if (order.id === orderId) {
              return { ...order, rated: true }
            }
            return order
          })

          this.setData({ orders })
          this.filterOrders()

          wx.showToast({
            title: '感谢您的评价！',
            icon: 'success'
          })
        }
      }
    })
  },

  /**
   * 重新预订
   */
  reorder(e) {
    const orderId = e.currentTarget.dataset.orderId

    // 获取订单信息
    const order = this.data.filteredOrders.find(o => o.id === orderId)
    if (!order) {
      wx.showToast({
        title: '订单信息不存在',
        icon: 'error'
      })
      return
    }

    wx.showModal({
      title: '重新预订',
      content: `确认重新预订 ${order.equipmentName.replace(' 租赁', '')} 到 ${order.locationName}？`,
      success: (res) => {
        if (res.confirm) {
          // 跳转到订单确认页面，传递设备ID和位置信息
          wx.navigateTo({
            url: `/pages/order-confirm/order-confirm?equipmentId=${order.equipmentId}&locationName=${encodeURIComponent(order.locationName)}&reorder=true`
          })
        }
      }
    })
  },

  /**
   * 去租赁设备
   */
  goToEquipment() {
    wx.switchTab({
      url: '/pages/equipment/equipment'
    })
  },

  /**
   * 阻止事件冒泡
   */
  stopPropagation() {
    // 阻止事件冒泡
  }
})