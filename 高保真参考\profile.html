<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>我的 - 逍遥境</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body { height: 852px; overflow: hidden; }
        .status-bar {
            background: black;
            color: white;
            height: 47px;
            font-size: 14px;
            font-weight: 600;
        }
        .content-area {
            height: calc(852px - 47px - 70px);
            overflow-y: auto;
        }
        .tab-bar {
            height: 70px;
            border-top: 1px solid #e5e7eb;
        }
        .tab-item.active {
            color: #1f2937;
        }
        .tab-item {
            color: #9ca3af;
        }
        .menu-item {
            transition: all 0.3s ease;
        }
        .menu-item:hover {
            background: #f9fafb;
            transform: translateX(4px);
        }
        .gradient-bg {
            background: linear-gradient(135deg, #1f2937 0%, #374151 100%);
        }
        .balance-card {
            background: linear-gradient(135deg, #444746 0%, #50645e 100%);
        }
        .level-badge {
            background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
        }
    </style>
</head>
<body class="bg-gray-50">
    <!-- iOS 状态栏 -->
    <div class="status-bar flex justify-between items-center px-6">
        <div class="flex items-center space-x-1">
            <span>9:41</span>
        </div>
        <div class="flex items-center space-x-1">
            <i class="fas fa-signal text-xs"></i>
            <i class="fas fa-wifi text-xs"></i>
            <div class="w-6 h-3 border border-white rounded-sm">
                <div class="w-4 h-1 bg-white rounded-sm mt-0.5 ml-0.5"></div>
            </div>
        </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="content-area">
        <!-- 用户信息卡片 -->
        <div class="gradient-bg m-4 rounded-2xl p-6 text-white">
            <div class="flex items-center space-x-4">
                <div class="w-20 h-20 bg-white bg-opacity-20 rounded-full flex items-center justify-center">
                    <i class="fas fa-user text-2xl"></i>
                </div>
                <div class="flex-1">
                    <h2 class="text-xl font-bold mb-1">张先生</h2>
                    <p class="text-white text-opacity-80 text-sm mb-2">用户ID: 1001</p>
                    <div class="flex items-center space-x-2">
                        <span class="level-badge text-white px-3 py-1 rounded-full text-xs font-medium">
                            <i class="fas fa-crown mr-1"></i>白银会员
                        </span>
                        <span class="text-white text-opacity-60 text-xs">飞行时长: 12.5小时</span>
                    </div>
                </div>
                <div class="text-center">
                    <button onclick="editProfile()" class="bg-white bg-opacity-20 p-2 rounded-full">
                        <i class="fas fa-edit"></i>
                    </button>
                </div>
            </div>
        </div>

        <!-- 余额卡片 -->
        <div class="balance-card m-4 rounded-2xl p-5 text-white">
            <div class="flex items-center justify-between mb-4">
                <div>
                    <h3 class="text-lg font-semibold mb-1">账户余额</h3>
                    <div class="text-3xl font-bold">¥150.00</div>
                    <p class="text-white text-opacity-80 text-sm">可租赁时长: 约2小时</p>
                </div>
                <div class="text-center">
                    <i class="fas fa-wallet text-3xl mb-2 opacity-80"></i>
                    <div class="text-xs opacity-80">电子钱包</div>
                </div>
            </div>
            <div class="flex space-x-3">
                <button onclick="recharge()" class="flex-1 bg-white bg-opacity-20 py-2 rounded-xl text-sm font-medium hover:bg-opacity-30 transition-colors">
                    <i class="fas fa-plus mr-1"></i>充值
                </button>
                <button onclick="viewTransactions()" class="bg-white bg-opacity-20 px-4 py-2 rounded-xl text-sm font-medium hover:bg-opacity-30 transition-colors">
                    <i class="fas fa-list mr-1"></i>明细
                </button>
            </div>
        </div>

        <!-- 统计信息 -->
        <div class="mx-4 mb-4">
            <div class="grid grid-cols-4 gap-4">
                <div class="bg-white rounded-xl p-4 text-center shadow-sm">
                    <div class="text-2xl font-bold text-gray-800">23</div>
                    <div class="text-xs text-gray-500 mt-1">总订单</div>
                </div>
                <div class="bg-white rounded-xl p-4 text-center shadow-sm">
                    <div class="text-2xl font-bold text-gray-800">156</div>
                    <div class="text-xs text-gray-500 mt-1">拍摄作品</div>
                </div>
                <div class="bg-white rounded-xl p-4 text-center shadow-sm">
                    <div class="text-2xl font-bold text-gray-800">4.9</div>
                    <div class="text-xs text-gray-500 mt-1">平均评分</div>
                </div>
                <div class="bg-white rounded-xl p-4 text-center shadow-sm">
                    <div class="text-2xl font-bold text-gray-800">8</div>
                    <div class="text-xs text-gray-500 mt-1">收藏地点</div>
                </div>
            </div>
        </div>

        <!-- 功能菜单 -->
        <div class="bg-white m-4 rounded-2xl shadow-sm">
            <div class="p-4 border-b border-gray-100">
                <h3 class="text-lg font-semibold text-gray-800">我的服务</h3>
            </div>
            
            <div class="space-y-1">
                <div class="menu-item flex items-center justify-between p-4 cursor-pointer" onclick="viewGallery()">
                    <div class="flex items-center space-x-3">
                        <div class="w-10 h-10 bg-blue-100 rounded-xl flex items-center justify-center">
                            <i class="fas fa-camera text-blue-600"></i>
                        </div>
                        <span class="font-medium text-gray-800">我的作品集</span>
                    </div>
                    <div class="flex items-center space-x-2">
                        <span class="text-sm text-gray-500">156张作品</span>
                        <i class="fas fa-chevron-right text-gray-400"></i>
                    </div>
                </div>

                <div class="menu-item flex items-center justify-between p-4 cursor-pointer" onclick="viewFavorites()">
                    <div class="flex items-center space-x-3">
                        <div class="w-10 h-10 bg-red-100 rounded-xl flex items-center justify-center">
                            <i class="fas fa-heart text-red-600"></i>
                        </div>
                        <span class="font-medium text-gray-800">收藏夹</span>
                    </div>
                    <div class="flex items-center space-x-2">
                        <span class="text-sm text-gray-500">8个地点</span>
                        <i class="fas fa-chevron-right text-gray-400"></i>
                    </div>
                </div>

                <div class="menu-item flex items-center justify-between p-4 cursor-pointer" onclick="viewCoupons()">
                    <div class="flex items-center space-x-3">
                        <div class="w-10 h-10 bg-yellow-100 rounded-xl flex items-center justify-center">
                            <i class="fas fa-ticket-alt text-yellow-600"></i>
                        </div>
                        <span class="font-medium text-gray-800">优惠券</span>
                    </div>
                    <div class="flex items-center space-x-2">
                        <span class="text-sm text-gray-500">3张可用</span>
                        <i class="fas fa-chevron-right text-gray-400"></i>
                    </div>
                </div>

                <div class="menu-item flex items-center justify-between p-4 cursor-pointer" onclick="viewMembership()">
                    <div class="flex items-center space-x-3">
                        <div class="w-10 h-10 bg-purple-100 rounded-xl flex items-center justify-center">
                            <i class="fas fa-crown text-purple-600"></i>
                        </div>
                        <span class="font-medium text-gray-800">会员特权</span>
                    </div>
                    <div class="flex items-center space-x-2">
                        <span class="text-sm text-gray-500">白银会员</span>
                        <i class="fas fa-chevron-right text-gray-400"></i>
                    </div>
                </div>
            </div>
        </div>

        <!-- 设置菜单 -->
        <div class="bg-white m-4 rounded-2xl shadow-sm">
            <div class="p-4 border-b border-gray-100">
                <h3 class="text-lg font-semibold text-gray-800">设置</h3>
            </div>
            
            <div class="space-y-1">
                <div class="menu-item flex items-center justify-between p-4 cursor-pointer" onclick="accountSettings()">
                    <div class="flex items-center space-x-3">
                        <div class="w-10 h-10 bg-gray-100 rounded-xl flex items-center justify-center">
                            <i class="fas fa-user-cog text-gray-600"></i>
                        </div>
                        <span class="font-medium text-gray-800">账户设置</span>
                    </div>
                    <i class="fas fa-chevron-right text-gray-400"></i>
                </div>

                <div class="menu-item flex items-center justify-between p-4 cursor-pointer" onclick="notifications()">
                    <div class="flex items-center space-x-3">
                        <div class="w-10 h-10 bg-green-100 rounded-xl flex items-center justify-center">
                            <i class="fas fa-bell text-green-600"></i>
                        </div>
                        <span class="font-medium text-gray-800">消息通知</span>
                    </div>
                    <i class="fas fa-chevron-right text-gray-400"></i>
                </div>

                <div class="menu-item flex items-center justify-between p-4 cursor-pointer" onclick="privacy()">
                    <div class="flex items-center space-x-3">
                        <div class="w-10 h-10 bg-indigo-100 rounded-xl flex items-center justify-center">
                            <i class="fas fa-shield-alt text-indigo-600"></i>
                        </div>
                        <span class="font-medium text-gray-800">隐私安全</span>
                    </div>
                    <i class="fas fa-chevron-right text-gray-400"></i>
                </div>

                <div class="menu-item flex items-center justify-between p-4 cursor-pointer" onclick="help()">
                    <div class="flex items-center space-x-3">
                        <div class="w-10 h-10 bg-orange-100 rounded-xl flex items-center justify-center">
                            <i class="fas fa-question-circle text-orange-600"></i>
                        </div>
                        <span class="font-medium text-gray-800">帮助中心</span>
                    </div>
                    <i class="fas fa-chevron-right text-gray-400"></i>
                </div>

                <div class="menu-item flex items-center justify-between p-4 cursor-pointer" onclick="about()">
                    <div class="flex items-center space-x-3">
                        <div class="w-10 h-10 bg-teal-100 rounded-xl flex items-center justify-center">
                            <i class="fas fa-info-circle text-teal-600"></i>
                        </div>
                        <span class="font-medium text-gray-800">关于逍遥境</span>
                    </div>
                    <div class="flex items-center space-x-2">
                        <span class="text-sm text-gray-500">v1.0.0</span>
                        <i class="fas fa-chevron-right text-gray-400"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 底部导航栏 -->
    <div class="tab-bar bg-white flex items-center justify-around">
        <div class="tab-item text-center" onclick="navigateTo('home')">
            <i class="fas fa-home text-xl mb-1"></i>
            <div class="text-xs">首页</div>
        </div>
        <div class="tab-item text-center" onclick="navigateTo('equipment')">
            <i class="fas fa-helicopter text-xl mb-1"></i>
            <div class="text-xs">设备</div>
        </div>
        <div class="tab-item text-center" onclick="navigateTo('orders')">
            <i class="fas fa-file-alt text-xl mb-1"></i>
            <div class="text-xs">订单</div>
        </div>
        <div class="tab-item active text-center">
            <i class="fas fa-user text-xl mb-1"></i>
            <div class="text-xs">我的</div>
        </div>
    </div>

    <script>
        function editProfile() {
            alert('编辑个人资料\n• 头像设置\n• 昵称修改\n• 手机号绑定');
        }

        function recharge() {
            alert('正在跳转到充值页面...');
            setTimeout(() => {
                window.parent.postMessage({type: 'navigate', page: 'recharge'}, '*');
            }, 500);
        }

        function viewTransactions() {
            alert('交易明细\n• 充值记录\n• 消费记录\n• 退款记录');
        }

        function viewGallery() {
            alert('正在跳转到作品集页面...');
            // 模拟跳转到作品集页面
        }

        function viewFavorites() {
            alert('我的收藏\n• 书圣故里\n• 鲁迅故里\n• 兰亭景区\n• 沈园\n• 东湖景区\n等8个地点');
        }

        function viewCoupons() {
            alert('我的优惠券\n• 首次租赁9折券\n• 满200减30券\n• 周末特惠券');
        }

        function viewMembership() {
            alert('会员特权\n• 白银会员：9.5折优惠\n• 专属客服\n• 优先预订\n\n升级到黄金会员享受更多特权！');
        }

        function accountSettings() {
            alert('账户设置\n• 修改密码\n• 实名认证\n• 绑定邮箱\n• 注销账户');
        }

        function notifications() {
            alert('消息通知设置\n• 订单状态通知: 开启\n• 优惠活动通知: 开启\n• 系统消息通知: 开启');
        }

        function privacy() {
            alert('隐私安全\n• 隐私设置\n• 数据管理\n• 安全中心\n• 用户协议');
        }

        function help() {
            alert('帮助中心\n• 常见问题\n• 使用教程\n• 联系客服\n• 意见反馈');
        }

        function about() {
            alert('关于逍遥境\n版本：v1.0.0\n\n专业无人机租赁平台\n让每个人都能体验航拍乐趣\n\n© 2024 逍遥境科技有限公司');
        }

        function navigateTo(page) {
            const tabs = document.querySelectorAll('.tab-item');
            tabs.forEach(tab => tab.classList.remove('active'));
            
            setTimeout(() => {
                window.parent.postMessage({type: 'navigate', page: page}, '*');
            }, 200);
        }
    </script>
</body>
</html> 