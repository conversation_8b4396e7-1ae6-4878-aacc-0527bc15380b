<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>我的订单 - 逍遥境</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body { height: 852px; overflow: hidden; }
        .status-bar {
            background: black;
            color: white;
            height: 47px;
            font-size: 14px;
            font-weight: 600;
        }
        .content-area {
            height: calc(852px - 47px - 70px);
            overflow-y: auto;
        }
        .tab-bar {
            height: 70px;
            border-top: 1px solid #e5e7eb;
        }
        .tab-item.active {
            color: #1f2937;
        }
        .tab-item {
            color: #9ca3af;
        }
        .order-card {
            transition: all 0.3s ease;
        }
        .order-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.1);
        }
        .status-active {
            background: linear-gradient(135deg, #10b981, #059669);
        }
        .status-completed {
            background: linear-gradient(135deg, #6b7280, #4b5563);
        }
        .status-cancelled {
            background: linear-gradient(135deg, #ef4444, #dc2626);
        }
        .tab-filter.active {
            background: #1f2937;
            color: white;
        }
    </style>
</head>
<body class="bg-gray-50">
    <!-- iOS 状态栏 -->
    <div class="status-bar flex justify-between items-center px-6">
        <div class="flex items-center space-x-1">
            <span>9:41</span>
        </div>
        <div class="flex items-center space-x-1">
            <i class="fas fa-signal text-xs"></i>
            <i class="fas fa-wifi text-xs"></i>
            <div class="w-6 h-3 border border-white rounded-sm">
                <div class="w-4 h-1 bg-white rounded-sm mt-0.5 ml-0.5"></div>
            </div>
        </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="content-area">
        <!-- 顶部导航 -->
        <div class="bg-white px-6 py-4 shadow-sm">
            <h1 class="text-2xl font-bold text-gray-800">我的订单</h1>
        </div>

        <!-- 订单状态筛选 -->
        <div class="bg-white px-6 py-4 border-b border-gray-100">
            <div class="flex space-x-3 overflow-x-auto">
                <button class="tab-filter active px-4 py-2 rounded-full text-sm whitespace-nowrap border border-gray-300" onclick="filterOrders('all')">
                    全部订单
                </button>
                <button class="tab-filter px-4 py-2 rounded-full text-sm whitespace-nowrap border border-gray-300" onclick="filterOrders('active')">
                    进行中
                </button>
                <button class="tab-filter px-4 py-2 rounded-full text-sm whitespace-nowrap border border-gray-300" onclick="filterOrders('completed')">
                    已完成
                </button>
                <button class="tab-filter px-4 py-2 rounded-full text-sm whitespace-nowrap border border-gray-300" onclick="filterOrders('cancelled')">
                    已取消
                </button>
            </div>
        </div>

        <!-- 订单列表 -->
        <div class="px-4 py-4 space-y-4">
            <!-- 当前活跃订单 -->
            <div class="order-card bg-white rounded-2xl p-5 shadow-sm border-l-4 border-green-500" data-status="active">
                <div class="flex items-start justify-between mb-3">
                    <div>
                        <h3 class="text-lg font-semibold text-gray-800">DJI Air 3 租赁</h3>
                        <p class="text-sm text-gray-500">订单号: XY20241201001</p>
                    </div>
                    <span class="status-active text-white px-3 py-1 rounded-full text-xs font-medium">
                        <i class="fas fa-circle text-xs mr-1"></i>进行中
                    </span>
                </div>

                <div class="flex items-center space-x-4 mb-4">
                    <img src="https://images.unsplash.com/photo-1473968512647-3e447244af8f?w=300&h=200&fit=crop" 
                         alt="DJI Air 3" class="w-16 h-12 object-cover rounded-lg">
                    <div class="flex-1">
                        <div class="text-sm text-gray-600 space-y-1">
                            <div><i class="fas fa-map-marker-alt w-4"></i> 书圣故里</div>
                            <div><i class="fas fa-clock w-4"></i> 2024-12-01 14:00 (2小时)</div>
                            <div><i class="fas fa-yen-sign w-4"></i> ¥175</div>
                        </div>
                    </div>
                </div>

                <div class="bg-green-50 rounded-xl p-3 mb-4">
                    <div class="flex items-center justify-between text-sm">
                        <span class="text-green-700 font-medium">
                            <i class="fas fa-play-circle mr-1"></i>正在操控中
                        </span>
                        <span class="text-green-600">剩余时间: 1小时15分</span>
                    </div>
                    <div class="mt-2 bg-green-200 rounded-full h-2">
                        <div class="bg-green-500 h-2 rounded-full" style="width: 35%"></div>
                    </div>
                </div>

                <div class="flex space-x-3">
                    <button onclick="continueControl()" class="flex-1 bg-green-600 text-white py-2 rounded-xl text-sm font-medium hover:bg-green-700 transition-colors">
                        继续操控
                    </button>
                    <button onclick="endEarly()" class="bg-gray-100 text-gray-700 px-4 py-2 rounded-xl text-sm font-medium hover:bg-gray-200 transition-colors">
                        提前结束
                    </button>
                </div>
            </div>

            <!-- 已完成订单1 -->
            <div class="order-card bg-white rounded-2xl p-5 shadow-sm" data-status="completed">
                <div class="flex items-start justify-between mb-3">
                    <div>
                        <h3 class="text-lg font-semibold text-gray-800">DJI Mini 4 Pro 租赁</h3>
                        <p class="text-sm text-gray-500">订单号: XY20241130002</p>
                    </div>
                    <span class="status-completed text-white px-3 py-1 rounded-full text-xs font-medium">
                        已完成
                    </span>
                </div>

                <div class="flex items-center space-x-4 mb-4">
                    <img src="https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=300&h=200&fit=crop" 
                         alt="DJI Mini 4 Pro" class="w-16 h-12 object-cover rounded-lg">
                    <div class="flex-1">
                        <div class="text-sm text-gray-600 space-y-1">
                            <div><i class="fas fa-map-marker-alt w-4"></i> 鲁迅故里</div>
                            <div><i class="fas fa-clock w-4"></i> 2024-11-30 09:00 (3小时)</div>
                            <div><i class="fas fa-yen-sign w-4"></i> ¥195</div>
                        </div>
                    </div>
                </div>

                <div class="bg-gray-50 rounded-xl p-3 mb-4">
                    <div class="flex items-center justify-between text-sm text-gray-600">
                        <span><i class="fas fa-camera mr-1"></i> 已拍摄 15张照片</span>
                        <span><i class="fas fa-video mr-1"></i> 已录制 3段视频</span>
                    </div>
                </div>

                <div class="flex space-x-3">
                    <button onclick="viewGallery('XY20241130002')" class="flex-1 bg-gray-800 text-white py-2 rounded-xl text-sm font-medium hover:bg-gray-700 transition-colors">
                        查看作品
                    </button>
                    <button onclick="rateOrder('XY20241130002')" class="bg-yellow-500 text-white px-4 py-2 rounded-xl text-sm font-medium hover:bg-yellow-600 transition-colors">
                        评价
                    </button>
                    <button onclick="reorder('XY20241130002')" class="bg-blue-600 text-white px-4 py-2 rounded-xl text-sm font-medium hover:bg-blue-700 transition-colors">
                        再次预订
                    </button>
                </div>
            </div>

            <!-- 已完成订单2 -->
            <div class="order-card bg-white rounded-2xl p-5 shadow-sm" data-status="completed">
                <div class="flex items-start justify-between mb-3">
                    <div>
                        <h3 class="text-lg font-semibold text-gray-800">DJI Air 3 租赁</h3>
                        <p class="text-sm text-gray-500">订单号: XY20241128003</p>
                    </div>
                    <span class="status-completed text-white px-3 py-1 rounded-full text-xs font-medium">
                        已完成
                    </span>
                </div>

                <div class="flex items-center space-x-4 mb-4">
                    <img src="https://images.unsplash.com/photo-1473968512647-3e447244af8f?w=300&h=200&fit=crop" 
                         alt="DJI Air 3" class="w-16 h-12 object-cover rounded-lg">
                    <div class="flex-1">
                        <div class="text-sm text-gray-600 space-y-1">
                            <div><i class="fas fa-map-marker-alt w-4"></i> 兰亭景区</div>
                            <div><i class="fas fa-clock w-4"></i> 2024-11-28 16:00 (1小时)</div>
                            <div><i class="fas fa-yen-sign w-4"></i> ¥95</div>
                        </div>
                    </div>
                </div>

                <div class="bg-gray-50 rounded-xl p-3 mb-4">
                    <div class="flex items-center justify-between text-sm text-gray-600">
                        <span><i class="fas fa-camera mr-1"></i> 已拍摄 8张照片</span>
                        <span><i class="fas fa-video mr-1"></i> 已录制 1段视频</span>
                    </div>
                </div>

                <div class="flex space-x-3">
                    <button onclick="viewGallery('XY20241128003')" class="flex-1 bg-gray-800 text-white py-2 rounded-xl text-sm font-medium hover:bg-gray-700 transition-colors">
                        查看作品
                    </button>
                    <button class="bg-gray-300 text-gray-500 px-4 py-2 rounded-xl text-sm font-medium cursor-not-allowed">
                        已评价
                    </button>
                    <button onclick="reorder('XY20241128003')" class="bg-blue-600 text-white px-4 py-2 rounded-xl text-sm font-medium hover:bg-blue-700 transition-colors">
                        再次预订
                    </button>
                </div>
            </div>

            <!-- 已取消订单 -->
            <div class="order-card bg-white rounded-2xl p-5 shadow-sm opacity-75" data-status="cancelled">
                <div class="flex items-start justify-between mb-3">
                    <div>
                        <h3 class="text-lg font-semibold text-gray-800">DJI Mavic 3 租赁</h3>
                        <p class="text-sm text-gray-500">订单号: XY20241125004</p>
                    </div>
                    <span class="status-cancelled text-white px-3 py-1 rounded-full text-xs font-medium">
                        已取消
                    </span>
                </div>

                <div class="flex items-center space-x-4 mb-4">
                    <img src="https://images.unsplash.com/photo-1508614999368-9260051292e5?w=300&h=200&fit=crop" 
                         alt="DJI Mavic 3" class="w-16 h-12 object-cover rounded-lg">
                    <div class="flex-1">
                        <div class="text-sm text-gray-600 space-y-1">
                            <div><i class="fas fa-map-marker-alt w-4"></i> 东湖景区</div>
                            <div><i class="fas fa-clock w-4"></i> 2024-11-25 10:00 (2小时)</div>
                            <div><i class="fas fa-yen-sign w-4"></i> ¥255</div>
                        </div>
                    </div>
                </div>

                <div class="bg-red-50 rounded-xl p-3 mb-4">
                    <p class="text-sm text-red-700">
                        <i class="fas fa-exclamation-circle mr-1"></i>
                        因天气原因取消，已全额退款
                    </p>
                </div>

                <div class="flex space-x-3">
                    <button onclick="reorder('XY20241125004')" class="flex-1 bg-blue-600 text-white py-2 rounded-xl text-sm font-medium hover:bg-blue-700 transition-colors">
                        重新预订
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 底部导航栏 -->
    <div class="tab-bar bg-white flex items-center justify-around">
        <div class="tab-item text-center" onclick="navigateTo('home')">
            <i class="fas fa-home text-xl mb-1"></i>
            <div class="text-xs">首页</div>
        </div>
        <div class="tab-item text-center" onclick="navigateTo('equipment')">
            <i class="fas fa-helicopter text-xl mb-1"></i>
            <div class="text-xs">设备</div>
        </div>
        <div class="tab-item active text-center">
            <i class="fas fa-file-alt text-xl mb-1"></i>
            <div class="text-xs">订单</div>
        </div>
        <div class="tab-item text-center" onclick="navigateTo('profile')">
            <i class="fas fa-user text-xl mb-1"></i>
            <div class="text-xs">我的</div>
        </div>
    </div>

    <script>
        function filterOrders(status) {
            // 更新按钮状态
            document.querySelectorAll('.tab-filter').forEach(btn => btn.classList.remove('active'));
            event.target.classList.add('active');

            // 筛选订单卡片
            document.querySelectorAll('.order-card').forEach(card => {
                if (status === 'all' || card.dataset.status === status) {
                    card.style.display = 'block';
                } else {
                    card.style.display = 'none';
                }
            });
        }

        function continueControl() {
            alert('正在连接无人机...\n即将跳转到操控界面');
        }

        function endEarly() {
            if (confirm('确认提前结束租赁？\n剩余时间将按比例退款')) {
                alert('租赁已结束，退款将在3-5个工作日内到账');
            }
        }

        function viewGallery(orderId) {
            alert(`查看订单 ${orderId} 的拍摄作品\n正在跳转到作品集...`);
        }

        function rateOrder(orderId) {
            const rating = prompt('请为本次租赁体验评分 (1-5星):');
            if (rating && rating >= 1 && rating <= 5) {
                alert(`感谢您的 ${rating} 星评价！`);
            }
        }

        function reorder(orderId) {
            if (confirm('确认重新预订相同的设备和地点？')) {
                alert('正在跳转到预订页面...');
            }
        }

        function navigateTo(page) {
            const tabs = document.querySelectorAll('.tab-item');
            tabs.forEach(tab => tab.classList.remove('active'));
            
            setTimeout(() => {
                window.parent.postMessage({type: 'navigate', page: page}, '*');
            }, 200);
        }
    </script>
</body>
</html> 