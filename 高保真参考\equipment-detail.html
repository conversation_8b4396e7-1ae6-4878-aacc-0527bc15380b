<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>设备详情 - 逍遥境</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body { height: 852px; overflow: hidden; }
        .status-bar {
            background: black;
            color: white;
            height: 47px;
            font-size: 14px;
            font-weight: 600;
        }
        .content-area {
            height: calc(852px - 47px);
            overflow-y: auto;
        }
        .spec-item {
            transition: all 0.3s ease;
        }
        .spec-item:hover {
            background: #f9fafb;
        }
        .rent-btn {
            background: linear-gradient(135deg, #1f2937 0%, #374151 100%);
            transition: all 0.3s ease;
        }
        .rent-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(31, 41, 55, 0.3);
        }
    </style>
</head>
<body class="bg-gray-50">
    <!-- iOS 状态栏 -->
    <div class="status-bar flex justify-between items-center px-6">
        <div class="flex items-center space-x-1">
            <span>9:41</span>
        </div>
        <div class="flex items-center space-x-1">
            <i class="fas fa-signal text-xs"></i>
            <i class="fas fa-wifi text-xs"></i>
            <div class="w-6 h-3 border border-white rounded-sm">
                <div class="w-4 h-1 bg-white rounded-sm mt-0.5 ml-0.5"></div>
            </div>
        </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="content-area relative">
        <!-- 设备图片区域 -->
        <div class="relative">
            <img src="https://images.unsplash.com/photo-1473968512647-3e447244af8f?w=600&h=400&fit=crop" 
                 alt="DJI Air 3" class="w-full h-80 object-cover">
            
            <!-- 返回按钮 -->
            <button onclick="goBack()" class="absolute top-4 left-4 w-10 h-10 bg-black bg-opacity-50 rounded-full flex items-center justify-center text-white">
                <i class="fas fa-chevron-left"></i>
            </button>
            
            <!-- 收藏按钮 -->
            <button onclick="toggleFavorite()" class="absolute top-4 right-4 w-10 h-10 bg-black bg-opacity-50 rounded-full flex items-center justify-center text-white">
                <i class="far fa-heart"></i>
            </button>

            <!-- 状态标签 -->
            <div class="absolute bottom-4 left-4">
                <span class="bg-green-500 text-white px-3 py-1 rounded-full text-sm font-medium">可租用</span>
            </div>
        </div>

        <!-- 设备信息区域 -->
        <div class="bg-white rounded-t-3xl -mt-6 relative z-10 px-6 py-6">
            <!-- 基本信息 -->
            <div class="mb-6">
                <h1 class="text-2xl font-bold text-gray-800 mb-2">DJI Air 3</h1>
                <p class="text-gray-500 mb-4">双主摄无人机，专业级航拍体验</p>
                
                <div class="flex items-center justify-between mb-4">
                    <div class="flex items-center space-x-4">
                        <div class="flex items-center space-x-1">
                            <div class="flex text-yellow-400">
                                <i class="fas fa-star text-sm"></i>
                                <i class="fas fa-star text-sm"></i>
                                <i class="fas fa-star text-sm"></i>
                                <i class="fas fa-star text-sm"></i>
                                <i class="fas fa-star text-sm"></i>
                            </div>
                            <span class="text-sm text-gray-600">4.9 (127条评价)</span>
                        </div>
                    </div>
                    <div class="text-right">
                        <span class="text-3xl font-bold text-gray-800">¥80</span>
                        <span class="text-lg text-gray-500">/小时</span>
                    </div>
                </div>
            </div>

            <!-- 设备特点 -->
            <div class="mb-6">
                <h3 class="text-lg font-semibold text-gray-800 mb-3">设备特点</h3>
                <div class="grid grid-cols-2 gap-3">
                    <div class="bg-gray-50 rounded-xl p-3 text-center">
                        <i class="fas fa-video text-gray-600 text-xl mb-2"></i>
                        <div class="text-sm font-medium text-gray-800">4K/60fps</div>
                        <div class="text-xs text-gray-500">高清录制</div>
                    </div>
                    <div class="bg-gray-50 rounded-xl p-3 text-center">
                        <i class="fas fa-clock text-gray-600 text-xl mb-2"></i>
                        <div class="text-sm font-medium text-gray-800">46分钟</div>
                        <div class="text-xs text-gray-500">最大续航</div>
                    </div>
                    <div class="bg-gray-50 rounded-xl p-3 text-center">
                        <i class="fas fa-camera text-gray-600 text-xl mb-2"></i>
                        <div class="text-sm font-medium text-gray-800">双主摄</div>
                        <div class="text-xs text-gray-500">广角+中焦</div>
                    </div>
                    <div class="bg-gray-50 rounded-xl p-3 text-center">
                        <i class="fas fa-shield-alt text-gray-600 text-xl mb-2"></i>
                        <div class="text-sm font-medium text-gray-800">避障</div>
                        <div class="text-xs text-gray-500">全向感知</div>
                    </div>
                </div>
            </div>

            <!-- 技术规格 -->
            <div class="mb-6">
                <h3 class="text-lg font-semibold text-gray-800 mb-3">技术规格</h3>
                <div class="space-y-3">
                    <div class="spec-item flex justify-between items-center py-3 px-4 rounded-xl">
                        <span class="text-gray-600">重量</span>
                        <span class="font-medium text-gray-800">720g</span>
                    </div>
                    <div class="spec-item flex justify-between items-center py-3 px-4 rounded-xl">
                        <span class="text-gray-600">最大飞行速度</span>
                        <span class="font-medium text-gray-800">21m/s</span>
                    </div>
                    <div class="spec-item flex justify-between items-center py-3 px-4 rounded-xl">
                        <span class="text-gray-600">最大飞行高度</span>
                        <span class="font-medium text-gray-800">6000m</span>
                    </div>
                    <div class="spec-item flex justify-between items-center py-3 px-4 rounded-xl">
                        <span class="text-gray-600">图传距离</span>
                        <span class="font-medium text-gray-800">20km</span>
                    </div>
                    <div class="spec-item flex justify-between items-center py-3 px-4 rounded-xl">
                        <span class="text-gray-600">存储</span>
                        <span class="font-medium text-gray-800">8GB内置 + microSD卡</span>
                    </div>
                </div>
            </div>

            <!-- 包含配件 -->
            <div class="mb-6">
                <h3 class="text-lg font-semibold text-gray-800 mb-3">包含配件</h3>
                <div class="grid grid-cols-2 gap-2 text-sm">
                    <div class="flex items-center space-x-2">
                        <i class="fas fa-check text-green-500 text-xs"></i>
                        <span class="text-gray-600">无人机主体</span>
                    </div>
                    <div class="flex items-center space-x-2">
                        <i class="fas fa-check text-green-500 text-xs"></i>
                        <span class="text-gray-600">遥控器</span>
                    </div>
                    <div class="flex items-center space-x-2">
                        <i class="fas fa-check text-green-500 text-xs"></i>
                        <span class="text-gray-600">电池×3</span>
                    </div>
                    <div class="flex items-center space-x-2">
                        <i class="fas fa-check text-green-500 text-xs"></i>
                        <span class="text-gray-600">充电器</span>
                    </div>
                    <div class="flex items-center space-x-2">
                        <i class="fas fa-check text-green-500 text-xs"></i>
                        <span class="text-gray-600">螺旋桨×4</span>
                    </div>
                    <div class="flex items-center space-x-2">
                        <i class="fas fa-check text-green-500 text-xs"></i>
                        <span class="text-gray-600">携带箱</span>
                    </div>
                </div>
            </div>

            <!-- 用户评价 -->
            <div class="mb-20">
                <h3 class="text-lg font-semibold text-gray-800 mb-3">用户评价</h3>
                <div class="space-y-4">
                    <div class="bg-gray-50 rounded-xl p-4">
                        <div class="flex items-center space-x-3 mb-2">
                            <div class="w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center">
                                <i class="fas fa-user text-gray-500 text-sm"></i>
                            </div>
                            <div>
                                <div class="font-medium text-gray-800 text-sm">张先生</div>
                                <div class="flex text-yellow-400 text-xs">
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                </div>
                            </div>
                        </div>
                        <p class="text-sm text-gray-600">画质非常棒，操控简单，特别适合拍摄风景。续航时间也很给力，推荐！</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- 底部操作栏 -->
        <div class="fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 px-6 py-4">
            <div class="flex space-x-4">
                <button onclick="addToFavorites()" class="flex-1 bg-gray-100 text-gray-700 py-3 rounded-xl font-medium flex items-center justify-center space-x-2">
                    <i class="far fa-heart"></i>
                    <span>收藏</span>
                </button>
                <button onclick="rentNow()" class="rent-btn flex-2 text-white py-3 px-8 rounded-xl font-medium">
                    立即租用
                </button>
            </div>
        </div>
    </div>

    <script>
        function goBack() {
            window.history.back();
        }

        function toggleFavorite() {
            const heartIcon = document.querySelector('.fa-heart');
            if (heartIcon.classList.contains('far')) {
                heartIcon.classList.remove('far');
                heartIcon.classList.add('fas');
                heartIcon.style.color = '#ef4444';
            } else {
                heartIcon.classList.remove('fas');
                heartIcon.classList.add('far');
                heartIcon.style.color = '';
            }
        }

        function addToFavorites() {
            alert('已添加到收藏夹');
        }

        function rentNow() {
            if (confirm('确认租用 DJI Air 3？\n租金：¥80/小时')) {
                alert('正在跳转到地点选择页面...');
                // 模拟跳转到地点选择页
            }
        }
    </script>
</body>
</html> 