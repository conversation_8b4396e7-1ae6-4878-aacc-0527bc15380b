# 充值页面接口文档

## 页面概述
充值页面，用户可以选择充值金额、支付方式，完成余额充值操作。

## 当前实现分析

### 页面文件位置
- `pages/recharge/recharge.js` - 充值页逻辑
- `utils/auth.js` - 用户认证工具

### 当前功能流程
1. **余额显示**：显示用户当前余额和可用时长
2. **金额选择**：提供预设金额选项和自定义金额输入
3. **支付方式选择**：支持微信支付、支付宝等
4. **优惠计算**：计算充值优惠和赠送金额
5. **支付处理**：调用支付接口完成充值
6. **结果反馈**：显示充值结果并更新余额

## 需要替换的接口

### 1. 获取充值配置接口

#### 接口信息
- **接口名称**: 获取充值配置信息
- **请求方法**: GET
- **接口路径**: `/api/recharge/config`
- **当前模拟位置**: `pages/recharge/recharge.js` 第16-21行 `amountOptions` 数据

#### 请求头
```
Authorization: Bearer {token}
```

#### 响应数据
```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "amountOptions": [
      {
        "value": 50,
        "hours": "0.6",
        "bonus": 0,
        "popular": false
      },
      {
        "value": 100,
        "hours": "1.2",
        "bonus": 5,
        "popular": false
      },
      {
        "value": 200,
        "hours": "2.5",
        "bonus": 20,
        "popular": true
      },
      {
        "value": 500,
        "hours": "6",
        "bonus": 80,
        "popular": false
      }
    ],
    "paymentMethods": [
      {
        "type": "wechat",
        "name": "微信支付",
        "desc": "推荐使用，快速便捷",
        "icon": "💚",
        "colorClass": "green",
        "enabled": true,
        "fee": 0
      },
      {
        "type": "alipay",
        "name": "支付宝",
        "desc": "安全可靠，操作简单",
        "icon": "💙",
        "colorClass": "blue",
        "enabled": true,
        "fee": 0
      }
    ],
    "minAmount": 10,
    "maxAmount": 2000,
    "bonusRules": [
      {
        "minAmount": 100,
        "bonusPercent": 5,
        "description": "充值满100元赠送5%"
      },
      {
        "minAmount": 200,
        "bonusPercent": 10,
        "description": "充值满200元赠送10%"
      }
    ]
  }
}
```

### 2. 计算充值优惠接口

#### 接口信息
- **接口名称**: 计算充值金额的优惠和赠送
- **请求方法**: POST
- **接口路径**: `/api/recharge/calculate`
- **当前模拟位置**: `pages/recharge/recharge.js` 第180-236行 `calculateBonus` 方法

#### 请求头
```
Authorization: Bearer {token}
```

#### 请求参数
```json
{
  "amount": 200,
  "paymentMethod": "wechat"
}
```

#### 响应数据
```json
{
  "code": 200,
  "message": "计算成功",
  "data": {
    "originalAmount": 200,
    "bonusAmount": 20,
    "finalAmount": 220,
    "paymentFee": 0,
    "actualPayment": 200,
    "bonusReason": "充值满200元赠送10%",
    "availableHours": "2.7",
    "breakdown": [
      {
        "item": "充值金额",
        "amount": 200
      },
      {
        "item": "赠送金额",
        "amount": 20
      },
      {
        "item": "支付手续费",
        "amount": 0
      }
    ]
  }
}
```

### 3. 创建充值订单接口

#### 接口信息
- **接口名称**: 创建充值订单
- **请求方法**: POST
- **接口路径**: `/api/recharge/create-order`
- **当前模拟位置**: `pages/recharge/recharge.js` 第240-332行 `confirmRecharge` 方法

#### 请求头
```
Authorization: Bearer {token}
```

#### 请求参数
```json
{
  "amount": 200,
  "paymentMethod": "wechat",
  "bonusAmount": 20,
  "finalAmount": 220,
  "clientInfo": {
    "platform": "wechat_miniprogram",
    "version": "1.0.0",
    "deviceId": "device_001"
  }
}
```

#### 响应数据
```json
{
  "code": 200,
  "message": "订单创建成功",
  "data": {
    "rechargeOrderId": "recharge_1706345678901",
    "amount": 200,
    "bonusAmount": 20,
    "finalAmount": 220,
    "paymentMethod": "wechat",
    "status": "pending",
    "createTime": "2024-01-27T15:00:00.000Z",
    "expireTime": "2024-01-27T15:30:00.000Z",
    "paymentInfo": {
      "wechat": {
        "prepayId": "wx_prepay_001",
        "nonceStr": "random_string",
        "timeStamp": "1706345678",
        "package": "prepay_id=wx_prepay_001",
        "signType": "RSA",
        "paySign": "signature_string"
      }
    }
  }
}
```

### 4. 微信支付接口

#### 接口信息
- **接口名称**: 调用微信支付
- **请求方法**: 微信小程序API
- **接口路径**: `wx.requestPayment`
- **当前模拟位置**: `pages/recharge/recharge.js` 第240-332行 `confirmRecharge` 方法中的支付调用

#### 微信支付参数
```javascript
wx.requestPayment({
  timeStamp: paymentInfo.wechat.timeStamp,
  nonceStr: paymentInfo.wechat.nonceStr,
  package: paymentInfo.wechat.package,
  signType: paymentInfo.wechat.signType,
  paySign: paymentInfo.wechat.paySign,
  success: (res) => {
    // 支付成功，验证支付结果
    this.verifyPayment(rechargeOrderId)
  },
  fail: (err) => {
    // 支付失败处理
    console.error('支付失败：', err)
  }
})
```

### 5. 验证支付结果接口

#### 接口信息
- **接口名称**: 验证支付结果并更新余额
- **请求方法**: POST
- **接口路径**: `/api/recharge/verify-payment`
- **当前模拟位置**: 需要新增，当前在 `confirmRecharge` 方法的成功回调中

#### 请求头
```
Authorization: Bearer {token}
```

#### 请求参数
```json
{
  "rechargeOrderId": "recharge_1706345678901",
  "paymentResult": {
    "transactionId": "wx_transaction_001",
    "paymentTime": "2024-01-27T15:05:00.000Z"
  }
}
```

#### 响应数据
```json
{
  "code": 200,
  "message": "充值成功",
  "data": {
    "rechargeOrderId": "recharge_1706345678901",
    "transactionId": "tx_1706345678901",
    "amount": 200,
    "bonusAmount": 20,
    "finalAmount": 220,
    "beforeBalance": 150.00,
    "afterBalance": 370.00,
    "paymentTime": "2024-01-27T15:05:00.000Z",
    "status": "success",
    "availableHours": "4.9"
  }
}
```

### 6. 获取充值记录接口

#### 接口信息
- **接口名称**: 获取用户充值历史记录
- **请求方法**: GET
- **接口路径**: `/api/recharge/history`
- **当前模拟位置**: 暂无，可作为扩展功能

#### 请求头
```
Authorization: Bearer {token}
```

#### 请求参数
```
page=1        // 页码
limit=20      // 每页数量
status=all    // 状态：all/success/failed/pending
startDate=2024-01-01  // 开始日期，可选
endDate=2024-01-31    // 结束日期，可选
```

#### 响应数据
```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "list": [
      {
        "id": "recharge_001",
        "amount": 200,
        "bonusAmount": 20,
        "finalAmount": 220,
        "paymentMethod": "wechat",
        "paymentMethodText": "微信支付",
        "status": "success",
        "statusText": "成功",
        "createTime": "2024-01-27T15:00:00.000Z",
        "paymentTime": "2024-01-27T15:05:00.000Z",
        "transactionId": "tx_1706345678901"
      }
    ],
    "pagination": {
      "page": 1,
      "limit": 20,
      "total": 8,
      "totalPages": 1
    },
    "summary": {
      "totalAmount": 1200,
      "totalBonus": 150,
      "successCount": 7,
      "failedCount": 1
    }
  }
}
```

## 替换指导

### 1. 修改页面初始化
**文件**: `pages/recharge/recharge.js`
**位置**: 第56-61行 `onLoad` 方法

**替换为**:
```javascript
async onLoad(options) {
  console.log('充值页面加载')
  this.checkAuth()
  await this.loadRechargeConfig()
  this.loadUserBalance()
  this.updateConfirmButton()
}

// 新增方法：加载充值配置
async loadRechargeConfig() {
  try {
    const response = await request.get('/api/recharge/config')
    
    if (response && response.data) {
      this.setData({
        amountOptions: response.data.amountOptions,
        paymentMethods: response.data.paymentMethods,
        minAmount: response.data.minAmount,
        maxAmount: response.data.maxAmount,
        bonusRules: response.data.bonusRules
      })
    }
  } catch (error) {
    console.error('加载充值配置失败：', error)
    // 使用默认配置
  }
}
```

### 2. 修改优惠计算
**文件**: `pages/recharge/recharge.js`
**位置**: 第180-236行 `calculateBonus` 方法

**替换为**:
```javascript
async calculateBonus() {
  const { selectedAmount, customAmount, selectedPayment } = this.data
  const amount = selectedAmount || parseFloat(customAmount) || 0

  if (amount <= 0 || !selectedPayment) {
    this.setData({
      finalAmount: 0,
      bonusAmount: 0,
      canRecharge: false
    })
    return
  }

  try {
    const response = await request.post('/api/recharge/calculate', {
      amount: amount,
      paymentMethod: selectedPayment
    })

    if (response && response.data) {
      this.setData({
        finalAmount: response.data.finalAmount,
        bonusAmount: response.data.bonusAmount,
        actualPayment: response.data.actualPayment,
        bonusReason: response.data.bonusReason,
        availableHours: response.data.availableHours,
        canRecharge: true
      })
    }
  } catch (error) {
    console.error('计算优惠失败：', error)
    // 使用本地计算作为降级方案
    this.calculateBonusLocal(amount)
  }
}

// 本地优惠计算降级方案
calculateBonusLocal(amount) {
  let bonusAmount = 0
  let bonusReason = ''
  
  if (amount >= 200) {
    bonusAmount = amount * 0.1
    bonusReason = '充值满200元赠送10%'
  } else if (amount >= 100) {
    bonusAmount = amount * 0.05
    bonusReason = '充值满100元赠送5%'
  }
  
  this.setData({
    finalAmount: amount + bonusAmount,
    bonusAmount: bonusAmount,
    actualPayment: amount,
    bonusReason: bonusReason,
    canRecharge: true
  })
}
```

### 3. 修改充值确认
**文件**: `pages/recharge/recharge.js`
**位置**: 第240-332行 `confirmRecharge` 方法

**替换为**:
```javascript
async confirmRecharge() {
  if (!this.checkAuth()) return

  const { selectedAmount, customAmount, selectedPayment, finalAmount, bonusAmount } = this.data
  const amount = selectedAmount || parseFloat(customAmount) || 0

  if (amount <= 0 || !selectedPayment) {
    wx.showToast({
      title: '请选择充值金额和支付方式',
      icon: 'none'
    })
    return
  }

  try {
    wx.showLoading({ title: '创建订单中...' })

    // 1. 创建充值订单
    const orderResponse = await request.post('/api/recharge/create-order', {
      amount: amount,
      paymentMethod: selectedPayment,
      bonusAmount: bonusAmount,
      finalAmount: finalAmount,
      clientInfo: {
        platform: 'wechat_miniprogram',
        version: '1.0.0'
      }
    })

    if (!orderResponse || !orderResponse.data) {
      throw new Error('创建订单失败')
    }

    const { rechargeOrderId, paymentInfo } = orderResponse.data

    wx.hideLoading()

    // 2. 调用微信支付
    if (selectedPayment === 'wechat') {
      wx.requestPayment({
        timeStamp: paymentInfo.wechat.timeStamp,
        nonceStr: paymentInfo.wechat.nonceStr,
        package: paymentInfo.wechat.package,
        signType: paymentInfo.wechat.signType,
        paySign: paymentInfo.wechat.paySign,
        success: async (res) => {
          // 3. 验证支付结果
          await this.verifyPayment(rechargeOrderId, res)
        },
        fail: (err) => {
          console.error('支付失败：', err)
          wx.showToast({
            title: '支付失败',
            icon: 'error'
          })
        }
      })
    }

  } catch (error) {
    wx.hideLoading()
    console.error('充值失败：', error)
    wx.showToast({
      title: error.message || '充值失败，请重试',
      icon: 'error'
    })
  }
}

// 新增方法：验证支付结果
async verifyPayment(rechargeOrderId, paymentResult) {
  try {
    wx.showLoading({ title: '验证支付结果...' })

    const response = await request.post('/api/recharge/verify-payment', {
      rechargeOrderId: rechargeOrderId,
      paymentResult: paymentResult
    })

    wx.hideLoading()

    if (response && response.data) {
      // 更新本地用户余额
      const currentUser = auth.getCurrentUser()
      if (currentUser) {
        currentUser.balance = response.data.afterBalance
        auth.updateUserInfo(currentUser)
      }

      // 更新页面余额显示
      this.loadUserBalance()

      wx.showToast({
        title: `充值成功！到账${response.data.finalAmount}元`,
        icon: 'success',
        duration: 3000
      })

      // 延迟返回上一页
      setTimeout(() => {
        wx.navigateBack()
      }, 3000)
    }
  } catch (error) {
    wx.hideLoading()
    console.error('验证支付失败：', error)
    wx.showToast({
      title: '支付验证失败，请联系客服',
      icon: 'error'
    })
  }
}
```

## 错误处理和降级方案

### 降级方案实现
```javascript
// 充值配置降级方案
loadRechargeConfigLocal() {
  this.setData({
    amountOptions: [
      { value: 50, hours: '0.6', bonus: 0 },
      { value: 100, hours: '1.2', bonus: 5 },
      { value: 200, hours: '2.5', bonus: 20 },
      { value: 500, hours: '6', bonus: 80 }
    ],
    paymentMethods: [
      {
        type: 'wechat',
        name: '微信支付',
        desc: '推荐使用，快速便捷',
        enabled: true
      }
    ]
  })
}

// 支付失败处理
handlePaymentFailure(error) {
  let message = '支付失败'
  
  if (error.errMsg) {
    if (error.errMsg.includes('cancel')) {
      message = '支付已取消'
    } else if (error.errMsg.includes('fail')) {
      message = '支付失败，请重试'
    }
  }
  
  wx.showToast({
    title: message,
    icon: 'none'
  })
}
```

## 注意事项

1. **支付安全**: 所有支付相关操作需要严格的安全验证
2. **订单状态**: 需要处理支付超时、取消等各种状态
3. **余额同步**: 充值成功后需要及时更新用户余额
4. **重复支付**: 防止用户重复点击导致重复支付
5. **网络异常**: 支付过程中的网络异常处理
6. **用户体验**: 提供清晰的支付流程和状态反馈

## 测试建议

1. 测试充值配置的加载和显示
2. 测试不同金额的优惠计算
3. 测试微信支付的完整流程
4. 测试支付失败和取消的处理
5. 测试网络异常时的降级方案
6. 测试余额更新的准确性
7. 测试重复支付的防护机制
