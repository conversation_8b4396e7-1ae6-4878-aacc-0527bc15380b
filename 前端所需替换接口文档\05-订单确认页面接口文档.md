# 订单确认页面接口文档

## 页面概述
订单确认页面，用户选择租赁时间、时长，确认订单信息并完成支付。

## 当前实现分析

### 页面文件位置
- `pages/order-confirm/order-confirm.js` - 订单确认页逻辑
- `utils/orderManager.js` - 订单管理工具
- `utils/mockData.js` - 模拟设备和地点数据

### 当前功能流程
1. **订单信息加载**：根据设备ID和地点ID加载详细信息
2. **时间选择**：选择租赁日期和时间段
3. **时长选择**：选择租赁时长（1-4小时）
4. **费用计算**：计算设备费+服务费+保险费
5. **余额检查**：验证用户余额是否充足
6. **订单提交**：创建订单并扣除余额

## 需要替换的接口

### 1. 检查时间段可用性接口

#### 接口信息
- **接口名称**: 检查指定时间段的可用性
- **请求方法**: GET
- **接口路径**: `/api/orders/availability`
- **当前模拟位置**: `pages/order-confirm/order-confirm.js` 第22-28行 `timeSlots` 数据

#### 请求参数
```
equipmentId=dji_air3                    // 设备ID
locationId=shusheng_01                  // 地点ID
date=2024-01-27                         // 日期
```

#### 响应数据
```json
{
  "code": 200,
  "message": "检查完成",
  "data": {
    "date": "2024-01-27",
    "timeSlots": [
      {
        "time": "09:00",
        "available": true,
        "reason": ""
      },
      {
        "time": "10:30",
        "available": true,
        "reason": ""
      },
      {
        "time": "13:00",
        "available": true,
        "reason": ""
      },
      {
        "time": "14:00",
        "available": false,
        "reason": "已被预订"
      },
      {
        "time": "16:00",
        "available": true,
        "reason": ""
      },
      {
        "time": "18:00",
        "available": true,
        "reason": ""
      }
    ],
    "weatherInfo": {
      "suitable": true,
      "condition": "晴",
      "temperature": "15-22°C",
      "windLevel": "3级",
      "warning": ""
    }
  }
}
```

### 2. 计算订单费用接口

#### 接口信息
- **接口名称**: 计算订单总费用
- **请求方法**: POST
- **接口路径**: `/api/orders/calculate-fee`
- **当前模拟位置**: `pages/order-confirm/order-confirm.js` 第198-212行 `calculateFees` 方法

#### 请求参数
```json
{
  "equipmentId": "dji_air3",
  "locationId": "shusheng_01",
  "duration": 2,
  "startTime": "2024-01-27 10:00:00"
}
```

#### 响应数据
```json
{
  "code": 200,
  "message": "计算成功",
  "data": {
    "equipmentFee": 160,
    "serviceFee": 10,
    "insuranceFee": 5,
    "locationFee": 0,
    "discount": 0,
    "totalFee": 175,
    "breakdown": [
      {
        "item": "设备租赁费",
        "amount": 160,
        "description": "DJI Air 3 × 2小时"
      },
      {
        "item": "服务费",
        "amount": 10,
        "description": "平台服务费"
      },
      {
        "item": "保险费",
        "amount": 5,
        "description": "设备保险费"
      }
    ]
  }
}
```

### 3. 创建订单接口

#### 接口信息
- **接口名称**: 创建租赁订单
- **请求方法**: POST
- **接口路径**: `/api/orders/create`
- **当前模拟位置**: `utils/orderManager.js` 第67-149行 `callCreateOrderAPI` 方法

#### 请求头
```
Authorization: Bearer {token}
```

#### 请求参数
```json
{
  "equipmentId": "dji_air3",
  "equipmentName": "DJI Air 3",
  "locationId": "shusheng_01",
  "locationName": "书圣故里",
  "startTime": "2024-01-27 10:00:00",
  "duration": 2,
  "totalAmount": 175,
  "paymentMethod": "balance",
  "remark": "备注信息"
}
```

#### 响应数据
```json
{
  "code": 200,
  "message": "订单创建成功",
  "data": {
    "orderId": "order_1706345678901",
    "order": {
      "id": "order_1706345678901",
      "equipmentId": "dji_air3",
      "equipmentName": "DJI Air 3",
      "locationId": "shusheng_01",
      "locationName": "书圣故里",
      "userId": "user_001",
      "status": "ongoing",
      "statusText": "进行中",
      "orderTime": "2024-01-27 09:30:00",
      "startTime": "2024-01-27 10:00:00",
      "endTime": "2024-01-27 12:00:00",
      "duration": 2,
      "price": 80,
      "totalAmount": 175,
      "payment": {
        "method": "balance",
        "methodText": "余额支付",
        "transactionId": "tx_1706345678901"
      },
      "equipment": {
        "icon": "icon-drone",
        "battery": 100,
        "specs": ["4K双摄", "46分钟续航"]
      },
      "location": {
        "address": "绍兴市越城区蕺山街道",
        "distance": 2.3
      },
      "canControl": true,
      "canCancel": true,
      "canReview": false
    }
  }
}
```

### 4. 余额扣除接口

#### 接口信息
- **接口名称**: 扣除用户余额
- **请求方法**: POST
- **接口路径**: `/api/user/deduct-balance`
- **当前模拟位置**: `utils/orderManager.js` 第36行 `deductBalance` 方法调用

#### 请求头
```
Authorization: Bearer {token}
```

#### 请求参数
```json
{
  "amount": 175,
  "orderId": "order_1706345678901",
  "description": "租赁费用扣除"
}
```

#### 响应数据
```json
{
  "code": 200,
  "message": "扣除成功",
  "data": {
    "transactionId": "tx_1706345678901",
    "beforeBalance": 299.50,
    "afterBalance": 124.50,
    "amount": 175,
    "createTime": "2024-01-27T10:00:00.000Z"
  }
}
```

## 替换指导

### 1. 添加时间段可用性检查
**文件**: `pages/order-confirm/order-confirm.js`
**需要新增方法**:

```javascript
async loadTimeSlots() {
  const { equipment, location, startDate } = this.data
  
  if (!equipment || !location || !startDate) return
  
  try {
    const response = await request.get('/api/orders/availability', {
      equipmentId: equipment.id,
      locationId: location.id,
      date: startDate
    })
    
    if (response && response.data) {
      this.setData({
        timeSlots: response.data.timeSlots,
        weatherInfo: response.data.weatherInfo
      })
    }
  } catch (error) {
    console.error('加载时间段失败：', error)
    // 使用默认时间段
    this.setData({
      timeSlots: [
        { time: '09:00', available: true },
        { time: '10:30', available: true },
        { time: '13:00', available: true },
        { time: '16:00', available: true }
      ]
    })
  }
}
```

### 2. 修改费用计算
**文件**: `pages/order-confirm/order-confirm.js`
**位置**: 第198-212行 `calculateFees` 方法

**当前代码**:
```javascript
calculateFees() {
  const { equipment, selectedDuration, serviceFee, insuranceFee } = this.data

  if (!equipment || !selectedDuration) return

  const equipmentFee = equipment.price * selectedDuration
  const totalFee = equipmentFee + serviceFee + insuranceFee

  this.setData({
    totalFee
  })

  console.log('费用计算完成', { equipmentFee, totalFee })
}
```

**替换为**:
```javascript
async calculateFees() {
  const { equipment, location, selectedDuration, selectedTimeSlot, startDate } = this.data

  if (!equipment || !location || !selectedDuration || !selectedTimeSlot) return

  try {
    const response = await request.post('/api/orders/calculate-fee', {
      equipmentId: equipment.id,
      locationId: location.id,
      duration: selectedDuration,
      startTime: `${startDate} ${selectedTimeSlot}:00`
    })

    if (response && response.data) {
      this.setData({
        equipmentFee: response.data.equipmentFee,
        serviceFee: response.data.serviceFee,
        insuranceFee: response.data.insuranceFee,
        totalFee: response.data.totalFee,
        feeBreakdown: response.data.breakdown
      })
      
      console.log('费用计算完成', response.data)
    }
  } catch (error) {
    console.error('计算费用失败：', error)
    // 使用本地计算作为降级方案
    this.calculateFeesLocal()
  }
}

// 本地费用计算降级方案
calculateFeesLocal() {
  const { equipment, selectedDuration } = this.data
  const equipmentFee = equipment.price * selectedDuration
  const serviceFee = 10
  const insuranceFee = 5
  const totalFee = equipmentFee + serviceFee + insuranceFee

  this.setData({
    equipmentFee,
    serviceFee,
    insuranceFee,
    totalFee
  })
}
```

### 3. 修改订单创建
**文件**: `utils/orderManager.js`
**位置**: 第67-149行 `callCreateOrderAPI` 方法

**替换为**:
```javascript
async function callCreateOrderAPI(orderData) {
  try {
    const response = await request.post('/api/orders/create', orderData, {
      showLoading: true,
      loadingText: '创建订单中...'
    })
    
    if (response && response.data) {
      return {
        success: true,
        order: response.data.order,
        message: '订单创建成功'
      }
    } else {
      throw new Error('订单创建失败')
    }
  } catch (error) {
    console.error('创建订单API调用失败：', error)
    return {
      success: false,
      message: error.message || '网络错误，请重试'
    }
  }
}
```

### 4. 修改余额扣除
**文件**: `utils/orderManager.js`
**需要实现 `deductBalance` 方法**:

```javascript
async function deductBalance(amount, orderId) {
  try {
    const response = await request.post('/api/user/deduct-balance', {
      amount: amount,
      orderId: orderId,
      description: '租赁费用扣除'
    })
    
    if (response && response.data) {
      // 更新本地用户信息
      const currentUser = auth.getCurrentUser()
      if (currentUser) {
        currentUser.balance = response.data.afterBalance
        auth.updateUserInfo(currentUser)
      }
      
      return {
        success: true,
        data: response.data
      }
    } else {
      throw new Error('余额扣除失败')
    }
  } catch (error) {
    console.error('扣除余额失败：', error)
    return {
      success: false,
      message: error.message || '余额扣除失败'
    }
  }
}
```

### 5. 修改订单提交流程
**文件**: `pages/order-confirm/order-confirm.js`
**位置**: 第264-326行 `submitOrder` 方法

**需要添加时间段再次验证**:
```javascript
async submitOrder() {
  if (!this.checkAuth()) return
  if (!this.checkBalance()) return

  const { equipment, location, startDate, selectedTimeSlot, selectedDuration, totalFee, userInfo } = this.data

  // 验证必填信息
  if (!startDate || !selectedTimeSlot || !selectedDuration) {
    wx.showToast({
      title: '请选择租赁时间和时长',
      icon: 'none'
    })
    return
  }

  this.setData({ submitting: true })

  try {
    // 1. 再次检查时间段可用性
    const availabilityCheck = await request.get('/api/orders/availability', {
      equipmentId: equipment.id,
      locationId: location.id,
      date: startDate
    })

    const timeSlot = availabilityCheck.data.timeSlots.find(slot => slot.time === selectedTimeSlot)
    if (!timeSlot || !timeSlot.available) {
      throw new Error('选择的时间段已不可用，请重新选择')
    }

    // 2. 准备订单数据
    const orderData = {
      equipmentId: equipment.id,
      equipmentName: equipment.name,
      locationId: location.id,
      locationName: location.name,
      startTime: `${startDate} ${selectedTimeSlot}:00`,
      duration: selectedDuration,
      totalAmount: totalFee,
      paymentMethod: 'balance',
      userId: userInfo.id
    }

    console.log('提交订单', orderData)

    // 3. 调用订单处理逻辑
    const result = await this.processOrder(orderData)

    if (result.success) {
      wx.showToast({
        title: '订单创建成功',
        icon: 'success',
        duration: 2000
      })

      // 跳转到订单详情或控制页面
      setTimeout(() => {
        wx.redirectTo({
          url: `/pages/drone-control/drone-control?orderId=${result.orderId}&equipmentId=${equipment.id}`
        })
      }, 2000)
    } else {
      throw new Error(result.message || '订单创建失败')
    }

  } catch (error) {
    console.error('提交订单失败', error)
    wx.showToast({
      title: error.message || '提交失败，请重试',
      icon: 'error'
    })
  } finally {
    this.setData({ submitting: false })
  }
}
```

## 错误处理和降级方案

### 降级方案实现
```javascript
// 费用计算降级方案
calculateFeesLocal() {
  const { equipment, selectedDuration } = this.data
  const equipmentFee = equipment.price * selectedDuration
  const serviceFee = 10
  const insuranceFee = 5
  const totalFee = equipmentFee + serviceFee + insuranceFee

  this.setData({
    equipmentFee,
    serviceFee, 
    insuranceFee,
    totalFee
  })
}

// 时间段加载降级方案
loadTimeSlotsLocal() {
  this.setData({
    timeSlots: [
      { time: '09:00', available: true },
      { time: '10:30', available: true },
      { time: '13:00', available: true },
      { time: '16:00', available: true }
    ]
  })
}
```

## 注意事项

1. **时间验证**: 需要验证选择的时间是否在有效范围内
2. **库存检查**: 创建订单前需要再次检查设备和时间段可用性
3. **事务处理**: 订单创建和余额扣除需要保证事务一致性
4. **重复提交**: 防止用户重复点击提交按钮
5. **费用计算**: 服务端计算费用更准确，避免客户端篡改
6. **网络异常**: 提供完善的错误处理和重试机制

## 测试建议

1. 测试时间段可用性检查
2. 测试费用计算的准确性
3. 测试订单创建的完整流程
4. 测试余额不足的处理
5. 测试网络异常时的降级方案
6. 测试重复提交的防护
7. 测试订单创建后的页面跳转
