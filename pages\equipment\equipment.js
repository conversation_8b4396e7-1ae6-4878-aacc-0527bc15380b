// pages/equipment/equipment.js
const app = getApp()
const auth = require('../../utils/auth.js')
const { mockEquipment } = require('../../utils/mockData.js')

Page({

  /**
   * 页面的初始数据
   */
  data: {
    // 设备数据
    allEquipment: [],
    filteredEquipment: [],
    
    // 筛选和排序
    currentFilter: 'all',
    currentSort: 'rating',
    showSort: false,
    
    // 页面状态
    loading: true
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    console.log('设备列表页面加载')
    this.checkAuth()
    this.loadEquipment()
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    // 检查登录状态
    this.checkAuth()
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {
    this.loadEquipment()
  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {

  },

  /**
   * 检查认证状态
   */
  checkAuth() {
    if (!auth.checkLoginStatus()) {
      auth.redirectToLogin('/pages/equipment/equipment')
      return false
    }
    return true
  },

  /**
   * 加载设备数据
   */
  async loadEquipment() {
    if (!this.checkAuth()) return

    this.setData({ loading: true })

    try {
      // 模拟API调用延迟
      await new Promise(resolve => setTimeout(resolve, 500))
      
      const allEquipment = [...mockEquipment]
      this.setData({ allEquipment })
      
      // 应用当前筛选和排序
      this.applyFilterAndSort()
      
      console.log('设备数据加载完成：', allEquipment.length, '个')
    } catch (error) {
      console.error('加载设备失败：', error)
      wx.showToast({
        title: '加载失败',
        icon: 'error'
      })
    } finally {
      this.setData({ loading: false })
      wx.stopPullDownRefresh()
    }
  },

  /**
   * 筛选变更
   */
  onFilterChange(e) {
    const filter = e.currentTarget.dataset.filter
    this.setData({ currentFilter: filter })
    this.applyFilterAndSort()
  },

  /**
   * 排序变更
   */
  onSortChange(e) {
    const sort = e.currentTarget.dataset.sort
    this.setData({ 
      currentSort: sort,
      showSort: false
    })
    this.applyFilterAndSort()
  },

  /**
   * 应用筛选和排序
   */
  applyFilterAndSort() {
    let filtered = [...this.data.allEquipment]
    
    // 应用筛选
    switch (this.data.currentFilter) {
      case 'available':
        filtered = filtered.filter(item => item.available)
        break
      case 'professional':
        filtered = filtered.filter(item => 
          item.name.includes('Mavic') || item.name.includes('Air 3')
        )
        break
      case 'entry':
        filtered = filtered.filter(item => 
          item.name.includes('Mini')
        )
        break
      case 'sport':
        filtered = filtered.filter(item => 
          item.name.includes('FPV')
        )
        break
      default:
        // 全部，不筛选
        break
    }
    
    // 应用排序
    switch (this.data.currentSort) {
      case 'rating':
        filtered.sort((a, b) => b.rating - a.rating)
        break
      case 'price_low':
        filtered.sort((a, b) => a.price - b.price)
        break
      case 'price_high':
        filtered.sort((a, b) => b.price - a.price)
        break
      case 'popularity':
        filtered.sort((a, b) => b.reviewCount - a.reviewCount)
        break
    }
    
    this.setData({ filteredEquipment: filtered })
    console.log('筛选排序后设备数量：', filtered.length)
  },

  /**
   * 重置筛选
   */
  resetFilter() {
    this.setData({ 
      currentFilter: 'all',
      currentSort: 'rating'
    })
    this.applyFilterAndSort()
  },

  /**
   * 显示排序选项
   */
  showSortOptions() {
    this.setData({ showSort: true })
  },

  /**
   * 隐藏排序选项
   */
  hideSortOptions() {
    this.setData({ showSort: false })
  },

  /**
   * 阻止事件冒泡
   */
  stopPropagation() {
    // 阻止事件冒泡
  },

  /**
   * 跳转到设备详情
   */
  goToDetail(e) {
    const id = e.currentTarget.dataset.id
    wx.navigateTo({
      url: `/pages/equipment-detail/equipment-detail?id=${id}`
    })
  },

  /**
   * 租用设备
   */
  rentEquipment(e) {
    const id = e.currentTarget.dataset.id
    console.log('租用设备:', id)

    wx.navigateTo({
      url: `/pages/equipment-detail/equipment-detail?id=${id}`
    })
  },

  /**
   * 阻止事件冒泡
   */
  stopPropagation(e) {
    // 阻止事件冒泡
  }
})