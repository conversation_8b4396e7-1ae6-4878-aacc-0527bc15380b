# 逍遥境无人机租赁小程序开发文档设计demo

## 项目概述

**项目名称**: 逍遥境 - 无人机租赁微信小程序  
**项目类型**: 微信小程序  
**设计风格**: 白色+黑色简约高级线条现代风格，类似ChatGPT柔和流畅风格（务必遵循）
**目标用户**: 无人机航拍爱好者、摄影师、旅游用户  

## 核心功能

1. **用户认证**: 微信授权登录 + 微信手机号验证授权（先写测试数据，模拟登录成功）
2. **设备租赁**: 浏览、选择、租赁无人机设备
3. **地点选择**: 基于地图的拍摄地点选择
4. **实时操控**: 模拟无人机操控和拍摄
5. **作品管理**: 拍摄作品存储和管理
6. **订单管理**: 完整的订单生命周期管理
7. **账户管理**: 余额充值、消费记录

## 页面结构 (Pages)

### 1. 登录授权页 (`pages/login/login`)
**功能**: 用户登录入口
- **wxml**: 微信授权按钮、手机号登录表单
- **wxss**: 高级黑白配色、卡片样式、按钮动效
- **js**: 
  - `wx.login()` 获取微信授权
  - `wx.getPhoneNumber()` 获取手机号授权
  - 登录状态存储到 `wx.setStorageSync()`
- **json**: 导航栏隐藏配置

### 2. 首页 (`pages/home/<USER>
**功能**: 设备展示和快捷入口
- **wxml**: 搜索栏、轮播图、设备卡片、底部TabBar
- **wxss**: 卡片阴影、轮播图样式、网格布局
- **js**:
  - 设备数据获取和渲染
  - 轮播图自动播放
  - 页面跳转逻辑
- **json**: TabBar配置

### 3. 设备页 (`pages/devices/devices`)
**功能**: 设备列表和筛选
- **wxml**: 筛选器、设备列表、状态标签
- **wxss**: 筛选按钮样式、列表项布局
- **js**:
  - 设备筛选逻辑
  - 设备状态管理
  - 跳转到设备详情
- **json**: 标准页面配置

### 4. 设备详情页 (`pages/device-detail/device-detail`)
**功能**: 设备详细信息展示
- **wxml**: 图片轮播、规格参数、用户评价、操作按钮
- **wxss**: 图片展示、规格表格、评价样式
- **js**:
  - 设备详情数据加载
  - 余额检查逻辑
  - 跳转到地点选择
- **json**: 标准页面配置

### 5. 地点选择页 (`pages/location/location`)
**功能**: 拍摄地点选择
- **wxml**: 地图组件、地点列表、确认按钮
- **wxss**: 地图样式、地点卡片、选中状态
- **js**:
  - `wx.getLocation()` 获取当前位置
  - 地图标记和交互
  - 地点选择逻辑
- **json**: 地图权限配置

### 6. 订单确认页 (`pages/order/order`)
**功能**: 订单信息确认和支付
- **wxml**: 设备信息、时间选择、费用明细、支付按钮
- **wxss**: 时间选择器样式、费用计算显示
- **js**:
  - 时间段选择逻辑
  - 费用计算
  - `wx.requestPayment()` 支付接口
- **json**: 标准页面配置

### 7. 无人机操控页 (`pages/control/control`)
**功能**: 无人机操控界面
- **wxml**: 视频流、操控按钮、飞行数据、拍摄按钮
- **wxss**: 全屏视频、操控界面、数据显示
- **js**:
  - 模拟飞行数据更新
  - 拍照录像功能
  - 操控指令发送
- **json**: 全屏显示配置

### 8. 订单页 (`pages/orders/orders`)
**功能**: 订单历史和管理
- **wxml**: 订单筛选、订单列表、状态标签、操作按钮
- **wxss**: 订单卡片、状态样式、按钮组
- **js**:
  - 订单数据获取和筛选
  - 订单状态更新
  - 取消/继续操作
- **json**: TabBar配置

### 9. 个人中心页 (`pages/profile/profile`)
**功能**: 用户信息和设置
- **wxml**: 用户头像、余额卡片、功能菜单
- **wxss**: 渐变背景、卡片布局、菜单样式
- **js**:
  - 用户信息显示
  - 统计数据计算
  - 功能跳转
- **json**: TabBar配置

### 10. 作品集页 (`pages/gallery/gallery`)
**功能**: 拍摄作品管理
- **wxml**: 筛选器、作品网格、预览模态框
- **wxss**: 网格布局、图片样式、模态框
- **js**:
  - 作品数据管理
  - 图片预览功能
  - 分享和删除操作
- **json**: 标准页面配置

### 11. 充值页 (`pages/recharge/recharge`)
**功能**: 账户余额充值
- **wxml**: 金额选择、支付方式、充值按钮
- **wxss**: 金额按钮、支付方式样式
- **js**:
  - 金额选择逻辑
  - `wx.requestPayment()` 支付处理
  - 余额更新
- **json**: 标准页面配置

## 技术架构

### 前端技术栈
- **框架**: 微信小程序原生框架
- **样式**: WXSS (类似CSS)
- **脚本**: JavaScript ES6+
- **组件**: 微信小程序内置组件

### 核心组件使用
```javascript
// 主要使用的微信小程序组件
- view, text, image, button (基础组件)
- swiper, swiper-item (轮播组件)
- scroll-view (滚动组件)
- map (地图组件)
- camera (相机组件，用于无人机视频流)
- picker (选择器组件)
- modal (模态框组件)
```

### 数据存储
```javascript
// 本地存储方案
wx.setStorageSync('userInfo', userInfo)      // 用户信息
wx.setStorageSync('orders', orders)          // 订单数据
wx.setStorageSync('userPhotos', photos)      // 作品数据
wx.setStorageSync('isLoggedIn', true)        // 登录状态
```

## 核心业务逻辑

### 1. 用户认证流程
```javascript
// 完整登录流程
1. 用户进入小程序 → 检查登录状态
   - 已登录：直接跳转首页 (wx.switchTab)
   - 未登录：显示登录页面

2. 用户选择登录方式：
   a) 微信授权登录：
      - wx.login() 获取临时登录凭证
      - wx.getUserProfile() 获取用户基本信息
      - 发送到后端验证 → 成功：存储用户信息，跳转首页
      - 失败：显示错误提示，重新登录

   b) 手机号登录：
      - 显示手机号输入框
      - wx.getPhoneNumber() 或手动输入
      - 请求验证验证失败：显示错误提示，重新验证

3. 登录成功后：
   - 存储用户token和基本信息到本地
   - wx.switchTab({ url: '/pages/home/<USER>' })
   - 更新全局登录状态
```

### 2. 设备租赁完整流程
```javascript
// 详细租赁流程与页面跳转
1. 首页浏览 (pages/home/<USER>
   ├── 点击设备卡片 → wx.navigateTo('/pages/device-detail/device-detail?id=xxx')
   ├── 点击"查看全部" → wx.switchTab('/pages/devices/devices')
   └── 点击快捷功能 → 对应功能页面

2. 设备列表页 (pages/devices/devices)
   ├── 筛选设备 → 本页面刷新列表
   ├── 点击设备卡片 → wx.navigateTo('/pages/device-detail/device-detail?id=xxx')
   └── 返回首页 → wx.switchTab('/pages/home/<USER>')

3. 设备详情页 (pages/device-detail/device-detail)
   ├── 查看设备信息 → 本页面展示
   ├── 点击"立即租赁" → 检查登录状态
   │   ├── 未登录 → wx.redirectTo('/pages/login/login')
   │   └── 已登录 → 检查余额
   │       ├── 余额不足 → 提示充值，跳转 wx.navigateTo('/pages/recharge/recharge')
   │       └── 余额充足 → wx.navigateTo('/pages/location/location')
   └── 返回设备列表 → wx.navigateBack()

4. 地点选择页 (pages/location/location)
   ├── 选择拍摄地点 → 本页面更新选择状态
   ├── 确认地点 → wx.navigateTo('/pages/order/order')
   └── 返回设备详情 → wx.navigateBack()

5. 订单确认页 (pages/order/order)
   ├── 选择时间和时长 → 本页面计算费用
   ├── 确认下单 → 执行下单逻辑
   │   ├── 余额检查失败 → 提示充值，跳转充值页
   │   ├── 下单成功 → 创建订单，wx.redirectTo('/pages/control/control')
   │   └── 下单失败 → 显示错误提示，停留当前页
   └── 返回地点选择 → wx.navigateBack()

6. 无人机操控页 (pages/control/control)
   ├── 实时操控 → 本页面功能
   ├── 拍照录像 → 保存到作品集
   ├── 结束飞行 → 更新订单状态，wx.switchTab('/pages/orders/orders')
   └── 紧急情况 → 强制结束，跳转订单页
```

### 3. 订单管理流程
```javascript
// 订单状态管理与页面跳转
1. 订单列表页 (pages/orders/orders)
   ├── 查看订单列表 → 本页面展示
   ├── 筛选订单状态 → 本页面刷新
   ├── 点击"继续操控" → wx.navigateTo('/pages/control/control?orderId=xxx')
   ├── 点击"取消订单" → 确认弹窗
   │   ├── 确认取消 → 更新订单状态，退还余额，刷新列表
   │   └── 取消操作 → 停留当前页
   ├── 点击"查看作品" → wx.navigateTo('/pages/gallery/gallery?orderId=xxx')
   └── 其他Tab → wx.switchTab()

2. 订单状态流转：
   - 创建订单 → status: 'pending'
   - 支付成功 → status: 'active'
   - 开始操控 → status: 'flying'
   - 结束飞行 → status: 'completed'
   - 取消订单 → status: 'cancelled'
```

### 4. 账户管理流程
```javascript
// 余额和账户管理
1. 个人中心页 (pages/profile/profile)
   ├── 查看个人信息 → 本页面展示
   ├── 点击"余额充值" → wx.navigateTo('/pages/recharge/recharge')
   ├── 点击"我的作品集" → wx.navigateTo('/pages/gallery/gallery')
   ├── 点击"我的订单" → wx.switchTab('/pages/orders/orders')
   ├── 点击"设置" → wx.navigateTo('/pages/settings/settings')
   └── 退出登录 → 清除本地数据，wx.redirectTo('/pages/login/login')

2. 充值页面 (pages/recharge/recharge)
   ├── 选择充值金额 → 本页面更新
   ├── 选择支付方式 → 本页面更新
   ├── 确认充值 → wx.requestPayment()
   │   ├── 支付成功 → 更新余额，显示成功提示，返回上一页
   │   ├── 支付失败 → 显示失败提示，停留当前页
   │   └── 支付取消 → 停留当前页
   └── 返回 → wx.navigateBack()

3. 作品集页面 (pages/gallery/gallery)
   ├── 查看作品列表 → 本页面展示
   ├── 筛选作品类型 → 本页面刷新
   ├── 点击作品 → 本页面模态框预览
   ├── 分享作品 → 调用分享API
   ├── 删除作品 → 确认后删除，刷新列表
   └── 返回 → wx.navigateBack()
```

### 3. 支付集成
```javascript
// 微信支付
wx.requestPayment({
  timeStamp: '',
  nonceStr: '',
  package: '',
  signType: 'MD5',
  paySign: '',
  success: function(res) {
    // 支付成功处理
  },
  fail: function(res) {
    // 支付失败处理
  }
})
```

### 4. 地图功能
```javascript
// 地图相关API
wx.getLocation()        // 获取当前位置
wx.chooseLocation()     // 选择位置
wx.openLocation()       // 打开地图
```

### 7. 页面生命周期管理
```javascript
// 页面基类 - utils/basePage.js
const BasePage = {
  // 页面数据
  data: {
    loading: false,
    error: null,
    userInfo: null
  },

  // 页面加载
  onLoad(options) {
    // 检查登录状态
    if (!this.checkAuth()) return

    // 显示加载状态
    this.setData({ loading: true })

    // 初始化页面数据
    this.initPageData(options)
      .then(() => {
        this.setData({ loading: false })
      })
      .catch((error) => {
        this.handleError(error)
        this.setData({ loading: false })
      })
  },

  // 页面显示
  onShow() {
    // 刷新用户信息
    this.refreshUserInfo()

    // 刷新页面数据（如果需要）
    if (this.needRefresh) {
      this.refreshPageData()
      this.needRefresh = false
    }
  },

  // 检查认证状态
  checkAuth() {
    if (this.route === 'pages/login/login') {
      return true
    }

    return AuthManager.checkLogin()
  },

  // 初始化页面数据（子页面重写）
  initPageData(options) {
    return Promise.resolve()
  },

  // 刷新用户信息
  refreshUserInfo() {
    const userInfo = wx.getStorageSync('userInfo')
    this.setData({ userInfo })
  },

  // 错误处理
  handleError(error) {
    this.setData({ error: error.message })
    ErrorManager.handleError(error, this.route)
  },

  // 显示加载状态
  showLoading(title = '加载中...') {
    wx.showLoading({ title })
    this.setData({ loading: true })
  },

  // 隐藏加载状态
  hideLoading() {
    wx.hideLoading()
    this.setData({ loading: false })
  }
}
```

### 8. 具体页面实现逻辑

#### 登录页面逻辑 (pages/login/login.js)
```javascript
const app = getApp()

Page({
  data: {
    showPhoneModal: false,
    phoneNumber: '',
    verifyCode: '',
    countdown: 0,
    canSendCode: true
  },

  onLoad() {
    // 如果已登录，直接跳转首页
    if (AuthManager.checkLogin()) {
      wx.switchTab({
        url: '/pages/home/<USER>'
      })
    }
  },

  // 微信登录
  async wechatLogin() {
    try {
      this.showLoading('登录中...')

      // 获取微信登录凭证
      const loginRes = await this.wxLogin()

      // 获取用户信息
      const userProfile = await this.getUserProfile()

      // 发送到后端验证
      const userInfo = await this.authWithBackend({
        code: loginRes.code,
        userInfo: userProfile
      })

      // 登录成功
      AuthManager.loginSuccess(userInfo)

    } catch (error) {
      this.handleLoginError(error)
    } finally {
      this.hideLoading()
    }
  },

  // 手机号登录
  phoneLogin() {
    this.setData({ showPhoneModal: true })
  },

  // 发送验证码
  async sendVerifyCode() {
    const { phoneNumber } = this.data

    if (!Validator.validatePhone(phoneNumber)) {
      wx.showToast({
        title: '请输入正确的手机号',
        icon: 'error'
      })
      return
    }

    try {
      // 发送验证码请求
      await this.requestVerifyCode(phoneNumber)

      // 开始倒计时
      this.startCountdown()

      wx.showToast({
        title: '验证码已发送',
        icon: 'success'
      })

    } catch (error) {
      wx.showToast({
        title: '发送失败，请重试',
        icon: 'error'
      })
    }
  },

  // 确认手机号登录
  async confirmPhoneLogin() {
    const { phoneNumber, verifyCode } = this.data

    if (!Validator.validatePhone(phoneNumber)) {
      wx.showToast({
        title: '请输入正确的手机号',
        icon: 'error'
      })
      return
    }

    if (!Validator.validateCode(verifyCode)) {
      wx.showToast({
        title: '请输入6位验证码',
        icon: 'error'
      })
      return
    }

    try {
      this.showLoading('验证中...')

      // 验证手机号和验证码
      const userInfo = await this.verifyPhoneCode({
        phone: phoneNumber,
        code: verifyCode
      })

      // 登录成功
      this.setData({ showPhoneModal: false })
      AuthManager.loginSuccess(userInfo)

    } catch (error) {
      this.handleLoginError(error)
    } finally {
      this.hideLoading()
    }
  },

  // 处理登录错误
  handleLoginError(error) {
    let message = '登录失败，请重试'

    switch (error.code) {
      case 'USER_DENIED':
        message = '用户拒绝授权'
        break
      case 'INVALID_CODE':
        message = '验证码错误'
        break
      case 'PHONE_NOT_REGISTERED':
        message = '手机号未注册'
        break
    }

    wx.showToast({
      title: message,
      icon: 'error'
    })
  }
})
```

#### 订单确认页面逻辑 (pages/order/order.js)
```javascript
Page({
  data: {
    deviceInfo: null,
    locationInfo: null,
    selectedDate: null,
    selectedTime: null,
    selectedDuration: null,
    totalAmount: 0,
    userBalance: 0,
    loading: false
  },

  onLoad(options) {
    this.initOrderData(options)
  },

  // 初始化订单数据
  initOrderData(options) {
    const deviceId = options.deviceId
    const locationId = options.locationId

    // 获取设备信息
    const deviceInfo = this.getDeviceInfo(deviceId)

    // 获取地点信息
    const locationInfo = this.getLocationInfo(locationId)

    // 获取用户余额
    const userInfo = wx.getStorageSync('userInfo')
    const userBalance = userInfo.balance || 0

    this.setData({
      deviceInfo,
      locationInfo,
      userBalance
    })
  },

  // 选择日期
  selectDate(e) {
    const date = e.currentTarget.dataset.date
    this.setData({ selectedDate: date })
    this.calculateTotal()
  },

  // 选择时间
  selectTime(e) {
    const time = e.currentTarget.dataset.time
    this.setData({ selectedTime: time })
    this.calculateTotal()
  },

  // 选择时长
  selectDuration(e) {
    const duration = e.currentTarget.dataset.duration
    this.setData({ selectedDuration: duration })
    this.calculateTotal()
  },

  // 计算总费用
  calculateTotal() {
    const { deviceInfo, selectedDuration } = this.data

    if (deviceInfo && selectedDuration) {
      const deviceFee = deviceInfo.hourlyRate * selectedDuration
      const serviceFee = 5
      const totalAmount = deviceFee + serviceFee

      this.setData({ totalAmount })
    }
  },

  // 提交订单
  async submitOrder() {
    // 验证订单数据
    const orderData = this.getOrderData()
    const validation = Validator.validateOrderData(orderData)

    if (!validation.isValid) {
      wx.showToast({
        title: validation.errors[0],
        icon: 'error'
      })
      return
    }

    // 检查余额
    if (!BalanceManager.checkBalance(orderData.total)) {
      return
    }

    try {
      this.setData({ loading: true })

      // 创建订单
      const order = OrderManager.createOrder(orderData)

      // 支付订单
      const paySuccess = await OrderManager.payOrder(order.id)

      if (paySuccess) {
        // 跳转到操控页面
        wx.redirectTo({
          url: `/pages/control/control?orderId=${order.id}`
        })
      }

    } catch (error) {
      ErrorManager.handleError(error, 'submitOrder')
    } finally {
      this.setData({ loading: false })
    }
  },

  // 获取订单数据
  getOrderData() {
    const { deviceInfo, locationInfo, selectedDate, selectedTime, selectedDuration, totalAmount } = this.data

    return {
      deviceId: deviceInfo.id,
      deviceName: deviceInfo.name,
      locationId: locationInfo.id,
      locationName: locationInfo.name,
      date: selectedDate,
      time: selectedTime,
      duration: selectedDuration,
      total: totalAmount
    }
  }
})
```

## 页面路由配置

### app.json 配置
```json
{
  "pages": [
    "pages/login/login",
    "pages/home/<USER>",
    "pages/devices/devices",
    "pages/device-detail/device-detail",
    "pages/location/location",
    "pages/order/order",
    "pages/control/control",
    "pages/orders/orders",
    "pages/profile/profile",
    "pages/gallery/gallery",
    "pages/recharge/recharge"
  ],
  "window": {
    "backgroundTextStyle": "light",
    "navigationBarBackgroundColor": "#ffffff",
    "navigationBarTitleText": "逍遥境",
    "navigationBarTextStyle": "black",
    "backgroundColor": "#f8f9fa"
  },
  "tabBar": {
    "color": "#999999",
    "selectedColor": "#3B82F6",
    "backgroundColor": "#ffffff",
    "borderStyle": "black",
    "list": [
      {
        "pagePath": "pages/home/<USER>",
        "text": "首页",
        "iconPath": "images/home.png",
        "selectedIconPath": "images/home-active.png"
      },
      {
        "pagePath": "pages/devices/devices",
        "text": "设备",
        "iconPath": "images/devices.png",
        "selectedIconPath": "images/devices-active.png"
      },
      {
        "pagePath": "pages/orders/orders",
        "text": "订单",
        "iconPath": "images/orders.png",
        "selectedIconPath": "images/orders-active.png"
      },
      {
        "pagePath": "pages/profile/profile",
        "text": "我的",
        "iconPath": "images/profile.png",
        "selectedIconPath": "images/profile-active.png"
      }
    ]
  },
  "permission": {
    "scope.userLocation": {
      "desc": "您的位置信息将用于选择拍摄地点"
    }
  }
}
```

## 关键功能实现

### 1. 登录状态管理
```javascript
// utils/auth.js
const AuthManager = {
  // 检查登录状态
  checkLogin() {
    const isLoggedIn = wx.getStorageSync('isLoggedIn')
    const userInfo = wx.getStorageSync('userInfo')

    if (!isLoggedIn || !userInfo) {
      this.redirectToLogin()
      return false
    }

    // 检查token是否过期
    if (this.isTokenExpired(userInfo.token)) {
      this.logout()
      return false
    }

    return true
  },

  // 重定向到登录页
  redirectToLogin() {
    wx.redirectTo({
      url: '/pages/login/login'
    })
  },

  // 登录成功处理
  loginSuccess(userInfo) {
    wx.setStorageSync('isLoggedIn', true)
    wx.setStorageSync('userInfo', userInfo)
    wx.setStorageSync('loginTime', Date.now())

    // 跳转到首页
    wx.switchTab({
      url: '/pages/home/<USER>'
    })
  },

  // 退出登录
  logout() {
    wx.removeStorageSync('isLoggedIn')
    wx.removeStorageSync('userInfo')
    wx.removeStorageSync('loginTime')

    wx.redirectTo({
      url: '/pages/login/login'
    })
  },

  // 检查token是否过期
  isTokenExpired(token) {
    const loginTime = wx.getStorageSync('loginTime')
    const currentTime = Date.now()
    const expireTime = 7 * 24 * 60 * 60 * 1000 // 7天

    return currentTime - loginTime > expireTime
  }
}
```

### 2. 余额检查与管理
```javascript
// utils/balance.js
const BalanceManager = {
  // 检查余额是否充足
  checkBalance(amount) {
    const userInfo = wx.getStorageSync('userInfo')
    const balance = userInfo.balance || 0

    if (balance < amount) {
      this.showInsufficientBalanceModal()
      return false
    }
    return true
  },

  // 显示余额不足提示
  showInsufficientBalanceModal() {
    wx.showModal({
      title: '余额不足',
      content: '当前余额不足以支付此订单，是否前往充值？',
      confirmText: '去充值',
      cancelText: '取消',
      success: (res) => {
        if (res.confirm) {
          wx.navigateTo({
            url: '/pages/recharge/recharge'
          })
        }
      }
    })
  },

  // 扣除余额
  deductBalance(amount, orderId) {
    const userInfo = wx.getStorageSync('userInfo')
    const currentBalance = userInfo.balance || 0

    if (currentBalance < amount) {
      return false
    }

    userInfo.balance = currentBalance - amount
    wx.setStorageSync('userInfo', userInfo)

    // 记录消费记录
    this.addTransaction({
      type: 'deduct',
      amount: amount,
      orderId: orderId,
      timestamp: new Date().toISOString(),
      description: '设备租赁费用'
    })

    return true
  },

  // 退还余额
  refundBalance(amount, orderId) {
    const userInfo = wx.getStorageSync('userInfo')
    userInfo.balance = (userInfo.balance || 0) + amount
    wx.setStorageSync('userInfo', userInfo)

    // 记录退款记录
    this.addTransaction({
      type: 'refund',
      amount: amount,
      orderId: orderId,
      timestamp: new Date().toISOString(),
      description: '订单取消退款'
    })
  },

  // 充值余额
  rechargeBalance(amount, paymentMethod) {
    return new Promise((resolve, reject) => {
      // 调用支付接口
      wx.requestPayment({
        timeStamp: String(Date.now()),
        nonceStr: this.generateNonceStr(),
        package: `prepay_id=${this.generatePrepayId()}`,
        signType: 'MD5',
        paySign: this.generatePaySign(),
        success: (res) => {
          // 支付成功，更新余额
          const userInfo = wx.getStorageSync('userInfo')
          userInfo.balance = (userInfo.balance || 0) + amount
          wx.setStorageSync('userInfo', userInfo)

          // 记录充值记录
          this.addTransaction({
            type: 'recharge',
            amount: amount,
            paymentMethod: paymentMethod,
            timestamp: new Date().toISOString(),
            description: '账户充值'
          })

          wx.showToast({
            title: '充值成功',
            icon: 'success'
          })

          resolve(res)
        },
        fail: (err) => {
          wx.showToast({
            title: '支付失败',
            icon: 'error'
          })
          reject(err)
        }
      })
    })
  },

  // 添加交易记录
  addTransaction(transaction) {
    const transactions = wx.getStorageSync('transactions') || []
    transactions.unshift({
      id: 'TXN_' + Date.now(),
      ...transaction
    })
    wx.setStorageSync('transactions', transactions)
  }
}
```

### 3. 订单管理系统
```javascript
// utils/order.js
const OrderManager = {
  // 创建订单
  createOrder(orderData) {
    const orders = wx.getStorageSync('orders') || []
    const newOrder = {
      id: 'ORDER_' + Date.now(),
      ...orderData,
      status: 'pending',
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    }

    orders.unshift(newOrder)
    wx.setStorageSync('orders', orders)

    return newOrder
  },

  // 更新订单状态
  updateOrderStatus(orderId, status, extraData = {}) {
    const orders = wx.getStorageSync('orders') || []
    const orderIndex = orders.findIndex(order => order.id === orderId)

    if (orderIndex !== -1) {
      orders[orderIndex] = {
        ...orders[orderIndex],
        status: status,
        updatedAt: new Date().toISOString(),
        ...extraData
      }
      wx.setStorageSync('orders', orders)
      return orders[orderIndex]
    }

    return null
  },

  // 支付订单
  payOrder(orderId) {
    const orders = wx.getStorageSync('orders') || []
    const order = orders.find(o => o.id === orderId)

    if (!order) {
      wx.showToast({
        title: '订单不存在',
        icon: 'error'
      })
      return false
    }

    // 检查余额
    if (!BalanceManager.checkBalance(order.total)) {
      return false
    }

    // 扣除余额
    if (BalanceManager.deductBalance(order.total, orderId)) {
      // 更新订单状态
      this.updateOrderStatus(orderId, 'active', {
        paidAt: new Date().toISOString()
      })

      wx.showToast({
        title: '支付成功',
        icon: 'success'
      })

      // 跳转到操控页面
      setTimeout(() => {
        wx.redirectTo({
          url: `/pages/control/control?orderId=${orderId}`
        })
      }, 1500)

      return true
    }

    return false
  },

  // 取消订单
  cancelOrder(orderId) {
    return new Promise((resolve, reject) => {
      wx.showModal({
        title: '确认取消',
        content: '确定要取消此订单吗？取消后将退还全部费用。',
        confirmText: '确认取消',
        cancelText: '继续订单',
        success: (res) => {
          if (res.confirm) {
            const order = this.updateOrderStatus(orderId, 'cancelled', {
              cancelledAt: new Date().toISOString()
            })

            if (order && order.status === 'active') {
              // 退还余额
              BalanceManager.refundBalance(order.total, orderId)
            }

            wx.showToast({
              title: '订单已取消',
              icon: 'success'
            })

            resolve(true)
          } else {
            resolve(false)
          }
        }
      })
    })
  },

  // 完成订单
  completeOrder(orderId) {
    const order = this.updateOrderStatus(orderId, 'completed', {
      completedAt: new Date().toISOString()
    })

    if (order) {
      wx.showToast({
        title: '订单已完成',
        icon: 'success'
      })

      // 跳转到订单列表
      setTimeout(() => {
        wx.switchTab({
          url: '/pages/orders/orders'
        })
      }, 1500)
    }
  },

  // 获取订单列表
  getOrders(status = 'all') {
    const orders = wx.getStorageSync('orders') || []

    if (status === 'all') {
      return orders
    }

    return orders.filter(order => order.status === status)
  }
}
```

### 4. 页面路由管理
```javascript
// utils/router.js
const RouterManager = {
  // 页面跳转封装
  navigateTo(url, params = {}) {
    // 检查登录状态（除登录页外）
    if (!url.includes('/pages/login/login') && !AuthManager.checkLogin()) {
      return
    }

    // 构建完整URL
    const fullUrl = this.buildUrl(url, params)

    wx.navigateTo({
      url: fullUrl,
      fail: (err) => {
        console.error('页面跳转失败:', err)
        wx.showToast({
          title: '页面跳转失败',
          icon: 'error'
        })
      }
    })
  },

  // Tab页面跳转
  switchTab(url) {
    if (!AuthManager.checkLogin()) {
      return
    }

    wx.switchTab({
      url: url,
      fail: (err) => {
        console.error('Tab跳转失败:', err)
      }
    })
  },

  // 重定向跳转
  redirectTo(url, params = {}) {
    const fullUrl = this.buildUrl(url, params)

    wx.redirectTo({
      url: fullUrl,
      fail: (err) => {
        console.error('重定向失败:', err)
      }
    })
  },

  // 返回上一页
  navigateBack(delta = 1) {
    wx.navigateBack({
      delta: delta,
      fail: (err) => {
        // 如果返回失败，跳转到首页
        this.switchTab('/pages/home/<USER>')
      }
    })
  },

  // 构建URL
  buildUrl(url, params) {
    if (Object.keys(params).length === 0) {
      return url
    }

    const queryString = Object.keys(params)
      .map(key => `${key}=${encodeURIComponent(params[key])}`)
      .join('&')

    return `${url}?${queryString}`
  },

  // 解析URL参数
  parseParams(url) {
    const params = {}
    const queryString = url.split('?')[1]

    if (queryString) {
      queryString.split('&').forEach(param => {
        const [key, value] = param.split('=')
        params[key] = decodeURIComponent(value)
      })
    }

    return params
  }
}
```

### 5. 错误处理与异常管理
```javascript
// utils/error.js
const ErrorManager = {
  // 统一错误处理
  handleError(error, context = '') {
    console.error(`[${context}] 错误:`, error)

    // 根据错误类型显示不同提示
    switch (error.type) {
      case 'NETWORK_ERROR':
        this.showNetworkError()
        break
      case 'AUTH_ERROR':
        this.handleAuthError()
        break
      case 'PAYMENT_ERROR':
        this.showPaymentError(error.message)
        break
      case 'VALIDATION_ERROR':
        this.showValidationError(error.message)
        break
      default:
        this.showGenericError(error.message)
    }
  },

  // 网络错误处理
  showNetworkError() {
    wx.showModal({
      title: '网络异常',
      content: '网络连接失败，请检查网络设置后重试',
      confirmText: '重试',
      cancelText: '取消',
      success: (res) => {
        if (res.confirm) {
          // 重新加载当前页面
          const pages = getCurrentPages()
          const currentPage = pages[pages.length - 1]
          if (currentPage.onLoad) {
            currentPage.onLoad(currentPage.options)
          }
        }
      }
    })
  },

  // 认证错误处理
  handleAuthError() {
    wx.showModal({
      title: '登录已过期',
      content: '请重新登录',
      showCancel: false,
      confirmText: '去登录',
      success: () => {
        AuthManager.logout()
      }
    })
  },

  // 支付错误处理
  showPaymentError(message) {
    wx.showModal({
      title: '支付失败',
      content: message || '支付过程中出现异常，请重试',
      confirmText: '重试',
      cancelText: '取消'
    })
  },

  // 验证错误处理
  showValidationError(message) {
    wx.showToast({
      title: message || '输入信息有误',
      icon: 'error',
      duration: 2000
    })
  },

  // 通用错误处理
  showGenericError(message) {
    wx.showToast({
      title: message || '操作失败，请重试',
      icon: 'error',
      duration: 2000
    })
  }
}
```

### 6. 数据验证与表单处理
```javascript
// utils/validator.js
const Validator = {
  // 手机号验证
  validatePhone(phone) {
    const phoneRegex = /^1[3-9]\d{9}$/
    return phoneRegex.test(phone)
  },

  // 验证码验证
  validateCode(code) {
    const codeRegex = /^\d{6}$/
    return codeRegex.test(code)
  },

  // 金额验证
  validateAmount(amount) {
    const num = parseFloat(amount)
    return !isNaN(num) && num > 0 && num <= 10000
  },

  // 订单数据验证
  validateOrderData(orderData) {
    const errors = []

    if (!orderData.deviceId) {
      errors.push('请选择设备')
    }

    if (!orderData.locationId) {
      errors.push('请选择拍摄地点')
    }

    if (!orderData.date || !orderData.time) {
      errors.push('请选择租赁时间')
    }

    if (!orderData.duration || orderData.duration <= 0) {
      errors.push('请选择租赁时长')
    }

    return {
      isValid: errors.length === 0,
      errors: errors
    }
  },

  // 表单提交前验证
  validateForm(formData, rules) {
    const errors = {}

    Object.keys(rules).forEach(field => {
      const rule = rules[field]
      const value = formData[field]

      // 必填验证
      if (rule.required && (!value || value.trim() === '')) {
        errors[field] = rule.message || `${field}不能为空`
        return
      }

      // 自定义验证
      if (rule.validator && !rule.validator(value)) {
        errors[field] = rule.message || `${field}格式不正确`
      }
    })

    return {
      isValid: Object.keys(errors).length === 0,
      errors: errors
    }
  }
}
```

## 开发注意事项

### 1. 权限申请
- 位置权限：用于地图功能
- 相机权限：用于拍照录像
- 存储权限：用于保存作品

### 2. 性能优化
- 图片懒加载
- 列表虚拟滚动
- 数据缓存策略
- 页面预加载

### 3. 用户体验
- 加载状态提示
- 错误处理和重试
- 网络异常处理
- 操作反馈

### 4. 安全考虑
- 用户数据加密存储
- API接口鉴权
- 支付安全验证
- 敏感信息保护

## 部署和发布

### 1. 开发环境
- 微信开发者工具
- 小程序测试号
- 本地调试服务器

### 2. 生产环境
- 小程序正式版本
- 生产服务器
- CDN资源部署
- 监控和日志

### 3. 发布流程
1. 代码提交和审核
2. 版本管理和发布
3. 用户反馈收集
4. 迭代优化

## 后续扩展

### 1. 功能扩展
- 实时视频流传输
- 多人协作拍摄
- AI智能拍摄建议
- 社交分享功能

### 2. 技术升级
- 云开发集成
- 实时数据库
- 云函数处理
- 智能推荐算法

这份文档提供了完整的微信小程序开发指南，包含了所有页面的详细实现方案和核心业务逻辑。开发团队可以根据这份文档进行具体的代码实现。
