# 地点页面接口文档

## 页面概述
地点选择页面，用户可以浏览、搜索拍摄地点，并选择地点进入订单确认流程。

## 当前实现分析

### 页面文件位置
- `pages/location/location.js` - 地点选择页逻辑
- `utils/mockData.js` - 模拟地点数据

### 当前功能流程
1. **地点列表加载**：从模拟数据加载所有拍摄地点
2. **搜索功能**：支持按地点名称、地址、特色搜索
3. **定位排序**：获取用户位置并按距离排序
4. **地点选择**：选择地点后跳转到订单确认页面
5. **设备验证**：确保用户已选择设备才能选择地点

## 需要替换的接口

### 1. 获取拍摄地点列表接口

#### 接口信息
- **接口名称**: 获取拍摄地点列表
- **请求方法**: GET
- **接口路径**: `/api/locations/list`
- **当前模拟位置**: `pages/location/location.js` 第104-126行 `loadLocations` 方法

#### 请求参数
```
page=1          // 页码，默认1
limit=50        // 每页数量，默认50
search=关键词   // 搜索关键词，可选
latitude=30.0   // 用户纬度，用于距离计算
longitude=120.0 // 用户经度，用于距离计算
recommended=true // 是否只返回推荐地点，可选
category=风景名胜 // 地点分类筛选，可选
```

#### 响应数据
```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "list": [
      {
        "id": "shusheng_01",
        "name": "书圣故里",
        "address": "绍兴市越城区蕺山街道",
        "distance": 2.3,
        "rating": 4.9,
        "reviewCount": 328,
        "price": 20,
        "unit": "次",
        "features": ["古典园林", "适合航拍", "白天开放", "免费停车"],
        "latitude": 30.0041,
        "longitude": 120.5804,
        "openTime": "08:00-17:30",
        "difficulty": "简单",
        "recommended": true,
        "images": ["图片URL1", "图片URL2", "图片URL3"],
        "description": "书圣故里历史文化街区，古色古香的建筑群，是航拍古建筑的绝佳地点。",
        "category": "风景名胜",
        "available": true,
        "weatherSuitable": true,
        "flightRestrictions": []
      }
    ],
    "pagination": {
      "page": 1,
      "limit": 50,
      "total": 20,
      "totalPages": 1,
      "hasNext": false,
      "hasPrev": false
    }
  }
}
```

### 2. 获取地点详情接口

#### 接口信息
- **接口名称**: 获取地点详细信息
- **请求方法**: GET
- **接口路径**: `/api/locations/detail/{id}`
- **当前模拟位置**: 暂无，需要新增

#### 请求参数
```
id: 地点ID（路径参数）
```

#### 响应数据
```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "id": "shusheng_01",
    "name": "书圣故里",
    "address": "绍兴市越城区蕺山街道",
    "distance": 2.3,
    "rating": 4.9,
    "reviewCount": 328,
    "price": 20,
    "unit": "次",
    "features": ["古典园林", "适合航拍", "白天开放", "免费停车"],
    "latitude": 30.0041,
    "longitude": 120.5804,
    "openTime": "08:00-17:30",
    "difficulty": "简单",
    "recommended": true,
    "images": ["图片URL1", "图片URL2", "图片URL3"],
    "description": "书圣故里历史文化街区，古色古香的建筑群，是航拍古建筑的绝佳地点。",
    "category": "风景名胜",
    "available": true,
    "weatherSuitable": true,
    "flightRestrictions": [],
    "facilities": ["停车场", "洗手间", "休息区"],
    "tips": ["建议上午拍摄光线较好", "注意保护古建筑", "遵守景区规定"],
    "reviews": [
      {
        "id": "review_001",
        "userId": "user_001",
        "userName": "用户昵称",
        "userAvatar": "头像URL",
        "rating": 5,
        "comment": "古建筑很美，航拍效果很棒！",
        "createTime": "2024-01-26T17:30:00.000Z",
        "images": ["评价图片URL1", "评价图片URL2"]
      }
    ],
    "relatedLocations": [
      {
        "id": "luxun_01",
        "name": "鲁迅故里",
        "distance": 1.5,
        "rating": 4.8,
        "image": "图片URL"
      }
    ]
  }
}
```

### 3. 地点搜索建议接口

#### 接口信息
- **接口名称**: 获取地点搜索建议
- **请求方法**: GET
- **接口路径**: `/api/locations/search-suggestions`
- **当前模拟位置**: `pages/location/location.js` 第131-152行 `filterLocations` 方法

#### 请求参数
```
keyword=关键词    // 搜索关键词
limit=10         // 返回数量限制，默认10
```

#### 响应数据
```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "suggestions": [
      {
        "id": "shusheng_01",
        "name": "书圣故里",
        "address": "绍兴市越城区蕺山街道",
        "category": "风景名胜",
        "matchType": "name"
      },
      {
        "id": "luxun_01", 
        "name": "鲁迅故里",
        "address": "绍兴市越城区鲁迅中路",
        "category": "文化景点",
        "matchType": "address"
      }
    ],
    "hotKeywords": ["书圣故里", "鲁迅故里", "兰亭景区", "东湖景区"]
  }
}
```

## 替换指导

### 1. 修改地点列表加载
**文件**: `pages/location/location.js`
**位置**: 第104-126行 `loadLocations` 方法

**当前代码**:
```javascript
async loadLocations() {
  if (!this.checkAuth()) return

  this.setData({ loading: true })

  try {
    const allLocations = [...mockLocations]
    this.setData({
      allLocations,
      filteredLocations: allLocations
    })
    
    console.log('地点数据加载完成：', allLocations.length, '个')
  } catch (error) {
    console.error('加载地点失败：', error)
    wx.showToast({
      title: '加载失败',
      icon: 'error'
    })
  } finally {
    this.setData({ loading: false })
  }
}
```

**替换为**:
```javascript
async loadLocations() {
  if (!this.checkAuth()) return

  this.setData({ loading: true })

  try {
    // 获取用户位置用于距离计算
    const location = await this.getUserLocation()
    
    const params = {
      page: 1,
      limit: 50
    }
    
    if (location) {
      params.latitude = location.latitude
      params.longitude = location.longitude
    }
    
    const response = await request.get('/api/locations/list', params)
    
    if (response && response.data) {
      this.setData({
        allLocations: response.data.list,
        filteredLocations: response.data.list
      })
      console.log('地点数据加载完成：', response.data.list.length, '个')
    }
  } catch (error) {
    console.error('加载地点失败：', error)
    wx.showToast({
      title: '加载失败',
      icon: 'error'
    })
    // 使用本地模拟数据作为降级方案
    this.loadLocationsFallback()
  } finally {
    this.setData({ loading: false })
  }
}
```

### 2. 修改搜索功能
**文件**: `pages/location/location.js`
**位置**: 第131-152行 `filterLocations` 方法

**替换为**:
```javascript
async filterLocations(keyword) {
  if (!keyword.trim()) {
    // 如果搜索关键词为空，显示所有地点
    this.setData({ filteredLocations: this.data.allLocations })
    return
  }

  try {
    const response = await request.get('/api/locations/search-suggestions', {
      keyword: keyword,
      limit: 20
    })
    
    if (response && response.data) {
      // 根据搜索建议获取完整地点信息
      const suggestionIds = response.data.suggestions.map(s => s.id)
      const filteredLocations = this.data.allLocations.filter(loc => 
        suggestionIds.includes(loc.id)
      )
      
      this.setData({ filteredLocations })
    }
  } catch (error) {
    console.error('搜索地点失败：', error)
    // 使用本地搜索作为降级方案
    this.filterLocationsLocal(keyword)
  }
}

// 本地搜索降级方案
filterLocationsLocal(keyword) {
  let filtered = [...this.data.allLocations]
  
  if (keyword.trim()) {
    filtered = filtered.filter(loc =>
      loc.name.includes(keyword) ||
      loc.address.includes(keyword) ||
      (loc.features && loc.features.some(feature => feature.includes(keyword)))
    )
  }
  
  this.setData({ filteredLocations: filtered })
}
```

### 3. 添加获取用户位置方法
**文件**: `pages/location/location.js`
**需要新增方法**:

```javascript
// 获取用户位置（可选）
getUserLocation() {
  return new Promise((resolve) => {
    wx.getLocation({
      type: 'wgs84',
      success: (res) => {
        console.log('获取位置成功：', res.latitude, res.longitude)
        resolve({
          latitude: res.latitude,
          longitude: res.longitude
        })
      },
      fail: (error) => {
        console.log('定位失败：', error)
        resolve(null) // 定位失败不影响主流程
      }
    })
  })
}
```

### 4. 修改定位排序功能
**文件**: `pages/location/location.js`
**位置**: 第157-188行 `getCurrentLocation` 方法

**替换为**:
```javascript
async getCurrentLocation() {
  wx.showLoading({ title: '定位中...' })
  
  try {
    const location = await this.getUserLocation()
    
    if (location) {
      this.setData({ currentLocation: '当前位置' })
      
      // 重新请求地点列表，包含位置信息进行服务端排序
      const response = await request.get('/api/locations/list', {
        page: 1,
        limit: 50,
        latitude: location.latitude,
        longitude: location.longitude
      })
      
      if (response && response.data) {
        this.setData({
          allLocations: response.data.list,
          filteredLocations: response.data.list
        })
      }
      
      wx.hideLoading()
      wx.showToast({
        title: '定位成功',
        icon: 'success'
      })
    } else {
      throw new Error('定位失败')
    }
  } catch (error) {
    console.error('定位失败：', error)
    wx.hideLoading()
    wx.showToast({
      title: '定位失败',
      icon: 'error'
    })
  }
}
```

## 错误处理和降级方案

### 降级方案实现
```javascript
// 地点列表降级方案
loadLocationsFallback() {
  const fallbackData = mockLocations
  this.setData({
    allLocations: fallbackData,
    filteredLocations: fallbackData
  })
}

// 本地搜索降级方案
filterLocationsLocal(keyword) {
  let filtered = [...this.data.allLocations]
  
  if (keyword.trim()) {
    filtered = filtered.filter(loc =>
      loc.name.includes(keyword) ||
      loc.address.includes(keyword) ||
      (loc.features && loc.features.some(feature => feature.includes(keyword)))
    )
  }
  
  this.setData({ filteredLocations: filtered })
}
```

## 注意事项

1. **位置权限**: 获取用户位置需要用户授权，失败时不影响主流程
2. **搜索优化**: 支持地点名称、地址、特色等多字段搜索
3. **距离计算**: 服务端计算距离比客户端更准确
4. **缓存策略**: 地点列表可以适当缓存，减少重复请求
5. **图片加载**: 地点图片需要支持懒加载和多尺寸
6. **实时状态**: 地点可用性需要实时更新

## 测试建议

1. 测试地点列表的加载功能
2. 测试搜索功能的准确性
3. 测试定位功能和距离排序
4. 测试网络异常时的降级方案
5. 测试地点选择后的页面跳转
6. 测试设备未选择时的提示逻辑
