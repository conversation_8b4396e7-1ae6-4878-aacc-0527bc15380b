# 逍遥境小程序 - 完整页面跳转流程图

## 🔄 核心业务流程

### 1. 用户认证流程
```mermaid
graph TD
    A[小程序启动] --> B{检查登录状态}
    B -->|已登录| C[跳转首页]
    B -->|未登录| D[显示登录页]
    
    D --> E{选择登录方式}
    E -->|微信登录| F[wx.login获取code]
    E -->|手机号登录| G[显示手机号输入框]
    
    F --> H[wx.getUserProfile获取用户信息]
    H --> I[发送到后端验证]
    I -->|成功| J[存储用户信息]
    I -->|失败| K[显示错误提示]
    K --> D
    
    G --> L[输入手机号]
    L --> M[发送验证码]
    M --> N[输入验证码]
    N --> O[验证码校验]
    O -->|成功| J
    O -->|失败| P[显示错误提示]
    P --> G
    
    J --> Q[wx.switchTab跳转首页]
```

### 2. 设备租赁完整流程
```mermaid
graph TD
    A[首页] --> B{用户操作}
    B -->|点击设备卡片| C[跳转设备详情]
    B -->|点击查看全部| D[跳转设备列表]
    B -->|点击快捷功能| E[对应功能页面]
    
    D --> F[设备列表页]
    F --> G{用户操作}
    G -->|筛选设备| H[刷新列表]
    G -->|点击设备| C
    G -->|返回| A
    
    C --> I[设备详情页]
    I --> J{用户操作}
    J -->|查看详情| K[展示设备信息]
    J -->|立即租赁| L{检查登录状态}
    J -->|返回| M[wx.navigateBack]
    
    L -->|未登录| N[wx.redirectTo登录页]
    L -->|已登录| O{检查余额}
    O -->|余额不足| P[提示充值]
    O -->|余额充足| Q[跳转地点选择]
    
    P --> R[wx.navigateTo充值页]
    R --> S[充值成功]
    S --> Q
    
    Q --> T[地点选择页]
    T --> U{用户操作}
    U -->|选择地点| V[更新选择状态]
    U -->|确认地点| W[跳转订单确认]
    U -->|返回| X[wx.navigateBack]
    
    W --> Y[订单确认页]
    Y --> Z{用户操作}
    Z -->|选择时间| AA[更新时间选择]
    Z -->|选择时长| BB[计算费用]
    Z -->|确认下单| CC{执行下单}
    Z -->|返回| DD[wx.navigateBack]
    
    CC -->|余额不足| EE[提示充值]
    CC -->|下单成功| FF[创建订单并支付]
    CC -->|下单失败| GG[显示错误提示]
    
    EE --> R
    FF --> HH[wx.redirectTo操控页]
    GG --> Y
    
    HH --> II[无人机操控页]
    II --> JJ{用户操作}
    JJ -->|实时操控| KK[操控功能]
    JJ -->|拍照录像| LL[保存作品]
    JJ -->|结束飞行| MM[完成订单]
    JJ -->|紧急停止| NN[强制结束]
    
    MM --> OO[更新订单状态]
    NN --> OO
    OO --> PP[wx.switchTab订单页]
```

### 3. 订单管理流程
```mermaid
graph TD
    A[订单页面] --> B{用户操作}
    B -->|查看订单| C[显示订单列表]
    B -->|筛选状态| D[刷新列表]
    B -->|继续操控| E[跳转操控页]
    B -->|取消订单| F{确认取消}
    B -->|查看作品| G[跳转作品集]
    
    F -->|确认| H[取消订单逻辑]
    F -->|取消| A
    H --> I[退还余额]
    I --> J[更新订单状态]
    J --> K[刷新列表]
    
    E --> L[wx.navigateTo操控页]
    G --> M[wx.navigateTo作品集]
```

### 4. 账户管理流程
```mermaid
graph TD
    A[个人中心] --> B{用户操作}
    B -->|余额充值| C[跳转充值页]
    B -->|我的作品| D[跳转作品集]
    B -->|我的订单| E[跳转订单页]
    B -->|设置| F[跳转设置页]
    B -->|退出登录| G{确认退出}
    
    C --> H[充值页面]
    H --> I{充值操作}
    I -->|选择金额| J[更新金额]
    I -->|选择支付方式| K[更新支付方式]
    I -->|确认充值| L[wx.requestPayment]
    I -->|返回| M[wx.navigateBack]
    
    L -->|支付成功| N[更新余额]
    L -->|支付失败| O[显示失败提示]
    L -->|支付取消| H
    
    N --> P[显示成功提示]
    P --> M
    O --> H
    
    G -->|确认| Q[清除本地数据]
    G -->|取消| A
    Q --> R[wx.redirectTo登录页]
```

## 📱 页面状态管理

### 1. 全局状态
```javascript
// app.js 全局状态
App({
  globalData: {
    userInfo: null,
    isLoggedIn: false,
    systemInfo: null,
    networkType: 'unknown'
  },

  onLaunch() {
    // 获取系统信息
    this.getSystemInfo()
    
    // 检查网络状态
    this.checkNetworkStatus()
    
    // 检查登录状态
    this.checkLoginStatus()
  },

  // 检查登录状态
  checkLoginStatus() {
    const isLoggedIn = wx.getStorageSync('isLoggedIn')
    const userInfo = wx.getStorageSync('userInfo')
    
    if (isLoggedIn && userInfo) {
      this.globalData.isLoggedIn = true
      this.globalData.userInfo = userInfo
    }
  },

  // 更新用户信息
  updateUserInfo(userInfo) {
    this.globalData.userInfo = userInfo
    wx.setStorageSync('userInfo', userInfo)
  },

  // 登录成功
  loginSuccess(userInfo) {
    this.globalData.isLoggedIn = true
    this.globalData.userInfo = userInfo
    wx.setStorageSync('isLoggedIn', true)
    wx.setStorageSync('userInfo', userInfo)
  },

  // 退出登录
  logout() {
    this.globalData.isLoggedIn = false
    this.globalData.userInfo = null
    wx.removeStorageSync('isLoggedIn')
    wx.removeStorageSync('userInfo')
  }
})
```

### 2. 页面状态管理
```javascript
// 页面状态管理示例
Page({
  data: {
    // 页面状态
    loading: false,
    error: null,
    
    // 业务数据
    deviceList: [],
    selectedDevice: null,
    
    // UI状态
    showModal: false,
    currentTab: 0,
    
    // 表单数据
    formData: {},
    formErrors: {}
  },

  // 状态更新方法
  updateState(newState) {
    this.setData(newState)
  },

  // 显示加载状态
  showLoading(message = '加载中...') {
    this.setData({ loading: true })
    wx.showLoading({ title: message })
  },

  // 隐藏加载状态
  hideLoading() {
    this.setData({ loading: false })
    wx.hideLoading()
  },

  // 显示错误状态
  showError(error) {
    this.setData({ error: error.message })
    wx.showToast({
      title: error.message,
      icon: 'error'
    })
  },

  // 清除错误状态
  clearError() {
    this.setData({ error: null })
  }
})
```

## 🔧 错误处理与重试机制

### 1. 网络错误处理
```javascript
// 网络请求错误处理
const NetworkManager = {
  async request(options) {
    const maxRetries = 3
    let retryCount = 0
    
    while (retryCount < maxRetries) {
      try {
        const result = await this.makeRequest(options)
        return result
      } catch (error) {
        retryCount++
        
        if (retryCount >= maxRetries) {
          throw error
        }
        
        // 等待后重试
        await this.delay(1000 * retryCount)
      }
    }
  },

  makeRequest(options) {
    return new Promise((resolve, reject) => {
      wx.request({
        ...options,
        success: resolve,
        fail: reject
      })
    })
  },

  delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms))
  }
}
```

### 2. 页面跳转错误处理
```javascript
// 安全的页面跳转
const SafeNavigator = {
  navigateTo(url, options = {}) {
    return new Promise((resolve, reject) => {
      wx.navigateTo({
        url,
        success: resolve,
        fail: (error) => {
          // 如果跳转失败，尝试其他方式
          if (error.errMsg.includes('limit exceed')) {
            // 页面栈超限，使用redirectTo
            wx.redirectTo({
              url,
              success: resolve,
              fail: reject
            })
          } else {
            reject(error)
          }
        }
      })
    })
  },

  switchTab(url) {
    return new Promise((resolve, reject) => {
      wx.switchTab({
        url,
        success: resolve,
        fail: reject
      })
    })
  }
}
```

## 📊 性能监控与优化

### 1. 页面性能监控
```javascript
// 页面性能监控
const PerformanceMonitor = {
  pageStartTime: 0,
  
  onPageStart() {
    this.pageStartTime = Date.now()
  },
  
  onPageReady() {
    const loadTime = Date.now() - this.pageStartTime
    console.log(`页面加载时间: ${loadTime}ms`)
    
    // 上报性能数据
    this.reportPerformance({
      loadTime,
      page: getCurrentPages().pop().route
    })
  },
  
  reportPerformance(data) {
    // 发送性能数据到后端
    wx.request({
      url: '/api/performance',
      method: 'POST',
      data
    })
  }
}
```

### 2. 内存管理
```javascript
// 内存管理
const MemoryManager = {
  // 清理页面数据
  cleanupPageData(page) {
    if (page.data.largeDataSet) {
      page.setData({
        largeDataSet: null
      })
    }
  },
  
  // 清理全局数据
  cleanupGlobalData() {
    const app = getApp()
    if (app.globalData.cache) {
      app.globalData.cache = {}
    }
  }
}
```

这份文档提供了完整的页面跳转逻辑、状态管理、错误处理和性能优化方案，确保小程序的稳定性和用户体验。
