<!--pages/gallery/gallery.wxml-->
<view class="gallery-container">
  <!-- 顶部导航 -->
  <view class="header">
    <view class="header-content">
      <view class="header-info">
        <text class="header-title">我的作品集</text>
        <text class="header-subtitle">共{{galleryStats.totalWorks}}张作品</text>
      </view>
    </view>
  </view>

  <!-- 筛选栏 -->
  <view class="filter-bar">
    <scroll-view class="filter-scroll" scroll-x="true">
      <view class="filter-list">
        <view
          class="filter-item {{currentCategory === 'all' ? 'active' : ''}}"
          bindtap="onCategoryChange"
          data-category="all"
        >
          全部 ({{galleryStats.totalWorks}})
        </view>
        <view
          class="filter-item {{currentCategory === 'photo' ? 'active' : ''}}"
          bindtap="onCategoryChange"
          data-category="photo"
        >
          照片 ({{galleryStats.photoCount}})
        </view>
        <view
          class="filter-item {{currentCategory === 'video' ? 'active' : ''}}"
          bindtap="onCategoryChange"
          data-category="video"
        >
          视频 ({{galleryStats.videoCount}})
        </view>
        <view
          class="filter-item {{currentCategory === 'recent' ? 'active' : ''}}"
          bindtap="onCategoryChange"
          data-category="recent"
        >
          最近7天
        </view>
      </view>
    </scroll-view>
  </view>

  <!-- 统计信息 -->
  <view class="stats-section">
    <view class="stats-grid">
      <view class="stats-item">
        <text class="stats-number">{{galleryStats.shootingDays}}</text>
        <text class="stats-label">拍摄天数</text>
      </view>
      <view class="stats-item">
        <text class="stats-number">{{galleryStats.storageSize}}</text>
        <text class="stats-label">存储空间</text>
      </view>
      <view class="stats-item">
        <text class="stats-number">{{galleryStats.shareCount}}</text>
        <text class="stats-label">分享次数</text>
      </view>
    </view>
  </view>

  <!-- 作品网格 -->
  <view class="media-grid" wx:if="{{filteredWorks.length > 0}}">
    <view
      class="media-item {{selectMode ? 'select-mode' : ''}}"
      wx:for="{{filteredWorks}}"
      wx:key="id"
      bindtap="handleMediaTap"
      data-work="{{item}}"
      data-index="{{index}}"
    >
      <!-- 选择模式复选框 -->
      <view class="select-checkbox" wx:if="{{selectMode}}">
        <text class="checkbox-icon">{{item.selected ? '✓' : ''}}</text>
      </view>

      <!-- 媒体内容 -->
      <image
        class="media-image"
        src="{{item.thumbnail || item.url}}"
        mode="aspectFill"
        lazy-load="true"
      />

      <!-- 视频播放图标 -->
      <view class="video-overlay" wx:if="{{item.type === 'video'}}">
        <text class="play-icon">▶</text>
      </view>

      <!-- 媒体信息 -->
      <view class="media-info">
        <view class="info-content">
          <text class="media-location">
            <text class="location-icon">{{item.type === 'video' ? '🎥' : '📷'}}</text>
            {{item.location}}
          </text>
          <text class="media-date">{{item.date}}</text>
        </view>
      </view>
    </view>

    <!-- 加载更多 -->
    <view class="load-more-item" bindtap="loadMore">
      <view class="load-more-content">
        <text class="load-more-icon">+</text>
        <text class="load-more-text">加载更多</text>
      </view>
    </view>
  </view>

  <!-- 空状态 -->
  <view class="empty-state" wx:if="{{filteredWorks.length === 0 && !loading}}">
    <text class="empty-icon">📸</text>
    <text class="empty-title">暂无作品</text>
    <text class="empty-desc">开始你的第一次飞行，记录美好瞬间</text>
    <button class="btn btn-primary" bindtap="goToEquipment">去租赁设备</button>
  </view>

  <!-- 加载状态 -->
  <view class="loading-state" wx:if="{{loading}}">
    <text class="loading-icon">⏳</text>
    <text class="loading-text">加载中...</text>
  </view>
</view>

<!-- 媒体查看模态框 -->
<view class="media-modal {{showModal ? 'show' : ''}}" bindtap="closeModal">
  <view class="modal-content" catchtap="stopPropagation">
    <view class="media-container">
      <image
        class="modal-image"
        src="{{currentMedia.url}}"
        mode="aspectFit"
        wx:if="{{currentMedia.type === 'photo'}}"
      />

      <video
        class="modal-video"
        src="{{currentMedia.url}}"
        wx:if="{{currentMedia.type === 'video'}}"
      />

      <button class="modal-close" bindtap="closeModal">
        <text class="close-icon">×</text>
      </button>
    </view>

    <!-- 底部操作栏 -->
    <view class="modal-actions">
      <button class="action-btn" bindtap="downloadMedia">
        <text class="action-icon">⬇</text>
      </button>
      <button class="action-btn" bindtap="shareMedia">
        <text class="action-icon">↗</text>
      </button>
      <button class="action-btn" bindtap="editMedia">
        <text class="action-icon">✎</text>
      </button>
      <button class="action-btn" bindtap="deleteMedia">
        <text class="action-icon">🗑</text>
      </button>
    </view>

    <!-- 媒体信息 -->
    <view class="modal-info">
      <text class="modal-title">{{currentMedia.title}}</text>
      <view class="modal-details">
        <text class="detail-item">拍摄时间: {{currentMedia.createTime}}</text>
        <text class="detail-item">设备: {{currentMedia.device}}</text>
        <text class="detail-item" wx:if="{{currentMedia.type === 'photo'}}">分辨率: {{currentMedia.resolution}}</text>
        <text class="detail-item" wx:if="{{currentMedia.type === 'video'}}">时长: {{currentMedia.duration}}</text>
        <text class="detail-item">文件大小: {{currentMedia.fileSize}}</text>
      </view>
    </view>
  </view>
</view>