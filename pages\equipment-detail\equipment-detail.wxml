<!--pages/equipment-detail/equipment-detail.wxml-->
<view class="detail-container">
  <!-- 设备图片区域 -->
  <view class="image-section">
    <image
      class="equipment-image"
      src="{{equipmentDetail.image || 'http://iph.href.lu/750x640'}}"
      mode="aspectFill"
    />

    <!-- 返回按钮 -->
    <view class="back-btn" bindtap="goBack">
      <text class="back-icon">‹</text>
    </view>

    <!-- 收藏按钮 -->
    <view class="favorite-btn" bindtap="toggleFavorite">
      <text class="favorite-icon">{{isFavorite ? '♥' : '♡'}}</text>
    </view>

    <!-- 状态标签 -->
    <view class="status-badge {{equipmentDetail.available ? 'available' : 'unavailable'}}">
      {{equipmentDetail.available ? '可租用' : '使用中'}}
    </view>
  </view>

  <!-- 设备信息区域 -->
  <view class="info-section">
    <!-- 基本信息 -->
    <view class="basic-info">
      <text class="equipment-name">{{equipmentDetail.name}}</text>
      <text class="equipment-desc">{{equipmentDetail.description}}</text>

      <view class="rating-price-row">
        <view class="rating-section">
          <view class="stars">
            <text class="star" wx:for="{{5}}" wx:key="*this">⭐</text>
          </view>
          <text class="rating-text">{{equipmentDetail.rating}} ({{equipmentDetail.reviewCount}}条评价)</text>
        </view>

        <view class="price-section">
          <text class="price-amount">¥{{equipmentDetail.price}}</text>
          <text class="price-unit">/{{equipmentDetail.unit}}</text>
        </view>
      </view>
    </view>

    <!-- 设备特点 -->
    <view class="features-section">
      <text class="section-title">设备特点</text>
      <view class="features-grid">
        <view class="feature-card">
          <text class="feature-icon">📹</text>
          <text class="feature-title">4K/60fps</text>
          <text class="feature-subtitle">高清录制</text>
        </view>
        <view class="feature-card">
          <text class="feature-icon">🕐</text>
          <text class="feature-title">46分钟</text>
          <text class="feature-subtitle">最大续航</text>
        </view>
        <view class="feature-card">
          <text class="feature-icon">📷</text>
          <text class="feature-title">双主摄</text>
          <text class="feature-subtitle">广角+中焦</text>
        </view>
        <view class="feature-card">
          <text class="feature-icon">🛡️</text>
          <text class="feature-title">避障</text>
          <text class="feature-subtitle">全向感知</text>
        </view>
      </view>
    </view>

    <!-- 技术规格 -->
    <view class="specs-section">
      <text class="section-title">技术规格</text>
      <view class="specs-list">
        <view class="spec-item" wx:for="{{specsArray}}" wx:key="key">
          <text class="spec-label">{{item.label}}</text>
          <text class="spec-value">{{item.value}}</text>
        </view>
      </view>
    </view>

    <!-- 包含配件 -->
    <view class="accessories-section">
      <text class="section-title">包含配件</text>
      <view class="accessories-grid">
        <view class="accessory-item" wx:for="{{accessories}}" wx:key="*this">
          <text class="check-icon">✓</text>
          <text class="accessory-text">{{item}}</text>
        </view>
      </view>
    </view>

    <!-- 用户评价 -->
    <view class="reviews-section">
      <text class="section-title">用户评价</text>
      <view class="review-card">
        <view class="review-header">
          <view class="user-avatar">
            <text class="avatar-icon">👤</text>
          </view>
          <view class="user-info">
            <text class="user-name">张先生</text>
            <view class="review-stars">
              <text class="star" wx:for="{{5}}" wx:key="*this">⭐</text>
            </view>
          </view>
        </view>
        <text class="review-text">画质非常棒，操控简单，特别适合拍摄风景。续航时间也很给力，推荐！</text>
      </view>
    </view>
  </view>
</view>

<!-- 底部操作栏 -->
<view class="bottom-bar">
  <view class="bottom-actions">
    <button class="favorite-action-btn" bindtap="addToFavorites">
      <text class="action-icon">♡</text>
      <text class="action-text">收藏</text>
    </button>
    <button
      class="rent-action-btn {{equipmentDetail.available ? '' : 'disabled'}}"
      bindtap="startRental"
      disabled="{{!equipmentDetail.available}}"
    >
      {{equipmentDetail.available ? '立即租用' : '暂不可用'}}
    </button>
  </view>
</view>