# 作品集页面接口文档

## 页面概述
作品集页面，用户可以查看、管理自己拍摄的照片和视频作品，支持筛选、删除、分享等功能。

## 当前实现分析

### 页面文件位置
- `pages/gallery/gallery.js` - 作品集页逻辑
- `utils/galleryManager.js` - 作品集管理工具

### 当前功能流程
1. **作品列表加载**：获取用户所有拍摄作品
2. **统计数据显示**：显示总作品数、照片数、视频数等统计信息
3. **分类筛选**：按全部/照片/视频/最近等条件筛选
4. **作品管理**：
   - 查看作品详情
   - 删除作品
   - 分享作品
   - 下载作品
5. **订单筛选**：可以按特定订单筛选作品

## 需要替换的接口

### 1. 获取用户作品集接口

#### 接口信息
- **接口名称**: 获取用户作品集列表
- **请求方法**: GET
- **接口路径**: `/api/gallery/works`
- **当前模拟位置**: `pages/gallery/gallery.js` 第117-165行 `loadGalleryData` 方法

#### 请求头
```
Authorization: Bearer {token}
```

#### 请求参数
```
page=1              // 页码
limit=50            // 每页数量
type=all            // 作品类型：all/photo/video
category=all        // 分类：all/recent/order
orderId=order_001   // 订单ID筛选，可选
startDate=2024-01-01 // 开始日期，可选
endDate=2024-01-31   // 结束日期，可选
```

#### 响应数据
```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "list": [
      {
        "id": "work_001",
        "type": "photo",
        "filename": "DJI_20240127_103000.jpg",
        "title": "书圣故里航拍",
        "description": "古建筑群的美丽航拍",
        "fileSize": 8.5,
        "resolution": "4000x3000",
        "duration": null,
        "createTime": "2024-01-27T10:30:00.000Z",
        "orderId": "order_001",
        "equipmentName": "DJI Air 3",
        "locationName": "书圣故里",
        "downloadUrl": "https://storage.example.com/photos/DJI_20240127_103000.jpg",
        "thumbnailUrl": "https://storage.example.com/thumbnails/DJI_20240127_103000_thumb.jpg",
        "shareCount": 5,
        "downloadCount": 2,
        "metadata": {
          "location": {
            "latitude": 30.0041,
            "longitude": 120.5804,
            "altitude": 125.5
          },
          "cameraSettings": {
            "iso": "200",
            "shutter": "1/250",
            "aperture": "f/2.8"
          }
        }
      },
      {
        "id": "work_002",
        "type": "video",
        "filename": "DJI_20240127_103500.mp4",
        "title": "鲁迅故里风光",
        "description": "江南水乡的动态美景",
        "fileSize": 125.8,
        "resolution": "4K",
        "duration": 45,
        "createTime": "2024-01-27T10:35:00.000Z",
        "orderId": "order_002",
        "equipmentName": "DJI Air 3",
        "locationName": "鲁迅故里",
        "downloadUrl": "https://storage.example.com/videos/DJI_20240127_103500.mp4",
        "thumbnailUrl": "https://storage.example.com/thumbnails/DJI_20240127_103500_thumb.jpg",
        "shareCount": 8,
        "downloadCount": 3
      }
    ],
    "pagination": {
      "page": 1,
      "limit": 50,
      "total": 45,
      "totalPages": 1,
      "hasNext": false,
      "hasPrev": false
    }
  }
}
```

### 2. 获取作品集统计数据接口

#### 接口信息
- **接口名称**: 获取用户作品集统计信息
- **请求方法**: GET
- **接口路径**: `/api/gallery/stats`
- **当前模拟位置**: `pages/gallery/gallery.js` 第117-165行 `loadGalleryData` 方法中的统计计算

#### 请求头
```
Authorization: Bearer {token}
```

#### 响应数据
```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "totalWorks": 45,
    "photoCount": 32,
    "videoCount": 13,
    "shootingDays": 8,
    "storageSize": "2.1GB",
    "shareCount": 156,
    "downloadCount": 89,
    "totalFlightTime": 720,
    "favoriteCount": 12,
    "recentWorks": 8
  }
}
```

### 3. 删除作品接口

#### 接口信息
- **接口名称**: 删除用户作品
- **请求方法**: DELETE
- **接口路径**: `/api/gallery/works/{workId}`
- **当前模拟位置**: `pages/gallery/gallery.js` 第310-333行 `deleteWork` 方法

#### 请求头
```
Authorization: Bearer {token}
```

#### 请求参数
```
workId: 作品ID（路径参数）
```

#### 响应数据
```json
{
  "code": 200,
  "message": "删除成功",
  "data": {
    "workId": "work_001",
    "deleteTime": "2024-01-27T15:00:00.000Z",
    "freedSpace": 8.5
  }
}
```

### 4. 分享作品接口

#### 接口信息
- **接口名称**: 分享作品（记录分享统计）
- **请求方法**: POST
- **接口路径**: `/api/gallery/share`
- **当前模拟位置**: `pages/gallery/gallery.js` 第280-309行 `shareWork` 方法

#### 请求头
```
Authorization: Bearer {token}
```

#### 请求参数
```json
{
  "workId": "work_001",
  "shareType": "wechat",
  "shareTarget": "friend"
}
```

#### 分享类型
- `wechat` - 微信分享
- `moments` - 朋友圈
- `download` - 下载分享
- `link` - 链接分享

#### 响应数据
```json
{
  "code": 200,
  "message": "分享成功",
  "data": {
    "shareId": "share_001",
    "workId": "work_001",
    "shareType": "wechat",
    "shareTime": "2024-01-27T15:00:00.000Z",
    "shareUrl": "https://example.com/share/work_001",
    "totalShareCount": 6
  }
}
```

### 5. 获取作品详情接口

#### 接口信息
- **接口名称**: 获取作品详细信息
- **请求方法**: GET
- **接口路径**: `/api/gallery/works/{workId}/detail`
- **当前模拟位置**: `pages/gallery/gallery.js` 第250-279行 `viewWork` 方法

#### 请求头
```
Authorization: Bearer {token}
```

#### 请求参数
```
workId: 作品ID（路径参数）
```

#### 响应数据
```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "id": "work_001",
    "type": "photo",
    "filename": "DJI_20240127_103000.jpg",
    "title": "书圣故里航拍",
    "description": "古建筑群的美丽航拍",
    "fileSize": 8.5,
    "resolution": "4000x3000",
    "createTime": "2024-01-27T10:30:00.000Z",
    "orderId": "order_001",
    "equipmentName": "DJI Air 3",
    "locationName": "书圣故里",
    "downloadUrl": "https://storage.example.com/photos/DJI_20240127_103000.jpg",
    "originalUrl": "https://storage.example.com/originals/DJI_20240127_103000.jpg",
    "thumbnailUrl": "https://storage.example.com/thumbnails/DJI_20240127_103000_thumb.jpg",
    "shareCount": 5,
    "downloadCount": 2,
    "viewCount": 25,
    "metadata": {
      "location": {
        "latitude": 30.0041,
        "longitude": 120.5804,
        "altitude": 125.5,
        "address": "绍兴市越城区蕺山街道"
      },
      "cameraSettings": {
        "iso": "200",
        "shutter": "1/250",
        "aperture": "f/2.8",
        "focal": "24mm",
        "whiteBalance": "auto"
      },
      "weather": {
        "condition": "晴",
        "temperature": "22°C",
        "windSpeed": "3级"
      }
    },
    "tags": ["古建筑", "航拍", "绍兴", "文化"],
    "relatedWorks": [
      {
        "id": "work_003",
        "thumbnailUrl": "https://storage.example.com/thumbnails/work_003_thumb.jpg",
        "title": "书圣故里夜景"
      }
    ]
  }
}
```

### 6. 批量操作作品接口

#### 接口信息
- **接口名称**: 批量删除或操作作品
- **请求方法**: POST
- **接口路径**: `/api/gallery/batch-operation`
- **当前模拟位置**: `pages/gallery/gallery.js` 选择模式相关功能

#### 请求头
```
Authorization: Bearer {token}
```

#### 请求参数
```json
{
  "operation": "delete",
  "workIds": ["work_001", "work_002", "work_003"]
}
```

#### 操作类型
- `delete` - 批量删除
- `download` - 批量下载
- `share` - 批量分享
- `tag` - 批量标签

#### 响应数据
```json
{
  "code": 200,
  "message": "批量操作成功",
  "data": {
    "operation": "delete",
    "successCount": 3,
    "failedCount": 0,
    "freedSpace": 25.3,
    "results": [
      {
        "workId": "work_001",
        "status": "success"
      },
      {
        "workId": "work_002",
        "status": "success"
      },
      {
        "workId": "work_003",
        "status": "success"
      }
    ]
  }
}
```

## 替换指导

### 1. 修改作品集数据加载
**文件**: `pages/gallery/gallery.js`
**位置**: 第117-165行 `loadGalleryData` 方法

**当前代码**:
```javascript
async loadGalleryData() {
  if (!this.checkAuth()) return

  this.setData({ loading: true })

  try {
    // 获取用户作品集数据
    let works = galleryManager.getUserGallery()

    // 如果指定了订单ID，只显示该订单的作品
    if (this.data.filterOrderId) {
      works = works.filter(work => work.orderId === this.data.filterOrderId)
    }

    // 计算统计数据
    const galleryStats = this.calculateStats(works)

    this.setData({
      allWorks: works,
      filteredWorks: works,
      galleryStats
    })

    this.applyFilter()

    console.log('作品集数据加载完成：', works.length, '个作品')
  } catch (error) {
    console.error('加载作品集失败：', error)
    wx.showToast({
      title: '加载失败',
      icon: 'error'
    })
  } finally {
    this.setData({ loading: false })
  }
}
```

**替换为**:
```javascript
async loadGalleryData() {
  if (!this.checkAuth()) return

  this.setData({ loading: true })

  try {
    // 构建请求参数
    const params = {
      page: 1,
      limit: 100,
      type: 'all',
      category: this.data.currentCategory
    }

    // 如果指定了订单ID，添加筛选条件
    if (this.data.filterOrderId) {
      params.orderId = this.data.filterOrderId
      params.category = 'order'
    }

    // 获取作品列表和统计数据
    const [worksResponse, statsResponse] = await Promise.all([
      request.get('/api/gallery/works', params),
      request.get('/api/gallery/stats')
    ])

    if (worksResponse && worksResponse.data && statsResponse && statsResponse.data) {
      this.setData({
        allWorks: worksResponse.data.list,
        filteredWorks: worksResponse.data.list,
        galleryStats: statsResponse.data
      })

      this.applyFilter()
      console.log('作品集数据加载完成：', worksResponse.data.list.length, '个作品')
    }
  } catch (error) {
    console.error('加载作品集失败：', error)
    wx.showToast({
      title: '加载失败',
      icon: 'error'
    })
    // 使用本地数据作为降级方案
    this.loadGalleryDataLocal()
  } finally {
    this.setData({ loading: false })
  }
}

// 本地数据降级方案
loadGalleryDataLocal() {
  let works = galleryManager.getUserGallery()
  
  if (this.data.filterOrderId) {
    works = works.filter(work => work.orderId === this.data.filterOrderId)
  }
  
  const galleryStats = this.calculateStats(works)
  
  this.setData({
    allWorks: works,
    filteredWorks: works,
    galleryStats
  })
  
  this.applyFilter()
}
```

### 2. 修改删除作品功能
**文件**: `pages/gallery/gallery.js`
**位置**: 第310-333行 `deleteWork` 方法

**替换为**:
```javascript
deleteWork(e) {
  const workId = e.currentTarget.dataset.workId
  const work = this.data.allWorks.find(w => w.id === workId)

  wx.showModal({
    title: '删除作品',
    content: `确定要删除"${work.title || work.filename}"吗？删除后无法恢复`,
    confirmText: '删除',
    confirmColor: '#ef4444',
    success: async (res) => {
      if (res.confirm) {
        try {
          const response = await request.delete(`/api/gallery/works/${workId}`)
          
          if (response && response.data) {
            // 从本地列表中移除
            const allWorks = this.data.allWorks.filter(w => w.id !== workId)
            const filteredWorks = this.data.filteredWorks.filter(w => w.id !== workId)
            
            this.setData({ allWorks, filteredWorks })
            
            // 重新计算统计数据
            const galleryStats = this.calculateStats(allWorks)
            this.setData({ galleryStats })
            
            wx.showToast({
              title: `删除成功，释放${response.data.freedSpace}MB空间`,
              icon: 'success'
            })
          }
        } catch (error) {
          console.error('删除作品失败：', error)
          wx.showToast({
            title: '删除失败',
            icon: 'error'
          })
        }
      }
    }
  })
}
```

### 3. 修改分享功能
**文件**: `pages/gallery/gallery.js`
**位置**: 第280-309行 `shareWork` 方法

**替换为**:
```javascript
async shareWork(e) {
  const workId = e.currentTarget.dataset.workId
  const work = this.data.allWorks.find(w => w.id === workId)

  try {
    // 记录分享统计
    const response = await request.post('/api/gallery/share', {
      workId: workId,
      shareType: 'wechat',
      shareTarget: 'friend'
    })

    // 微信分享
    wx.showShareMenu({
      withShareTicket: true,
      menus: ['shareAppMessage', 'shareTimeline']
    })

    // 或者直接调用分享
    wx.shareAppMessage({
      title: work.title || '我的航拍作品',
      path: `/pages/gallery/work-detail?workId=${workId}`,
      imageUrl: work.thumbnailUrl,
      success: () => {
        if (response && response.data) {
          wx.showToast({
            title: `分享成功！总分享${response.data.totalShareCount}次`,
            icon: 'success'
          })
        }
      }
    })
  } catch (error) {
    console.error('分享失败：', error)
    // 即使接口失败也允许分享
    wx.shareAppMessage({
      title: work.title || '我的航拍作品',
      path: `/pages/gallery/work-detail?workId=${workId}`,
      imageUrl: work.thumbnailUrl
    })
  }
}
```

## 错误处理和降级方案

### 降级方案实现
```javascript
// 作品集数据降级方案
loadGalleryDataLocal() {
  let works = galleryManager.getUserGallery()
  
  if (this.data.filterOrderId) {
    works = works.filter(work => work.orderId === this.data.filterOrderId)
  }
  
  const galleryStats = this.calculateStats(works)
  
  this.setData({
    allWorks: works,
    filteredWorks: works,
    galleryStats
  })
}

// 统计数据计算
calculateStats(works) {
  const photoCount = works.filter(w => w.type === 'photo').length
  const videoCount = works.filter(w => w.type === 'video').length
  const totalSize = works.reduce((sum, w) => sum + (w.fileSize || 0), 0)
  
  return {
    totalWorks: works.length,
    photoCount,
    videoCount,
    storageSize: `${(totalSize / 1024).toFixed(1)}GB`,
    shootingDays: new Set(works.map(w => w.createTime.split('T')[0])).size
  }
}
```

## 注意事项

1. **文件管理**: 需要合理的文件存储和CDN分发策略
2. **缓存策略**: 作品列表和缩略图需要适当缓存
3. **权限控制**: 确保用户只能操作自己的作品
4. **存储优化**: 提供不同质量的图片和视频版本
5. **分享统计**: 准确记录分享和下载统计
6. **批量操作**: 支持批量删除和下载功能

## 测试建议

1. 测试作品列表的加载和筛选
2. 测试作品的删除和批量操作
3. 测试分享功能和统计记录
4. 测试网络异常时的降级方案
5. 测试大量作品时的性能
6. 测试不同文件格式的支持
