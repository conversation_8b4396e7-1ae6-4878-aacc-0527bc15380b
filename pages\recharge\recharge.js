// pages/recharge/recharge.js
const app = getApp()
const auth = require('../../utils/auth.js')

Page({

  /**
   * 页面的初始数据
   */
  data: {
    // 用户余额
    userBalance: '150.00',
    availableHours: '2',

    // 充值选项
    amountOptions: [
      { value: 50, hours: '0.6' },
      { value: 100, hours: '1.2' },
      { value: 200, hours: '2.5' },
      { value: 500, hours: '6' }
    ],

    // 支付方式
    paymentMethods: [
      {
        type: 'wechat',
        name: '微信支付',
        desc: '推荐使用，快速便捷',
        icon: '💚',
        colorClass: 'green'
      },
      {
        type: 'alipay',
        name: '支付宝',
        desc: '安全可靠，操作简单',
        icon: '💙',
        colorClass: 'blue'
      }
    ],

    // 选择状态
    selectedAmount: 0,
    customAmount: '',
    selectedPayment: '',

    // 计算结果
    finalAmount: 0,
    bonusAmount: 0,
    canRecharge: false,
    confirmBtnText: '请选择充值金额和支付方式'
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    console.log('充值页面加载')
    this.checkAuth()
    this.loadUserBalance()
    this.updateConfirmButton()
  },

  /**
   * 返回上一页
   */
  goBack() {
    wx.navigateBack()
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {

  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {

  },

  /**
   * 检查认证状态
   */
  checkAuth() {
    if (!auth.checkLoginStatus()) {
      auth.redirectToLogin('/pages/recharge/recharge')
      return false
    }
    return true
  },

  /**
   * 加载用户余额
   */
  loadUserBalance() {
    const userInfo = auth.getCurrentUser()
    if (userInfo) {
      this.setData({
        userBalance: userInfo.balance || '150.00',
        availableHours: Math.floor((userInfo.balance || 150) / 75 * 10) / 10 || '2'
      })
    }
  },

  /**
   * 选择充值金额
   */
  selectAmount(e) {
    const amount = e.currentTarget.dataset.amount
    this.setData({
      selectedAmount: amount,
      customAmount: ''
    })
    this.updateConfirmButton()
  },

  /**
   * 自定义金额输入
   */
  onCustomAmountInput(e) {
    const value = e.detail.value
    // 允许用户输入，但只在有效范围内设置selectedAmount
    this.setData({
      customAmount: value
    })

    if (value && !isNaN(value)) {
      const numValue = parseFloat(value)
      if (numValue >= 10 && numValue <= 5000) {
        this.setData({
          selectedAmount: numValue
        })
      } else {
        this.setData({
          selectedAmount: 0
        })
        // 只在用户停止输入时显示提示，避免频繁弹窗
        if (this.inputTimer) {
          clearTimeout(this.inputTimer)
        }
        this.inputTimer = setTimeout(() => {
          if (numValue < 10 || numValue > 5000) {
            wx.showToast({
              title: '充值金额须在¥10-¥5000之间',
              icon: 'none',
              duration: 2000
            })
          }
        }, 1000)
      }
    } else {
      this.setData({
        selectedAmount: 0
      })
    }
    this.updateConfirmButton()
  },

  /**
   * 选择支付方式
   */
  selectPayment(e) {
    const type = e.currentTarget.dataset.type
    this.setData({
      selectedPayment: type
    })
    this.updateConfirmButton()
  },

  /**
   * 更新确认按钮状态
   */
  updateConfirmButton() {
    const { selectedAmount, selectedPayment } = this.data

    if (selectedAmount > 0 && selectedPayment) {
      this.setData({
        canRecharge: true,
        confirmBtnText: `确认充值 ¥${selectedAmount.toFixed(2)}`
      })
    } else {
      this.setData({
        canRecharge: false,
        confirmBtnText: '请选择充值金额和支付方式'
      })
    }
  },

  /**
   * 查看充值记录
   */
  viewRechargeHistory() {
    wx.showModal({
      title: '充值记录',
      content: '2024-11-30 14:30  +¥100.00  微信支付\n2024-11-25 09:15  +¥200.00  支付宝\n2024-11-20 16:45  +¥50.00   微信支付\n\n总充值：¥350.00',
      showCancel: false
    })
  },
  /**
   * 确认充值
   */
  async confirmRecharge() {
    if (!this.checkAuth()) return

    const { selectedAmount, selectedPayment } = this.data

    if (selectedAmount <= 0 || !selectedPayment) {
      wx.showToast({
        title: '请选择充值金额和支付方式',
        icon: 'none'
      })
      return
    }

    // 计算优惠
    let bonusAmount = 0
    const isFirstRecharge = false // 模拟首次充值状态

    if (isFirstRecharge) {
      bonusAmount = selectedAmount * 0.1 // 首次充值送10%
    }

    const finalAmount = selectedAmount + bonusAmount
    const paymentName = selectedPayment === 'wechat' ? '微信支付' : '支付宝'

    const confirmInfo = `充值确认\n\n充值金额：¥${selectedAmount.toFixed(2)}${bonusAmount > 0 ? `\n首次充值奖励：¥${bonusAmount.toFixed(2)}` : ''}${selectedAmount >= 200 ? '\n额外赠送：¥30优惠券' : ''}\n到账金额：¥${finalAmount.toFixed(2)}\n支付方式：${paymentName}\n充值后余额：¥${(parseFloat(this.data.userBalance) + finalAmount).toFixed(2)}\n\n确认充值？`

    const res = await wx.showModal({
      title: '充值确认',
      content: confirmInfo,
      confirmText: '确认',
      cancelText: '取消'
    })

    if (!res.confirm) return

    try {
      // 显示支付处理中
      this.setData({
        confirmBtnText: '支付处理中...',
        canRecharge: false
      })

      // 模拟支付延迟
      await new Promise(resolve => setTimeout(resolve, 2000))

      // 支付成功
      const newBalance = parseFloat(this.data.userBalance) + finalAmount

      // 更新用户余额
      const userInfo = auth.getCurrentUser()
      auth.updateUserInfo({
        ...userInfo,
        balance: newBalance
      })

      this.setData({
        userBalance: newBalance.toFixed(2),
        availableHours: Math.floor(newBalance / 75 * 10) / 10
      })

      wx.showModal({
        title: '充值成功',
        content: `充值金额：¥${finalAmount.toFixed(2)}\n当前余额：¥${newBalance.toFixed(2)}\n\n感谢您使用${paymentName}!`,
        showCancel: false
      })

      // 重置界面
      this.resetForm()

      console.log('充值成功，金额：', finalAmount)
    } catch (error) {
      console.error('充值失败：', error)
      wx.showToast({
        title: '充值失败',
        icon: 'error'
      })
      this.updateConfirmButton()
    }
  },

  /**
   * 重置表单
   */
  resetForm() {
    this.setData({
      selectedAmount: 0,
      customAmount: '',
      selectedPayment: '',
      canRecharge: false,
      confirmBtnText: '请选择充值金额和支付方式'
    })
  }
})