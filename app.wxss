/**
 * 逍遥境小程序全局样式
 * 基于"高级线条·简约·丝滑"设计理念
 * 使用直接样式值确保兼容性
 */

/* ===== 基础重置样式 ===== */
page {
  background-color: #f9fafb;
  color: #0b0f19;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", "Helvetica Neue", Helvetica, Arial, sans-serif;
  font-size: 32rpx;
  line-height: 1.6;
}

view, text, button, input, image, scroll-view {
  box-sizing: border-box;
}

/* ===== 颜色系统 ===== */
.text-primary { color: #0b0f19; }
.text-secondary { color: #6b7280; }
.text-tertiary { color: #9ca3af; }
.text-accent { color: #10a37f; }
.text-danger { color: #ef4444; }
.text-warning { color: #f59e0b; }
.text-success { color: #10b981; }
.text-white { color: #ffffff; }

.bg-primary { background-color: #ffffff; }
.bg-secondary { background-color: #f9fafb; }
.bg-tertiary { background-color: #f3f4f6; }
.bg-accent { background-color: #10a37f; }
.bg-danger { background-color: #ef4444; }
.bg-warning { background-color: #f59e0b; }
.bg-success { background-color: #10b981; }
.bg-dark { background-color: #1f2937; }

/* ===== 字体系统 ===== */
.text-xs { font-size: 24rpx; }
.text-sm { font-size: 28rpx; }
.text-base { font-size: 32rpx; }
.text-lg { font-size: 36rpx; }
.text-xl { font-size: 40rpx; }
.text-2xl { font-size: 48rpx; }
.text-3xl { font-size: 60rpx; }

.font-normal { font-weight: 400; }
.font-medium { font-weight: 500; }
.font-semibold { font-weight: 600; }
.font-bold { font-weight: 700; }

.text-left { text-align: left; }
.text-center { text-align: center; }
.text-right { text-align: right; }

.text-ellipsis {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

.text-line-clamp-2 {
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
}

/* ===== 间距系统 ===== */
.p-0 { padding: 0; }
.p-1 { padding: 8rpx; }
.p-2 { padding: 16rpx; }
.p-3 { padding: 24rpx; }
.p-4 { padding: 32rpx; }
.p-6 { padding: 48rpx; }
.p-8 { padding: 64rpx; }

.px-0 { padding-left: 0; padding-right: 0; }
.px-1 { padding-left: 8rpx; padding-right: 8rpx; }
.px-2 { padding-left: 16rpx; padding-right: 16rpx; }
.px-3 { padding-left: 24rpx; padding-right: 24rpx; }
.px-4 { padding-left: 32rpx; padding-right: 32rpx; }
.px-6 { padding-left: 48rpx; padding-right: 48rpx; }
.px-8 { padding-left: 64rpx; padding-right: 64rpx; }

.py-0 { padding-top: 0; padding-bottom: 0; }
.py-1 { padding-top: 8rpx; padding-bottom: 8rpx; }
.py-2 { padding-top: 16rpx; padding-bottom: 16rpx; }
.py-3 { padding-top: 24rpx; padding-bottom: 24rpx; }
.py-4 { padding-top: 32rpx; padding-bottom: 32rpx; }
.py-6 { padding-top: 48rpx; padding-bottom: 48rpx; }
.py-8 { padding-top: 64rpx; padding-bottom: 64rpx; }

.m-0 { margin: 0; }
.m-1 { margin: 8rpx; }
.m-2 { margin: 16rpx; }
.m-3 { margin: 24rpx; }
.m-4 { margin: 32rpx; }
.m-6 { margin: 48rpx; }
.m-8 { margin: 64rpx; }

.mx-auto { margin-left: auto; margin-right: auto; }
.mb-2 { margin-bottom: 16rpx; }
.mb-4 { margin-bottom: 32rpx; }
.mb-6 { margin-bottom: 48rpx; }
.mb-8 { margin-bottom: 64rpx; }
.mt-2 { margin-top: 16rpx; }
.mt-4 { margin-top: 32rpx; }
.mt-6 { margin-top: 48rpx; }
.mt-8 { margin-top: 64rpx; }

/* ===== 布局系统 ===== */
.container {
  width: 100%;
  max-width: 750rpx;
  margin: 0 auto;
  padding: 0 32rpx;
}

.flex { display: flex; }
.flex-col { flex-direction: column; }
.flex-row { flex-direction: row; }
.flex-wrap { flex-wrap: wrap; }
.flex-nowrap { flex-wrap: nowrap; }

.items-start { align-items: flex-start; }
.items-center { align-items: center; }
.items-end { align-items: flex-end; }
.items-stretch { align-items: stretch; }

.justify-start { justify-content: flex-start; }
.justify-center { justify-content: center; }
.justify-end { justify-content: flex-end; }
.justify-between { justify-content: space-between; }
.justify-around { justify-content: space-around; }

.flex-1 { flex: 1; }
.flex-shrink-0 { flex-shrink: 0; }

.grid {
  display: grid;
  gap: 32rpx;
}

.grid-cols-2 { grid-template-columns: repeat(2, 1fr); }
.grid-cols-3 { grid-template-columns: repeat(3, 1fr); }
.grid-cols-4 { grid-template-columns: repeat(4, 1fr); }

.gap-2 { gap: 16rpx; }
.gap-3 { gap: 24rpx; }
.gap-4 { gap: 32rpx; }
.gap-6 { gap: 48rpx; }

/* ===== 定位系统 ===== */
.relative { position: relative; }
.absolute { position: absolute; }
.fixed { position: fixed; }
.sticky { position: sticky; }

.top-0 { top: 0; }
.right-0 { right: 0; }
.bottom-0 { bottom: 0; }
.left-0 { left: 0; }

.z-10 { z-index: 10; }
.z-20 { z-index: 20; }
.z-30 { z-index: 30; }
.z-40 { z-index: 40; }
.z-50 { z-index: 50; }

/* ===== 圆角和边框 ===== */
.rounded-none { border-radius: 0; }
.rounded-sm { border-radius: 8rpx; }
.rounded { border-radius: 16rpx; }
.rounded-lg { border-radius: 24rpx; }
.rounded-xl { border-radius: 32rpx; }
.rounded-2xl { border-radius: 48rpx; }
.rounded-full { border-radius: 50%; }

.border { border: 2rpx solid rgba(11, 15, 25, 0.08); }
.border-t { border-top: 2rpx solid rgba(11, 15, 25, 0.08); }
.border-b { border-bottom: 2rpx solid rgba(11, 15, 25, 0.08); }
.border-l { border-left: 2rpx solid rgba(11, 15, 25, 0.08); }
.border-r { border-right: 2rpx solid rgba(11, 15, 25, 0.08); }

/* ===== 阴影系统 ===== */
.shadow-none { box-shadow: none; }
.shadow-sm { box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05); }
.shadow { box-shadow: 0 8rpx 16rpx rgba(0, 0, 0, 0.1); }
.shadow-lg { box-shadow: 0 16rpx 32rpx rgba(0, 0, 0, 0.15); }
.shadow-xl { box-shadow: 0 24rpx 48rpx rgba(0, 0, 0, 0.2); }

/* ===== 按钮组件 ===== */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 24rpx 48rpx;
  border-radius: 24rpx;
  font-size: 32rpx;
  font-weight: 500;
  text-align: center;
  transition: all 0.2s ease;
  border: none;
  cursor: pointer;
}

.btn-primary {
  background-color: #1f2937;
  color: #ffffff;
}

.btn-primary:hover {
  background-color: #374151;
  transform: translateY(-2rpx);
  box-shadow: 0 16rpx 32rpx rgba(31, 41, 55, 0.3);
}

.btn-accent {
  background-color: #10a37f;
  color: #ffffff;
}

.btn-accent:hover {
  background-color: #059669;
  transform: translateY(-2rpx);
}

.btn-secondary {
  background-color: #ffffff;
  color: #1f2937;
  border: 2rpx solid #1f2937;
}

.btn-secondary:hover {
  background-color: #1f2937;
  color: #ffffff;
}

.btn-ghost {
  background-color: transparent;
  color: #6b7280;
}

.btn-ghost:hover {
  background-color: #f3f4f6;
  color: #1f2937;
}

.btn-sm {
  padding: 16rpx 32rpx;
  font-size: 28rpx;
}

.btn-lg {
  padding: 32rpx 64rpx;
  font-size: 36rpx;
}

.btn-disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.btn-disabled:hover {
  transform: none;
  box-shadow: none;
}

/* ===== 卡片组件 ===== */
.card {
  background-color: #ffffff;
  border-radius: 24rpx;
  padding: 32rpx;
  box-shadow: 0 8rpx 16rpx rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.card:hover {
  transform: translateY(-4rpx);
  box-shadow: 0 16rpx 32rpx rgba(0, 0, 0, 0.15);
}

.card-compact {
  padding: 24rpx;
}

.card-bordered {
  border: 2rpx solid rgba(11, 15, 25, 0.08);
  box-shadow: none;
}

/* ===== 表单组件 ===== */
.input {
  width: 100%;
  padding: 24rpx 32rpx;
  border: 2rpx solid rgba(11, 15, 25, 0.08);
  border-radius: 16rpx;
  font-size: 32rpx;
  background-color: #ffffff;
  transition: all 0.2s ease;
}

.input:focus {
  border-color: #10a37f;
  box-shadow: 0 0 0 6rpx rgba(16, 163, 127, 0.1);
  outline: none;
}

.input-error {
  border-color: #ef4444;
}

.input-error:focus {
  border-color: #ef4444;
  box-shadow: 0 0 0 6rpx rgba(239, 68, 68, 0.1);
}

/* ===== 导航组件 ===== */
.navbar {
  background-color: #ffffff;
  padding: 32rpx;
  border-bottom: 2rpx solid rgba(11, 15, 25, 0.08);
}

.navbar-title {
  font-size: 48rpx;
  font-weight: 700;
  color: #1f2937;
}

.navbar-subtitle {
  font-size: 28rpx;
  color: #6b7280;
  margin-top: 8rpx;
}

/* ===== 标签组件 ===== */
.tag {
  display: inline-flex;
  align-items: center;
  padding: 8rpx 16rpx;
  border-radius: 16rpx;
  font-size: 24rpx;
  font-weight: 500;
}

.tag-primary {
  background-color: rgba(31, 41, 55, 0.1);
  color: #1f2937;
}

.tag-accent {
  background-color: rgba(16, 163, 127, 0.1);
  color: #10a37f;
}

.tag-success {
  background-color: rgba(16, 185, 129, 0.1);
  color: #10b981;
}

.tag-warning {
  background-color: rgba(245, 158, 11, 0.1);
  color: #f59e0b;
}

.tag-danger {
  background-color: rgba(239, 68, 68, 0.1);
  color: #ef4444;
}

/* ===== 分隔线 ===== */
.divider {
  height: 2rpx;
  background-color: rgba(11, 15, 25, 0.08);
  margin: 32rpx 0;
}

.divider-vertical {
  width: 2rpx;
  height: 32rpx;
  background-color: rgba(11, 15, 25, 0.08);
  margin: 0 16rpx;
}

/* ===== 工具类 ===== */
.w-full { width: 100%; }
.h-full { height: 100%; }
.w-auto { width: auto; }
.h-auto { height: auto; }

.min-h-screen { min-height: 100vh; }
.min-h-full { min-height: 100%; }

.overflow-hidden { overflow: hidden; }
.overflow-scroll { overflow: scroll; }
.overflow-auto { overflow: auto; }

.pointer-events-none { pointer-events: none; }
.pointer-events-auto { pointer-events: auto; }

.select-none { user-select: none; }
.select-text { user-select: text; }

.sr-only {
  position: absolute;
  width: 2rpx;
  height: 2rpx;
  padding: 0;
  margin: -2rpx;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* ===== 动画工具 ===== */
.transition {
  transition: all 0.2s ease;
}

.transition-transform {
  transition: transform 0.2s ease;
}

.transition-opacity {
  transition: opacity 0.2s ease;
}

.duration-200 { transition-duration: 0.2s; }
.duration-300 { transition-duration: 0.3s; }
.duration-500 { transition-duration: 0.5s; }

.ease-in { transition-timing-function: ease-in; }
.ease-out { transition-timing-function: ease-out; }
.ease-in-out { transition-timing-function: ease-in-out; }

/* ===== 响应式隐藏 ===== */
.hidden { display: none !important; }
.visible { visibility: visible; }
.invisible { visibility: hidden; }

/* ===== 特殊效果 ===== */
.backdrop-blur {
  backdrop-filter: blur(20rpx);
}

.gradient-bg {
  background: linear-gradient(135deg, #ffffff 0%, #f9fafb 100%);
}

.gradient-accent {
  background: linear-gradient(135deg, #10a37f 0%, #059669 100%);
}

/* ===== 滚动条样式 (微信小程序不支持，保留注释) ===== */
/*
::-webkit-scrollbar {
  width: 8rpx;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4rpx;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4rpx;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
*/
