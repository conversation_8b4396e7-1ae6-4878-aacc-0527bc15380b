/* pages/drone-control/drone-control.wxss */

/* 强制横屏显示 */
page {
  background: #000000;
  overflow: hidden;
}

.drone-control-container {
  width: 100vw;
  height: 100vh;
  background: #000000;
  position: relative;
  overflow: hidden;
}

/* 视频流区域 */
.video-stream {
  width: 100%;
  height: 100%;
  position: relative;
  background: linear-gradient(45deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
}

.video-background {
  width: 100%;
  height: 100%;
  opacity: 0.8;
}

.floating-clouds {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 20"><circle cx="20" cy="10" r="3" fill="rgba(255,255,255,0.1)"/><circle cx="40" cy="15" r="2" fill="rgba(255,255,255,0.08)"/><circle cx="60" cy="8" r="4" fill="rgba(255,255,255,0.06)"/><circle cx="80" cy="12" r="2.5" fill="rgba(255,255,255,0.09)"/></svg>') repeat-x;
  animation: float 20s linear infinite;
}

@keyframes float {
  0% { transform: translateX(0); }
  100% { transform: translateX(-200rpx); }
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.7; }
}

/* HUD 覆盖层 */
.hud-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
  z-index: 10;
}

/* 十字准心 */
.crosshair {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 80rpx;
  height: 80rpx;
  border: 4rpx solid rgba(255, 255, 255, 0.8);
  border-radius: 50%;
}

.crosshair::before,
.crosshair::after {
  content: '';
  position: absolute;
  background: rgba(255, 255, 255, 0.8);
}

.crosshair::before {
  top: 50%;
  left: -20rpx;
  right: -20rpx;
  height: 2rpx;
  transform: translateY(-50%);
}

.crosshair::after {
  left: 50%;
  top: -20rpx;
  bottom: -20rpx;
  width: 2rpx;
  transform: translateX(-50%);
}

/* 顶部状态栏 */
.top-status {
  position: absolute;
  top: 12rpx;
  left: 16rpx;
  right: 16rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  pointer-events: auto;
}

.status-left,
.status-right {
  display: flex;
  align-items: center;
  gap: 10rpx;
}

.exit-btn,
.signal-status {
  background: rgba(0, 0, 0, 0.75);
  padding: 6rpx 10rpx;
  border-radius: 16rpx;
  display: flex;
  align-items: center;
  gap: 6rpx;
  color: #ffffff;
  font-size: 16rpx;
  height: 32rpx;
  box-sizing: border-box;
  backdrop-filter: blur(8rpx);
}

.flight-time-display {
  background: rgba(0, 0, 0, 0.8);
  padding: 6rpx 12rpx;
  border-radius: 18rpx;
  display: flex;
  align-items: center;
  gap: 6rpx;
  color: #ffffff;
  font-size: 16rpx;
  height: 32rpx;
  box-sizing: border-box;
  backdrop-filter: blur(10rpx);
  border: 1rpx solid rgba(255, 255, 255, 0.15);
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.3);
}

.time-icon {
  font-size: 14rpx;
  opacity: 0.9;
}

.flight-time-display .time-text {
  font-family: 'Courier New', monospace;
  font-weight: 600;
  font-size: 16rpx;
  letter-spacing: 0.5rpx;
  color: #ffffff;
}

.battery-display {
  background: rgba(0, 0, 0, 0.8);
  padding: 6rpx 12rpx;
  border-radius: 18rpx;
  display: flex;
  align-items: center;
  gap: 6rpx;
  color: #ffffff;
  font-size: 16rpx;
  height: 32rpx;
  box-sizing: border-box;
  backdrop-filter: blur(10rpx);
  border: 1rpx solid rgba(255, 255, 255, 0.15);
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.3);
}

.battery-icon {
  font-size: 14rpx;
  opacity: 0.9;
}

.battery-text {
  font-family: 'Courier New', monospace;
  font-weight: 600;
  font-size: 16rpx;
  letter-spacing: 0.5rpx;
}

.battery-bar {
  width: 40rpx;
  height: 10rpx;
  background: #374151;
  border-radius: 5rpx;
  overflow: hidden;
  border: 1rpx solid rgba(255, 255, 255, 0.2);
}

.battery-fill {
  height: 100%;
  background: linear-gradient(90deg, #10b981 0%, #10b981 70%, #f59e0b 70%, #f59e0b 85%, #ef4444 85%);
  border-radius: 4rpx;
  transition: width 0.3s ease;
}

/* 飞行参数显示 */
.flight-params {
  position: absolute;
  top: 56rpx;
  left: 16rpx;
  background: rgba(0, 0, 0, 0.75);
  border-radius: 12rpx;
  padding: 8rpx 12rpx;
  pointer-events: auto;
  backdrop-filter: blur(8rpx);
  border: 1rpx solid rgba(255, 255, 255, 0.1);
}

.param-item {
  display: flex;
  justify-content: space-between;
  font-size: 14rpx;
  color: #ffffff;
  min-width: 120rpx;
  line-height: 1.4;
  margin-bottom: 6rpx;
}

.param-item:last-child {
  margin-bottom: 0;
}

.param-label {
  color: #d1d5db;
  font-weight: 400;
}

.param-value {
  font-family: monospace;
  color: #ffffff;
  font-weight: 600;
}

/* 相机设置 */
.camera-settings {
  position: absolute;
  top: 50rpx;
  right: 16rpx;
  background: rgba(0, 0, 0, 0.6);
  border-radius: 8rpx;
  padding: 6rpx 8rpx;
  pointer-events: auto;
  height: 80rpx;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.setting-item {
  font-size: 12rpx;
  color: #ffffff;
  line-height: 1.2;
}

.setting-item:last-child {
  margin-bottom: 0;
}

.setting-text {
  font-family: monospace;
  color: #ffffff;
}

/* 录制状态指示 */
.recording-indicator {
  position: absolute;
  top: 20rpx;
  left: 50%;
  transform: translateX(-50%);
  background: #ef4444;
  color: #ffffff;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  display: flex;
  align-items: center;
  gap: 8rpx;
  font-size: 20rpx;
  pointer-events: auto;
}

.recording-indicator.active {
  animation: pulse 1s infinite;
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

.recording-dot {
  font-size: 24rpx;
  animation: blink 1s infinite;
}

@keyframes blink {
  0%, 50% { opacity: 1; }
  51%, 100% { opacity: 0; }
}

/* 左侧操作杆 */
.left-joystick-container {
  position: absolute;
  bottom: 20rpx;
  left: 40rpx;
  pointer-events: auto;
}

/* 右侧操作杆 */
.right-joystick-container {
  position: absolute;
  bottom: 20rpx;
  right: 40rpx;
  pointer-events: auto;
}

.control-joystick {
  width: 80rpx;
  height: 80rpx;
  border: 2rpx solid rgba(255, 255, 255, 0.4);
  border-radius: 50%;
  position: relative;
  background: rgba(0, 0, 0, 0.3);
}

.joystick-handle {
  width: 32rpx;
  height: 32rpx;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 50%;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  transition: all 0.1s ease;
}

/* 底部控制面板 */
.bottom-control-panel {
  position: absolute;
  bottom: 5rpx;
  left: 0;
  right: 0;
  background: transparent;
  padding: 8rpx 16rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  pointer-events: auto;
  height: 80rpx;
  gap: 24rpx;
  box-sizing: border-box;
}

/* 统一控制按钮样式 */
.control-btn {
  width: 60rpx !important;
  height: 60rpx !important;
  min-width: 60rpx !important;
  min-height: 60rpx !important;
  max-width: 60rpx !important;
  max-height: 60rpx !important;
  border-radius: 50% !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  border: 2rpx solid rgba(255, 255, 255, 0.3) !important;
  transition: all 0.3s ease !important;
  flex-shrink: 0 !important;
  box-sizing: border-box !important;
  padding: 0 !important;
  margin: 0 !important;
}

.control-btn:active {
  transform: scale(0.9) !important;
}

.control-icon {
  font-size: 28rpx !important;
  line-height: 1 !important;
  width: 28rpx !important;
  height: 28rpx !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
}

.photo-btn {
  background: rgba(255, 255, 255, 0.9) !important;
}

.photo-btn .control-icon {
  color: #1f2937 !important;
}

.record-btn {
  background: rgba(239, 68, 68, 0.9) !important;
}

.record-btn .control-icon {
  color: #ffffff !important;
}

.record-btn.recording {
  animation: pulse 1s infinite !important;
}

.return-btn {
  background: rgba(34, 197, 94, 0.9) !important;
}

.return-btn .control-icon {
  color: #ffffff !important;
}

.settings-btn {
  background: rgba(59, 130, 246, 0.9) !important;
}

.settings-btn .control-icon {
  color: #ffffff !important;
}

/* 侧边控制栏 */
.side-controls {
  position: absolute;
  right: 16rpx;
  top: 50rpx;
  display: flex;
  flex-direction: column;
  gap: 8rpx;
  pointer-events: auto;
}

.side-btn {
  width: 32rpx;
  height: 32rpx;
  background: rgba(0, 0, 0, 0.6);
  color: #ffffff;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14rpx;
  transition: all 0.3s ease;
  border: 1rpx solid rgba(255, 255, 255, 0.2);
}

.side-btn:active {
  background: rgba(255, 255, 255, 0.2);
  transform: scale(0.9);
}

/* 设置弹窗 */
.settings-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.settings-content {
  background: #1f2937;
  border-radius: 24rpx;
  padding: 48rpx;
  max-width: 600rpx;
  width: 90%;
  color: #ffffff;
}

.settings-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 32rpx;
}

.settings-title {
  font-size: 36rpx;
  font-weight: 600;
}

.settings-close {
  font-size: 32rpx;
  color: #9ca3af;
}

.settings-list {
  margin-bottom: 32rpx;
}

.setting-item {
  display: flex;
  justify-content: space-between;
  padding: 24rpx 0;
  border-bottom: 1rpx solid #374151;
}

.setting-item:last-child {
  border-bottom: none;
}

.setting-label {
  font-size: 28rpx;
  color: #d1d5db;
}

.setting-value {
  font-size: 28rpx;
  color: #ffffff;
  font-family: monospace;
}

.settings-actions {
  text-align: center;
}

.settings-confirm-btn {
  background: #10b981;
  color: #ffffff;
  padding: 24rpx 48rpx;
  border-radius: 16rpx;
  font-size: 32rpx;
  border: none;
}

.status-text {
  font-size: 28rpx;
  font-weight: 500;
}

.connection-status.connected .status-text {
  color: #10b981;
}

.connection-status.disconnected .status-text {
  color: #ef4444;
}

.flight-mode {
  background: rgba(16, 163, 127, 0.2);
  border: 2rpx solid #10a37f;
  border-radius: 16rpx;
  padding: 8rpx 16rpx;
}

.mode-text {
  font-size: 24rpx;
  color: #10a37f;
  font-weight: 500;
}

.settings-btn {
  width: 64rpx;
  height: 64rpx;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
}

.settings-btn:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: scale(1.1);
}

.settings-icon {
  font-size: 32rpx;
}

/* 视频流区域 */
.video-container {
  position: relative;
  flex: 1;
  background: #1a1a1a;
  display: flex;
  align-items: center;
  justify-content: center;
}

.video-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16rpx;
  opacity: 0.6;
}

.video-icon {
  font-size: 120rpx;
}

.video-text {
  font-size: 36rpx;
  font-weight: 600;
}

.video-desc {
  font-size: 28rpx;
  color: #9ca3af;
}

/* HUD覆盖层 */
.hud-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
}

.hud-data {
  position: absolute;
  top: 32rpx;
  left: 32rpx;
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.data-item {
  background: rgba(0, 0, 0, 0.6);
  border-radius: 12rpx;
  padding: 8rpx 12rpx;
  backdrop-filter: blur(10rpx);
}

.data-label {
  display: block;
  font-size: 16rpx;
  color: #9ca3af;
  margin-bottom: 2rpx;
}

.data-value {
  font-size: 20rpx;
  font-weight: 600;
  color: #ffffff;
}

.hud-status {
  position: absolute;
  top: 24rpx;
  right: 24rpx;
  display: flex;
  flex-direction: column;
  gap: 12rpx;
}

.battery-indicator,
.signal-indicator,
.gps-indicator {
  background: rgba(0, 0, 0, 0.6);
  border-radius: 8rpx;
  padding: 8rpx 12rpx;
  display: flex;
  align-items: center;
  gap: 6rpx;
  backdrop-filter: blur(10rpx);
}

.battery-icon,
.signal-icon,
.gps-icon {
  font-size: 18rpx;
}

.battery-level,
.signal-level,
.gps-count {
  font-size: 18rpx;
  font-weight: 500;
}

.flight-time {
  position: absolute;
  bottom: 32rpx;
  left: 50%;
  transform: translateX(-50%);
  background: rgba(0, 0, 0, 0.6);
  border-radius: 16rpx;
  padding: 16rpx 32rpx;
  backdrop-filter: blur(10rpx);
}



.recording-indicator {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: rgba(239, 68, 68, 0.9);
  border-radius: 24rpx;
  padding: 16rpx 32rpx;
  display: flex;
  align-items: center;
  gap: 12rpx;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.7; }
}

.recording-icon {
  font-size: 24rpx;
}

.recording-text {
  font-size: 28rpx;
  font-weight: 600;
}

/* 控制面板 */
.control-panel {
  background: rgba(0, 0, 0, 0.9);
  padding: 32rpx;
  backdrop-filter: blur(20rpx);
  border-top: 2rpx solid rgba(255, 255, 255, 0.1);
}

.main-controls {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 24rpx;
  margin-bottom: 32rpx;
}

.control-btn {
  height: 120rpx;
  background: rgba(255, 255, 255, 0.1);
  border: 2rpx solid rgba(255, 255, 255, 0.2);
  border-radius: 16rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 8rpx;
  color: #ffffff;
  font-size: 24rpx;
  transition: all 0.2s ease;
}

.control-btn:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: translateY(-4rpx);
}

.control-btn[disabled] {
  opacity: 0.3;
  background: rgba(255, 255, 255, 0.05);
  border-color: rgba(255, 255, 255, 0.1);
}

.control-btn[disabled]:hover {
  transform: none;
}

.takeoff-btn {
  background: rgba(16, 185, 129, 0.2);
  border-color: #10b981;
  color: #10b981;
}

.takeoff-btn:hover {
  background: rgba(16, 185, 129, 0.3);
}

.land-btn {
  background: rgba(245, 158, 11, 0.2);
  border-color: #f59e0b;
  color: #f59e0b;
}

.land-btn:hover {
  background: rgba(245, 158, 11, 0.3);
}

.return-btn {
  background: rgba(59, 130, 246, 0.2);
  border-color: #3b82f6;
  color: #3b82f6;
}

.return-btn:hover {
  background: rgba(59, 130, 246, 0.3);
}

.emergency-btn {
  background: rgba(239, 68, 68, 0.2);
  border-color: #ef4444;
  color: #ef4444;
}

.emergency-btn:hover {
  background: rgba(239, 68, 68, 0.3);
}

.btn-icon {
  font-size: 36rpx;
}

.btn-text {
  font-size: 24rpx;
  font-weight: 500;
}

/* 拍摄控制 */
.camera-controls {
  display: flex;
  justify-content: center;
  gap: 32rpx;
}

.camera-btn {
  width: 120rpx;
  height: 120rpx;
  background: rgba(255, 255, 255, 0.1);
  border: 2rpx solid rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #ffffff;
  transition: all 0.2s ease;
}

.camera-btn:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: scale(1.1);
}

.camera-btn[disabled] {
  opacity: 0.3;
  background: rgba(255, 255, 255, 0.05);
}

.camera-btn[disabled]:hover {
  transform: none;
}

.photo-btn {
  background: rgba(16, 163, 127, 0.2);
  border-color: #10a37f;
}

.record-btn {
  background: rgba(239, 68, 68, 0.2);
  border-color: #ef4444;
}

.record-btn.recording {
  background: rgba(239, 68, 68, 0.4);
  animation: pulse 2s infinite;
}

.mode-btn {
  background: rgba(107, 114, 128, 0.2);
  border-color: #6b7280;
}

.mode-btn.active {
  background: rgba(16, 163, 127, 0.3);
  border-color: #10a37f;
  color: #10a37f;
}

.camera-icon {
  font-size: 48rpx;
}

/* 设置弹窗 */
.settings-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 64rpx;
}

.settings-content {
  background: #1f2937;
  border-radius: 24rpx;
  width: 100%;
  max-width: 640rpx;
  max-height: 80vh;
  overflow: hidden;
}

.settings-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 24rpx;
  border-bottom: 2rpx solid rgba(255, 255, 255, 0.1);
}

.settings-title {
  font-size: 24rpx;
  font-weight: 600;
  color: #ffffff;
}

.settings-close {
  width: 48rpx;
  height: 48rpx;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20rpx;
  color: #ffffff;
  cursor: pointer;
}

.settings-list {
  padding: 20rpx 24rpx;
  max-height: 60vh;
  overflow-y: auto;
}

.setting-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16rpx 0;
  border-bottom: 1rpx solid rgba(255, 255, 255, 0.1);
}

.setting-item:last-child {
  border-bottom: none;
}

.setting-label {
  font-size: 18rpx;
  color: #e5e7eb;
}

.setting-value {
  font-size: 18rpx;
  color: #10a37f;
  font-weight: 500;
}

.settings-actions {
  padding: 20rpx 24rpx;
  border-top: 2rpx solid rgba(255, 255, 255, 0.1);
}

.settings-btn {
  width: 100%;
  height: 64rpx;
  background: #10a37f;
  color: #ffffff;
  border-radius: 12rpx;
  font-size: 20rpx;
  font-weight: 600;
  border: none;
}

/* 加载遮罩 */
.loading-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.9);
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
}

.loading-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 32rpx;
}

.loading-spinner {
  width: 80rpx;
  height: 80rpx;
  border: 6rpx solid rgba(16, 163, 127, 0.2);
  border-top: 6rpx solid #10a37f;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: 32rpx;
  color: #ffffff;
  text-align: center;
}