<!--pages/location/location.wxml-->
<view class="location-container">
  <!-- 搜索栏 -->
  <view class="search-section">
    <view class="search-bar">
      <text class="search-icon">🔍</text>
      <input 
        class="search-input" 
        placeholder="搜索地点或地址"
        value="{{searchKeyword}}"
        bindinput="onSearchInput"
      />
    </view>
  </view>

  <!-- 当前定位 -->
  <view class="current-location" bindtap="getCurrentLocation">
    <text class="location-icon">📍</text>
    <text class="location-text">{{currentLocation || '获取当前位置'}}</text>
    <text class="location-action">定位</text>
  </view>

  <!-- 地点列表 -->
  <view class="locations-list">
    <view 
      class="location-card" 
      wx:for="{{filteredLocations}}" 
      wx:key="id"
      bindtap="selectLocation"
      data-location="{{item}}"
    >
      <view class="location-info">
        <text class="location-name">{{item.name}}</text>
        <text class="location-address">{{item.address}}</text>
        <view class="location-meta">
          <text class="distance">距离{{item.distance}}km</text>
          <text class="rating">⭐{{item.rating}}</text>
          <text class="price">¥{{item.price}}/{{item.unit}}</text>
        </view>
      </view>
      
      <view class="location-status">
        <view class="status-text available">
          可用
        </view>
      </view>
    </view>
  </view>

  <!-- 空状态 -->
  <view class="empty-state" wx:if="{{filteredLocations.length === 0 && !loading}}">
    <text class="empty-icon">📍</text>
    <text class="empty-title">暂无地点</text>
    <text class="empty-desc">当前区域暂无可用地点</text>
  </view>
</view>