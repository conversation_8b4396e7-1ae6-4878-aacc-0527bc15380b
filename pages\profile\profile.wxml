<!--pages/profile/profile.wxml-->
<view class="profile-container">
  <!-- 用户信息卡片 -->
  <view class="user-info-card">
    <view class="user-content">
      <view class="user-avatar">
        <text class="avatar-icon">👤</text>
      </view>
      <view class="user-details">
        <text class="user-name">{{userInfo.nickname || '张先生'}}</text>
        <text class="user-id">用户ID: {{userInfo.userId || '1001'}}</text>
        <view class="user-badges">
          <view class="level-badge">
            <text class="crown-icon">👑</text>
            <text class="level-text">{{userInfo.level || '白银会员'}}</text>
          </view>
          <text class="flight-time">飞行时长: {{userInfo.flightTime || '12.5'}}小时</text>
        </view>
      </view>
      <view class="edit-icon-container">
        <text class="edit-icon" bindtap="editProfile">✏️</text>
      </view>
    </view>
  </view>

  <!-- 余额卡片 -->
  <view class="balance-card">
    <view class="balance-content">
      <view class="balance-info">
        <text class="balance-title">账户余额</text>
        <text class="balance-amount">¥{{userInfo.balance || '150.00'}}</text>
        <text class="balance-desc">可租赁时长: 约{{userInfo.availableHours || '2'}}小时</text>
      </view>
      <view class="wallet-icon">
        <text class="wallet-emoji">💼</text>
      </view>
    </view>
    <view class="balance-actions">
      <button class="balance-btn primary" bindtap="recharge">
        <text class="btn-icon">+</text>
        <text class="btn-text">充值</text>
      </button>
      <button class="balance-btn secondary" bindtap="viewTransactions">
        <text class="btn-icon">📋</text>
        <text class="btn-text">明细</text>
      </button>
    </view>
  </view>

  <!-- 统计信息 -->
  <view class="stats-section">
    <view class="stats-grid">
      <view class="stats-item">
        <text class="stats-number">{{userStats.totalOrders || '23'}}</text>
        <text class="stats-label">总订单</text>
      </view>
      <view class="stats-item">
        <text class="stats-number">{{userStats.worksCount || '156'}}</text>
        <text class="stats-label">拍摄作品</text>
      </view>
      <view class="stats-item">
        <text class="stats-number">{{userStats.avgRating || '4.9'}}</text>
        <text class="stats-label">平均评分</text>
      </view>
      <view class="stats-item">
        <text class="stats-number">{{userStats.favoriteCount || '8'}}</text>
        <text class="stats-label">收藏地点</text>
      </view>
    </view>
  </view>

  <!-- 我的服务 -->
  <view class="service-section">
    <view class="section-header">
      <text class="section-title">我的服务</text>
    </view>
    <view class="menu-list">
      <view class="menu-item" bindtap="goToGallery">
        <view class="menu-left">
          <view class="menu-icon-wrapper blue">
            <text class="menu-icon">📷</text>
          </view>
          <text class="menu-text">我的作品集</text>
        </view>
        <view class="menu-right">
          <text class="menu-desc">{{userStats.worksCount || '156'}}张作品</text>
          <text class="menu-arrow">›</text>
        </view>
      </view>

      <view class="menu-item" bindtap="goToFavorites">
        <view class="menu-left">
          <view class="menu-icon-wrapper red">
            <text class="menu-icon">❤️</text>
          </view>
          <text class="menu-text">收藏夹</text>
        </view>
        <view class="menu-right">
          <text class="menu-desc">{{userStats.favoriteCount || '8'}}个地点</text>
          <text class="menu-arrow">›</text>
        </view>
      </view>

      <view class="menu-item" bindtap="goToCoupons">
        <view class="menu-left">
          <view class="menu-icon-wrapper yellow">
            <text class="menu-icon">🎫</text>
          </view>
          <text class="menu-text">优惠券</text>
        </view>
        <view class="menu-right">
          <text class="menu-desc">{{couponCount || '3'}}张可用</text>
          <text class="menu-arrow">›</text>
        </view>
      </view>

      <view class="menu-item" bindtap="goToMembership">
        <view class="menu-left">
          <view class="menu-icon-wrapper purple">
            <text class="menu-icon">👑</text>
          </view>
          <text class="menu-text">会员特权</text>
        </view>
        <view class="menu-right">
          <text class="menu-desc">{{userInfo.level || '白银会员'}}</text>
          <text class="menu-arrow">›</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 设置 -->
  <view class="settings-section">
    <view class="section-header">
      <text class="section-title">设置</text>
    </view>
    <view class="menu-list">
      <view class="menu-item" bindtap="goToAccountSettings">
        <view class="menu-left">
          <view class="menu-icon-wrapper gray">
            <text class="menu-icon">⚙️</text>
          </view>
          <text class="menu-text">账户设置</text>
        </view>
        <text class="menu-arrow">›</text>
      </view>

      <view class="menu-item" bindtap="goToNotifications">
        <view class="menu-left">
          <view class="menu-icon-wrapper green">
            <text class="menu-icon">🔔</text>
          </view>
          <text class="menu-text">消息通知</text>
        </view>
        <text class="menu-arrow">›</text>
      </view>

      <view class="menu-item" bindtap="goToPrivacy">
        <view class="menu-left">
          <view class="menu-icon-wrapper indigo">
            <text class="menu-icon">🛡️</text>
          </view>
          <text class="menu-text">隐私安全</text>
        </view>
        <text class="menu-arrow">›</text>
      </view>

      <view class="menu-item" bindtap="goToHelp">
        <view class="menu-left">
          <view class="menu-icon-wrapper orange">
            <text class="menu-icon">❓</text>
          </view>
          <text class="menu-text">帮助中心</text>
        </view>
        <text class="menu-arrow">›</text>
      </view>

      <view class="menu-item" bindtap="goToAbout">
        <view class="menu-left">
          <view class="menu-icon-wrapper teal">
            <text class="menu-icon">ℹ️</text>
          </view>
          <text class="menu-text">关于逍遥境</text>
        </view>
        <view class="menu-right">
          <text class="menu-desc">v1.0.0</text>
          <text class="menu-arrow">›</text>
        </view>
      </view>
    </view>

    <!-- 退出登录按钮 -->
    <view class="logout-section">
      <button class="logout-btn" bindtap="handleLogout">
        <text class="logout-text">退出登录</text>
      </button>
    </view>
  </view>
</view>