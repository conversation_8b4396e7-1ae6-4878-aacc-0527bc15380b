/**
 * 逍遥境小程序网络请求工具模块
 * 提供统一的网络请求封装、拦截器、错误处理等功能
 */

const app = getApp()

// API配置
const API_CONFIG = {
  baseUrl: 'http://volcanoes.cc:46783',
  timeout: 10000,
  enableMock: false, // 使用真实后端API
  retryCount: 2, // 重试次数
  retryDelay: 1000 // 重试延迟(ms)
}

/**
 * 统一网络请求方法
 * @param {object} options 请求配置
 * @returns {Promise} 请求Promise
 */
function request(options) {
  return new Promise((resolve, reject) => {
    // 参数验证
    if (!options || !options.url) {
      reject(new Error('请求URL不能为空'))
      return
    }
    
    // 合并配置
    const config = {
      method: 'GET',
      timeout: API_CONFIG.timeout,
      enableMock: API_CONFIG.enableMock,
      showLoading: false,
      showError: true,
      retryCount: 0,
      ...options,
      url: buildUrl(options.url),
      header: buildHeaders(options.header)
    }
    
    console.log('发起网络请求：', config.method, config.url)
    
    // 显示加载提示
    if (config.showLoading) {
      wx.showLoading({
        title: config.loadingText || '请求中...',
        mask: true
      })
    }
    
    // 模拟数据模式
    if (config.enableMock && shouldUseMock(config.url)) {
      handleMockResponse(config, resolve, reject)
      return
    }
    
    // 发起真实请求
    performRequest(config, resolve, reject)
  })
}

/**
 * 构建完整URL
 * @param {string} url 请求路径
 * @returns {string} 完整URL
 */
function buildUrl(url) {
  if (url.startsWith('http://') || url.startsWith('https://')) {
    return url
  }
  
  return `${API_CONFIG.baseUrl}${url.startsWith('/') ? url : '/' + url}`
}

/**
 * 构建请求头
 * @param {object} customHeaders 自定义请求头
 * @returns {object} 完整请求头
 */
function buildHeaders(customHeaders = {}) {
  const baseHeaders = {
    'Content-Type': 'application/json',
    'X-Requested-With': 'XMLHttpRequest'
  }
  
  // 添加认证token
  try {
    const token = wx.getStorageSync('token')
    if (token) {
      baseHeaders.Authorization = `Bearer ${token}`
    }
  } catch (error) {
    console.warn('获取token失败：', error)
  }
  
  // 添加设备信息
  if (app && app.globalData && app.globalData.systemInfo) {
    const { system, platform, version } = app.globalData.systemInfo
    baseHeaders['X-Device-Info'] = `${platform} ${system} ${version}`
  }
  
  return { ...baseHeaders, ...customHeaders }
}

/**
 * 执行网络请求
 * @param {object} config 请求配置
 * @param {function} resolve Promise resolve
 * @param {function} reject Promise reject
 */
function performRequest(config, resolve, reject) {
  const requestTask = wx.request({
    ...config,
    success: (response) => {
      console.log('请求成功：', config.url, response.statusCode)
      handleResponse(config, response, resolve, reject)
    },
    fail: (error) => {
      console.error('请求失败：', config.url, error)
      handleRequestError(config, error, resolve, reject)
    },
    complete: () => {
      if (config.showLoading) {
        wx.hideLoading()
      }
    }
  })
  
  // 设置超时处理
  setTimeout(() => {
    if (requestTask) {
      requestTask.abort()
      handleRequestError(config, { errMsg: 'request:fail timeout' }, resolve, reject)
    }
  }, config.timeout)
}

/**
 * 处理响应数据
 * @param {object} config 请求配置
 * @param {object} response 响应数据
 * @param {function} resolve Promise resolve
 * @param {function} reject Promise reject
 */
function handleResponse(config, response, resolve, reject) {
  const { statusCode, data } = response
  
  // HTTP状态码检查
  if (statusCode !== 200) {
    const error = new Error(`HTTP ${statusCode}`)
    error.statusCode = statusCode
    error.response = response
    handleError(config, error, reject)
    return
  }
  
  // 业务逻辑检查
  if (data && typeof data === 'object') {
    console.log('响应数据详情:', data)

    // 后端API响应格式：{ code: number, message: string, data: any }
    // code: 0 或 200 表示成功，其他表示失败
    if (data.code !== undefined && data.code !== 0 && data.code !== 200) {
      const error = new Error(data.message || '请求失败')
      error.code = data.code
      error.data = data

      // 特殊错误码处理
      if (data.code === 401 || data.code === 403) {
        handleAuthError(error)
      }

      handleError(config, error, reject)
      return
    }

    // 返回实际数据
    resolve(data.data || data)
  } else {
    resolve(data)
  }
}

/**
 * 处理请求错误
 * @param {object} config 请求配置
 * @param {object} error 错误信息
 * @param {function} resolve Promise resolve
 * @param {function} reject Promise reject
 */
function handleRequestError(config, error, resolve, reject) {
  let errorMessage = '网络请求失败'

  if (error.errMsg) {
    if (error.errMsg.includes('timeout')) {
      errorMessage = '请求超时，请检查网络连接'
    } else if (error.errMsg.includes('fail')) {
      errorMessage = '网络连接失败，请检查网络设置'
    }
  }

  const requestError = new Error(errorMessage)
  requestError.originalError = error

  // 对于登录接口，不进行重试（因为微信code只能使用一次）
  if (config.url.includes('/auth/wechat/login') || config.url.includes('/login')) {
    console.log('登录接口请求失败，不进行重试：', config.url)
    handleError(config, requestError, reject)
    return
  }

  // 重试机制
  if (config.retryCount < API_CONFIG.retryCount) {
    console.log(`请求失败，${API_CONFIG.retryDelay}ms后重试：`, config.url)
    setTimeout(() => {
      const retryConfig = { ...config, retryCount: config.retryCount + 1 }
      performRequest(retryConfig, resolve, reject)
    }, API_CONFIG.retryDelay)
    return
  }

  handleError(config, requestError, reject)
}

/**
 * 统一错误处理
 * @param {object} config 请求配置
 * @param {Error} error 错误对象
 * @param {function} reject Promise reject
 */
function handleError(config, error, reject) {
  console.error('请求处理错误：', config.url, error)
  
  // 显示错误提示
  if (config.showError) {
    wx.showToast({
      title: error.message || '请求失败',
      icon: 'error',
      duration: 2000
    })
  }
  
  reject(error)
}

/**
 * 处理认证错误
 * @param {Error} error 错误对象
 */
function handleAuthError(error) {
  console.log('认证失败，清除登录状态：', error.message)
  
  // 清除登录状态
  try {
    wx.removeStorageSync('isLoggedIn')
    wx.removeStorageSync('userInfo')
    wx.removeStorageSync('token')
    wx.removeStorageSync('loginTime')
    
    if (app && app.globalData) {
      app.globalData.isLoggedIn = false
      app.globalData.userInfo = null
    }
  } catch (e) {
    console.error('清除登录状态失败：', e)
  }
  
  // 显示提示并跳转到登录页
  wx.showModal({
    title: '登录已过期',
    content: '请重新登录',
    showCancel: false,
    success: () => {
      wx.reLaunch({
        url: '/pages/login/login'
      })
    }
  })
}

/**
 * 判断是否使用模拟数据
 * @param {string} url 请求URL
 * @returns {boolean}
 */
function shouldUseMock(url) {
  // 开发环境直接使用模拟数据，不发送真实请求
  return API_CONFIG.enableMock
}

/**
 * 处理模拟响应
 * @param {object} config 请求配置
 * @param {function} resolve Promise resolve
 * @param {function} reject Promise reject
 */
function handleMockResponse(config, resolve, reject) {
  console.log('使用模拟数据：', config.url)
  
  // 模拟网络延迟
  setTimeout(() => {
    try {
      const mockData = getMockData(config.url, config.method, config.data)
      
      if (config.showLoading) {
        wx.hideLoading()
      }
      
      resolve(mockData)
    } catch (error) {
      if (config.showLoading) {
        wx.hideLoading()
      }
      reject(error)
    }
  }, 300 + Math.random() * 500) // 300-800ms随机延迟
}

/**
 * 获取模拟数据
 * @param {string} url 请求URL
 * @param {string} method 请求方法
 * @param {object} data 请求数据
 * @returns {any} 模拟数据
 */
function getMockData(url, method, data) {
  console.log('获取模拟数据：', url, method)

  // 根据URL返回对应的模拟数据
  if (url.includes('/auth/login') || url.includes('/login')) {
    return {
      success: true,
      data: {
        id: 'test_user_001',
        nickname: '逍遥测试用户',
        avatar: '',
        phone: '138****8888',
        balance: 299.50,
        level: 'VIP',
        creditScore: 850,
        token: 'mock_token_' + Date.now(),
        registerTime: '2024-01-15'
      }
    }
  }

  // 用户信息相关
  if (url.includes('/user/profile') || url.includes('/profile')) {
    return {
      success: true,
      data: {
        id: 'test_user_001',
        nickname: '逍遥测试用户',
        avatar: '',
        phone: '138****8888',
        balance: 299.50,
        level: 'VIP',
        creditScore: 850,
        registerTime: '2024-01-15'
      }
    }
  }

  if (url.includes('/equipment')) {
    return {
      success: true,
      data: require('./mockData.js').mockEquipment
    }
  }

  if (url.includes('/locations')) {
    return {
      success: true,
      data: require('./mockData.js').mockLocations
    }
  }

  if (url.includes('/orders')) {
    return {
      success: true,
      data: require('./mockData.js').mockOrders || []
    }
  }

  // 默认返回成功响应
  return {
    success: true,
    message: '操作成功',
    data: null
  }
}

/**
 * GET请求封装
 * @param {string} url 请求URL
 * @param {object} params 请求参数
 * @param {object} options 额外配置
 * @returns {Promise}
 */
function get(url, params = {}, options = {}) {
  const queryString = Object.keys(params)
    .map(key => `${encodeURIComponent(key)}=${encodeURIComponent(params[key])}`)
    .join('&')
  
  const fullUrl = queryString ? `${url}?${queryString}` : url
  
  return request({
    url: fullUrl,
    method: 'GET',
    ...options
  })
}

/**
 * POST请求封装
 * @param {string} url 请求URL
 * @param {object} data 请求数据
 * @param {object} options 额外配置
 * @returns {Promise}
 */
function post(url, data = {}, options = {}) {
  return request({
    url,
    method: 'POST',
    data,
    ...options
  })
}

/**
 * PUT请求封装
 * @param {string} url 请求URL
 * @param {object} data 请求数据
 * @param {object} options 额外配置
 * @returns {Promise}
 */
function put(url, data = {}, options = {}) {
  return request({
    url,
    method: 'PUT',
    data,
    ...options
  })
}

/**
 * DELETE请求封装
 * @param {string} url 请求URL
 * @param {object} options 额外配置
 * @returns {Promise}
 */
function del(url, options = {}) {
  return request({
    url,
    method: 'DELETE',
    ...options
  })
}

/**
 * 文件上传
 * @param {string} url 上传URL
 * @param {string} filePath 本地文件路径
 * @param {object} options 额外配置
 * @returns {Promise}
 */
function uploadFile(url, filePath, options = {}) {
  return new Promise((resolve, reject) => {
    const config = {
      url: buildUrl(url),
      filePath,
      name: 'file',
      header: buildHeaders(options.header),
      formData: options.formData || {},
      ...options
    }
    
    console.log('上传文件：', config.url)
    
    if (config.showLoading) {
      wx.showLoading({
        title: '上传中...',
        mask: true
      })
    }
    
    const uploadTask = wx.uploadFile({
      ...config,
      success: (response) => {
        console.log('文件上传成功：', response.statusCode)
        
        try {
          const data = JSON.parse(response.data)
          resolve(data)
        } catch (error) {
          resolve(response.data)
        }
      },
      fail: (error) => {
        console.error('文件上传失败：', error)
        
        if (config.showError) {
          wx.showToast({
            title: '上传失败',
            icon: 'error'
          })
        }
        
        reject(error)
      },
      complete: () => {
        if (config.showLoading) {
          wx.hideLoading()
        }
      }
    })
    
    // 监听上传进度
    if (options.onProgress) {
      uploadTask.onProgressUpdate(options.onProgress)
    }
  })
}

/**
 * 设置API配置
 * @param {object} config 配置对象
 */
function setConfig(config) {
  Object.assign(API_CONFIG, config)
  console.log('API配置已更新：', API_CONFIG)
}

/**
 * 获取API配置
 * @returns {object} 当前配置
 */
function getConfig() {
  return { ...API_CONFIG }
}

module.exports = {
  request,
  get,
  post,
  put,
  delete: del,
  uploadFile,
  setConfig,
  getConfig
} 