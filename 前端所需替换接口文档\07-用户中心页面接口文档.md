# 用户中心页面接口文档

## 页面概述
用户中心页面，显示用户基本信息、统计数据，提供各种功能入口（充值、交易明细、收藏、优惠券等）。

## 当前实现分析

### 页面文件位置
- `pages/profile/profile.js` - 用户中心页逻辑
- `utils/auth.js` - 用户认证工具
- `utils/galleryManager.js` - 作品集管理

### 当前功能流程
1. **用户信息显示**：显示头像、昵称、余额等基本信息
2. **统计数据**：显示总订单数、总飞行时长、作品数量
3. **功能入口**：
   - 充值功能
   - 交易明细
   - 我的收藏
   - 优惠券管理
   - 会员特权
   - 设置选项
4. **退出登录**：清除本地数据并跳转到登录页

## 需要替换的接口

### 1. 获取用户统计数据接口

#### 接口信息
- **接口名称**: 获取用户统计数据
- **请求方法**: GET
- **接口路径**: `/api/user/stats`
- **当前模拟位置**: `pages/profile/profile.js` 第140-165行 `loadUserStats` 方法

#### 请求头
```
Authorization: Bearer {token}
```

#### 响应数据
```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "totalOrders": 25,
    "totalFlightTime": 120,
    "worksCount": 45,
    "totalSpent": 2580,
    "memberLevel": "silver",
    "memberLevelText": "白银会员",
    "points": 1250,
    "couponCount": 3,
    "favoriteCount": 8,
    "reviewCount": 12,
    "averageRating": 4.8
  }
}
```

### 2. 获取用户收藏列表接口

#### 接口信息
- **接口名称**: 获取用户收藏的地点和设备
- **请求方法**: GET
- **接口路径**: `/api/user/favorites`
- **当前模拟位置**: `pages/profile/profile.js` 第207-213行 `goToFavorites` 方法

#### 请求头
```
Authorization: Bearer {token}
```

#### 请求参数
```
page=1     // 页码
limit=20   // 每页数量
type=all   // 收藏类型：all/location/equipment
```

#### 响应数据
```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "list": [
      {
        "id": "fav_001",
        "type": "location",
        "itemId": "shusheng_01",
        "itemName": "书圣故里",
        "itemImage": "图片URL",
        "itemRating": 4.9,
        "favoriteTime": "2024-01-26T10:00:00.000Z",
        "available": true
      },
      {
        "id": "fav_002",
        "type": "equipment",
        "itemId": "dji_air3",
        "itemName": "DJI Air 3",
        "itemImage": "图片URL",
        "itemPrice": 80,
        "favoriteTime": "2024-01-25T15:30:00.000Z",
        "available": true
      }
    ],
    "pagination": {
      "page": 1,
      "limit": 20,
      "total": 8,
      "totalPages": 1
    }
  }
}
```

### 3. 获取用户优惠券接口

#### 接口信息
- **接口名称**: 获取用户优惠券列表
- **请求方法**: GET
- **接口路径**: `/api/user/coupons`
- **当前模拟位置**: `pages/profile/profile.js` 第215-221行 `goToCoupons` 方法

#### 请求头
```
Authorization: Bearer {token}
```

#### 请求参数
```
page=1        // 页码
limit=20      // 每页数量
status=all    // 优惠券状态：all/unused/used/expired
```

#### 响应数据
```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "list": [
      {
        "id": "coupon_001",
        "name": "首次租赁9折券",
        "description": "首次租赁享受9折优惠",
        "type": "discount",
        "value": 0.9,
        "minAmount": 50,
        "maxDiscount": 50,
        "status": "unused",
        "statusText": "未使用",
        "validFrom": "2024-01-01T00:00:00.000Z",
        "validTo": "2024-12-31T23:59:59.000Z",
        "applicableItems": ["all"],
        "usageRules": "仅限首次租赁使用"
      },
      {
        "id": "coupon_002",
        "name": "满200减30券",
        "description": "订单满200元减30元",
        "type": "cash",
        "value": 30,
        "minAmount": 200,
        "status": "unused",
        "statusText": "未使用",
        "validFrom": "2024-01-01T00:00:00.000Z",
        "validTo": "2024-03-31T23:59:59.000Z",
        "applicableItems": ["all"]
      }
    ],
    "pagination": {
      "page": 1,
      "limit": 20,
      "total": 3,
      "totalPages": 1
    }
  }
}
```

### 4. 获取交易明细接口

#### 接口信息
- **接口名称**: 获取用户交易明细
- **请求方法**: GET
- **接口路径**: `/api/user/transactions`
- **当前模拟位置**: `pages/profile/profile.js` 第188-196行 `viewTransactions` 方法

#### 请求头
```
Authorization: Bearer {token}
```

#### 请求参数
```
page=1        // 页码
limit=20      // 每页数量
type=all      // 交易类型：all/recharge/consume/refund
startDate=2024-01-01  // 开始日期，可选
endDate=2024-01-31    // 结束日期，可选
```

#### 响应数据
```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "list": [
      {
        "id": "tx_001",
        "type": "consume",
        "typeText": "消费",
        "amount": -175,
        "balance": 124.50,
        "description": "租赁费用扣除",
        "relatedOrderId": "order_001",
        "createTime": "2024-01-27T10:00:00.000Z",
        "status": "success",
        "statusText": "成功"
      },
      {
        "id": "tx_002",
        "type": "recharge",
        "typeText": "充值",
        "amount": 300,
        "balance": 299.50,
        "description": "余额充值",
        "paymentMethod": "wechat",
        "createTime": "2024-01-26T15:30:00.000Z",
        "status": "success",
        "statusText": "成功"
      }
    ],
    "pagination": {
      "page": 1,
      "limit": 20,
      "total": 15,
      "totalPages": 1
    },
    "summary": {
      "totalRecharge": 500,
      "totalConsume": 375,
      "totalRefund": 0,
      "currentBalance": 124.50
    }
  }
}
```

### 5. 用户退出登录接口

#### 接口信息
- **接口名称**: 用户退出登录
- **请求方法**: POST
- **接口路径**: `/api/auth/logout`
- **当前模拟位置**: `pages/profile/profile.js` 第294-330行 `performLogout` 方法

#### 请求头
```
Authorization: Bearer {token}
```

#### 响应数据
```json
{
  "code": 200,
  "message": "退出成功",
  "data": {
    "logoutTime": "2024-01-27T16:00:00.000Z"
  }
}
```

### 6. 更新用户信息接口

#### 接口信息
- **接口名称**: 更新用户基本信息
- **请求方法**: PUT
- **接口路径**: `/api/user/profile`
- **当前模拟位置**: `pages/profile/profile.js` 第170-176行 `editProfile` 方法

#### 请求头
```
Authorization: Bearer {token}
```

#### 请求参数
```json
{
  "nickname": "新昵称",
  "avatar": "头像URL",
  "gender": 1,
  "birthday": "1990-01-01",
  "location": "浙江省绍兴市"
}
```

#### 响应数据
```json
{
  "code": 200,
  "message": "更新成功",
  "data": {
    "id": "user_001",
    "nickname": "新昵称",
    "avatar": "头像URL",
    "gender": 1,
    "birthday": "1990-01-01",
    "location": "浙江省绍兴市",
    "updateTime": "2024-01-27T16:00:00.000Z"
  }
}
```

## 替换指导

### 1. 修改用户统计数据加载
**文件**: `pages/profile/profile.js`
**位置**: 第140-165行 `loadUserStats` 方法

**当前代码**:
```javascript
async loadUserStats() {
  try {
    const userInfo = this.data.userInfo
    if (userInfo) {
      // 从作品集获取真实的作品数量
      const galleryManager = require('../../utils/galleryManager')
      const userGallery = galleryManager.getUserGallery()
      const actualWorksCount = userGallery.length

      const userStats = {
        totalOrders: userInfo.totalOrders || 25,
        totalFlightTime: userInfo.totalFlightTime || 120,
        worksCount: actualWorksCount // 使用真实的作品数量
      }

      this.setData({
        userStats,
        couponCount: 3 // 模拟优惠券数量
      })

      console.log('用户统计数据更新：', userStats)
    }
  } catch (error) {
    console.error('加载用户统计失败：', error)
  }
}
```

**替换为**:
```javascript
async loadUserStats() {
  try {
    const response = await request.get('/api/user/stats')
    
    if (response && response.data) {
      this.setData({
        userStats: {
          totalOrders: response.data.totalOrders,
          totalFlightTime: response.data.totalFlightTime,
          worksCount: response.data.worksCount
        },
        couponCount: response.data.couponCount,
        memberLevel: response.data.memberLevel,
        memberLevelText: response.data.memberLevelText,
        points: response.data.points
      })
      
      console.log('用户统计数据加载完成：', response.data)
    }
  } catch (error) {
    console.error('加载用户统计失败：', error)
    // 使用本地数据作为降级方案
    this.loadUserStatsLocal()
  }
}

// 本地统计数据降级方案
loadUserStatsLocal() {
  const userInfo = this.data.userInfo
  if (userInfo) {
    const galleryManager = require('../../utils/galleryManager')
    const userGallery = galleryManager.getUserGallery()
    
    this.setData({
      userStats: {
        totalOrders: userInfo.totalOrders || 25,
        totalFlightTime: userInfo.totalFlightTime || 120,
        worksCount: userGallery.length
      },
      couponCount: 3
    })
  }
}
```

### 2. 修改收藏功能
**文件**: `pages/profile/profile.js`
**位置**: 第207-213行 `goToFavorites` 方法

**替换为**:
```javascript
async goToFavorites() {
  try {
    const response = await request.get('/api/user/favorites', {
      page: 1,
      limit: 50,
      type: 'all'
    })
    
    if (response && response.data) {
      // 跳转到收藏页面，传递数据
      wx.navigateTo({
        url: `/pages/favorites/favorites?data=${encodeURIComponent(JSON.stringify(response.data))}`
      })
    }
  } catch (error) {
    console.error('获取收藏列表失败：', error)
    // 显示模拟数据
    wx.showModal({
      title: '我的收藏',
      content: '• 书圣故里\n• 鲁迅故里\n• 兰亭景区\n• 沈园\n• 东湖景区\n等8个地点',
      showCancel: false
    })
  }
}
```

### 3. 修改优惠券功能
**文件**: `pages/profile/profile.js`
**位置**: 第215-221行 `goToCoupons` 方法

**替换为**:
```javascript
async goToCoupons() {
  try {
    const response = await request.get('/api/user/coupons', {
      page: 1,
      limit: 20,
      status: 'all'
    })
    
    if (response && response.data) {
      // 跳转到优惠券页面
      wx.navigateTo({
        url: `/pages/coupons/coupons?data=${encodeURIComponent(JSON.stringify(response.data))}`
      })
    }
  } catch (error) {
    console.error('获取优惠券失败：', error)
    // 显示模拟数据
    wx.showModal({
      title: '我的优惠券',
      content: '• 首次租赁9折券\n• 满200减30券\n• 周末特惠券',
      showCancel: false
    })
  }
}
```

### 4. 修改交易明细功能
**文件**: `pages/profile/profile.js`
**位置**: 第188-196行 `viewTransactions` 方法

**替换为**:
```javascript
async viewTransactions() {
  try {
    const response = await request.get('/api/user/transactions', {
      page: 1,
      limit: 20,
      type: 'all'
    })
    
    if (response && response.data) {
      // 跳转到交易明细页面
      wx.navigateTo({
        url: `/pages/transactions/transactions?data=${encodeURIComponent(JSON.stringify(response.data))}`
      })
    }
  } catch (error) {
    console.error('获取交易明细失败：', error)
    // 显示模拟数据
    wx.showModal({
      title: '交易明细',
      content: '• 充值记录\n• 消费记录\n• 退款记录',
      showCancel: false
    })
  }
}
```

### 5. 修改退出登录
**文件**: `pages/profile/profile.js`
**位置**: 第294-330行 `performLogout` 方法

**替换为**:
```javascript
async performLogout() {
  try {
    wx.showLoading({ title: '退出中...' })

    // 调用退出登录接口
    await request.post('/api/auth/logout')

    // 清除本地存储的用户数据
    wx.removeStorageSync('userInfo')
    wx.removeStorageSync('token')
    wx.removeStorageSync('loginTime')

    wx.hideLoading()

    wx.showToast({
      title: '已退出登录',
      icon: 'success',
      duration: 1500
    })

    // 延迟跳转到登录页面
    setTimeout(() => {
      wx.reLaunch({
        url: '/pages/login/login'
      })
    }, 1500)

  } catch (error) {
    wx.hideLoading()
    console.error('退出登录失败：', error)
    
    // 即使接口失败也清除本地数据
    wx.removeStorageSync('userInfo')
    wx.removeStorageSync('token')
    wx.removeStorageSync('loginTime')
    
    wx.showToast({
      title: '已退出登录',
      icon: 'success'
    })
    
    setTimeout(() => {
      wx.reLaunch({
        url: '/pages/login/login'
      })
    }, 1500)
  }
}
```

## 错误处理和降级方案

### 降级方案实现
```javascript
// 统计数据降级方案
loadUserStatsLocal() {
  const userInfo = this.data.userInfo
  if (userInfo) {
    const galleryManager = require('../../utils/galleryManager')
    const userGallery = galleryManager.getUserGallery()
    
    this.setData({
      userStats: {
        totalOrders: userInfo.totalOrders || 25,
        totalFlightTime: userInfo.totalFlightTime || 120,
        worksCount: userGallery.length
      },
      couponCount: 3
    })
  }
}

// 功能页面降级方案
showFeaturePlaceholder(title, content) {
  wx.showModal({
    title: title,
    content: content,
    showCancel: false
  })
}
```

## 注意事项

1. **数据同步**: 用户信息和统计数据需要实时同步
2. **权限验证**: 所有接口都需要验证用户登录状态
3. **缓存策略**: 用户信息可以适当缓存，减少重复请求
4. **退出处理**: 退出登录需要清除所有本地数据
5. **功能完整性**: 各个功能入口需要对应完整的页面实现
6. **会员系统**: 需要支持会员等级和特权展示

## 测试建议

1. 测试用户信息和统计数据的加载
2. 测试各个功能入口的跳转
3. 测试退出登录的完整流程
4. 测试网络异常时的降级方案
5. 测试用户信息更新功能
6. 测试会员特权和优惠券功能
