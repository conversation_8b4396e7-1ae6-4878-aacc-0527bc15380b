/* pages/login/login.wxss */

.login-container {
  position: relative;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  padding: 80rpx 48rpx 32rpx;
  background: linear-gradient(180deg, #f8fafc 0%, #f1f5f9 50%, #e2e8f0 100%);
  overflow: hidden;
}

/* 主要内容区域 */
.main-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  flex: 1;
  position: relative;
  z-index: 10;
}

/* 背景渐变 */
.bg-gradient {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(circle at 30% 20%, rgba(79, 70, 229, 0.08) 0%, transparent 50%),
              radial-gradient(circle at 70% 60%, rgba(16, 185, 129, 0.06) 0%, transparent 50%),
              radial-gradient(circle at 20% 80%, rgba(245, 158, 11, 0.05) 0%, transparent 50%);
  z-index: 1;
}

/* Logo区域 */
.logo-section {
  position: relative;
  z-index: 10;
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 80rpx;
}

.logo-icon {
  width: 140rpx;
  height: 140rpx;
  border-radius: 28rpx;
  background: linear-gradient(135deg, #1f2937 0%, #374151 50%, #4f46e5 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 40rpx;
  box-shadow: 0 16rpx 48rpx rgba(31, 41, 55, 0.2),
              0 8rpx 24rpx rgba(79, 70, 229, 0.15);
  position: relative;
  overflow: hidden;
  backdrop-filter: blur(10rpx);
}

.logo-icon::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg,
    rgba(255, 255, 255, 0.2) 0%,
    rgba(255, 255, 255, 0.1) 50%,
    rgba(255, 255, 255, 0.05) 100%
  );
  opacity: 0;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.logo-icon:active::before {
  opacity: 1;
}

.logo-icon .icon {
  font-size: 72rpx;
  color: #ffffff;
  z-index: 2;
  position: relative;
}

.logo-text {
  text-align: center;
}

.app-name {
  display: block;
  font-size: 52rpx;
  font-weight: 600;
  background: linear-gradient(135deg, #1f2937 0%, #374151 50%, #4f46e5 100%);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  margin-bottom: 12rpx;
  letter-spacing: -0.5rpx;
}

.app-desc {
  display: block;
  font-size: 26rpx;
  color: #64748b;
  font-weight: 400;
  letter-spacing: 0.5rpx;
  opacity: 0.8;
}

/* 欢迎区域 */
.welcome-section {
  position: relative;
  z-index: 10;
  text-align: center;
  margin-bottom: 80rpx;
  padding: 0 20rpx;
}

.welcome-title {
  display: block;
  font-size: 44rpx;
  font-weight: 600;
  background: linear-gradient(135deg, #1f2937 0%, #374151 50%, #4f46e5 100%);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  margin-bottom: 20rpx;
  letter-spacing: -0.5rpx;
  line-height: 1.2;
}

.welcome-desc {
  display: block;
  font-size: 28rpx;
  color: #64748b;
  font-weight: 400;
  line-height: 1.5;
  letter-spacing: 0.5rpx;
  opacity: 0.9;
}

/* 登录方式区域 */
.login-methods {
  position: relative;
  z-index: 10;
  width: 100%;
  max-width: 600rpx;
  margin-bottom: 80rpx;
}

/* 登录按钮 */
.login-btn {
  width: 100%;
  height: 88rpx;
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(20rpx);
  color: #1f2937;
  border-radius: 24rpx;
  font-size: 28rpx;
  font-weight: 500;
  margin-bottom: 20rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border: none;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.08);
}

.login-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg,
    rgba(255, 255, 255, 0.2) 0%,
    rgba(255, 255, 255, 0.1) 100%
  );
  opacity: 0;
  transition: opacity 0.3s ease;
}

.login-btn:active {
  transform: translateY(-2rpx) scale(0.98);
  box-shadow: 0 12rpx 40rpx rgba(0, 0, 0, 0.12);
}

.login-btn:active::before {
  opacity: 1;
}

.btn-icon {
  font-size: 32rpx;
  margin-right: 12rpx;
}

.btn-text {
  font-size: 28rpx;
  font-weight: 500;
  letter-spacing: 0.5rpx;
}

/* 微信登录按钮 */
.wx-login-btn {
  background: linear-gradient(135deg, #1f2937 0%, #374151 50%, #4f46e5 100%);
  color: #ffffff;
  border: none;
  box-shadow: 0 12rpx 40rpx rgba(31, 41, 55, 0.25);
  margin-bottom: 16rpx;
  position: relative;
}

.wx-login-btn::before {
  background: linear-gradient(135deg,
    rgba(255, 255, 255, 0.15) 0%,
    rgba(255, 255, 255, 0.08) 100%
  );
}

.wx-login-btn:active {
  transform: translateY(-2rpx) scale(0.98);
  box-shadow: 0 16rpx 48rpx rgba(31, 41, 55, 0.35);
}

/* 手机号授权登录按钮 */
.phone-auth-btn {
  background: rgba(255, 255, 255, 0.7);
  backdrop-filter: blur(20rpx);
  color: #1f2937;
  border: 1rpx solid rgba(31, 41, 55, 0.2);
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.06);
  position: relative;
}

.phone-auth-btn::before {
  background: linear-gradient(135deg,
    rgba(31, 41, 55, 0.05) 0%,
    rgba(31, 41, 55, 0.02) 100%
  );
}

.phone-auth-btn:active {
  background: rgba(31, 41, 55, 0.05);
  border-color: rgba(31, 41, 55, 0.3);
  transform: translateY(-2rpx) scale(0.98);
  box-shadow: 0 12rpx 40rpx rgba(31, 41, 55, 0.15);
}



/* 输入框组 */
.input-group {
  margin-bottom: 28rpx;
}

.input {
  width: 100%;
  height: 88rpx;
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(20rpx);
  border: none;
  border-radius: 20rpx;
  padding: 0 28rpx;
  font-size: 28rpx;
  color: #1f2937;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.06);
}

.input:focus {
  background: rgba(255, 255, 255, 0.95);
  box-shadow: 0 8rpx 32rpx rgba(79, 70, 229, 0.15);
  outline: none;
  transform: translateY(-2rpx);
}

.input::placeholder {
  color: #94a3b8;
  opacity: 0.7;
}

/* 验证码输入 */
.code-input-wrapper {
  display: flex;
  align-items: center;
  gap: 12rpx;
}

.code-input {
  flex: 1;
}

.send-code-btn {
  height: 88rpx;
  padding: 0 28rpx;
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(20rpx);
  color: #1f2937;
  border: none;
  border-radius: 20rpx;
  font-size: 24rpx;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  flex-shrink: 0;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.06);
}

.send-code-btn:not([disabled]):active {
  background: rgba(255, 255, 255, 0.95);
  transform: translateY(-2rpx);
  box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.1);
}

.send-code-btn[disabled] {
  opacity: 0.5;
  color: #94a3b8;
  background: rgba(249, 250, 251, 0.6);
}



/* 协议区域 - 底部固定 */
.agreement-section {
  position: relative;
  z-index: 10;
  width: 100%;
  margin-top: auto;
  padding: 28rpx 0 0;
}

.agreement-wrapper {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12rpx;
  width: 100%;
  padding: 16rpx 20rpx;
  background: rgba(255, 255, 255, 0.6);
  backdrop-filter: blur(20rpx);
  border-radius: 20rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.04);
}

.agreement-checkbox {
  margin: 0;
  padding: 0;
  flex-shrink: 0;
  transform: scale(0.9);
}

.agreement-text {
  font-size: 22rpx;
  color: #64748b;
  line-height: 1.5;
  text-align: left;
  white-space: nowrap;
  flex: 1;
  font-weight: 400;
}

.link-text {
  color: #4f46e5;
  text-decoration: none;
  cursor: pointer;
  transition: color 0.3s ease;
  font-weight: 500;
}

.link-text:active {
  color: #3730a3;
}

/* 装饰元素 */
.decoration-dots {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 2;
  overflow: hidden;
}

.dot {
  position: absolute;
  border-radius: 50%;
  animation: float 8s ease-in-out infinite;
  backdrop-filter: blur(10rpx);
}

.dot-1 {
  width: 140rpx;
  height: 140rpx;
  top: 15%;
  left: 8%;
  animation-delay: 0s;
  background: radial-gradient(circle, rgba(79, 70, 229, 0.08) 0%, rgba(79, 70, 229, 0.02) 50%, transparent 80%);
}

.dot-2 {
  width: 100rpx;
  height: 100rpx;
  top: 55%;
  right: 12%;
  animation-delay: 2.5s;
  background: radial-gradient(circle, rgba(16, 185, 129, 0.06) 0%, rgba(16, 185, 129, 0.02) 50%, transparent 80%);
}

.dot-3 {
  width: 70rpx;
  height: 70rpx;
  top: 75%;
  left: 15%;
  animation-delay: 5s;
  background: radial-gradient(circle, rgba(245, 158, 11, 0.05) 0%, rgba(245, 158, 11, 0.01) 50%, transparent 80%);
}

@keyframes float {
  0%, 100% {
    transform: translateY(0) rotate(0deg) scale(1);
    opacity: 0.4;
  }
  50% {
    transform: translateY(-30rpx) rotate(180deg) scale(1.1);
    opacity: 0.7;
  }
}

/* 加载遮罩 */
.loading-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(248, 250, 252, 0.9);
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
  backdrop-filter: blur(20rpx);
}

.loading-content {
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(20rpx);
  border-radius: 24rpx;
  padding: 48rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  border: none;
  box-shadow: 0 20rpx 60rpx rgba(0, 0, 0, 0.15);
}

.loading-spinner {
  width: 56rpx;
  height: 56rpx;
  border: 3rpx solid rgba(229, 231, 235, 0.3);
  border-top: 3rpx solid #4f46e5;
  border-radius: 50%;
  animation: spin 1.2s linear infinite;
  margin-bottom: 24rpx;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: 26rpx;
  color: #1f2937;
  text-align: center;
  font-weight: 500;
  letter-spacing: 0.5rpx;
}