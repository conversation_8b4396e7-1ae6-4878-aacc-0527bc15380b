# 无人机控制页面原理解释文档

## 页面概述

无人机控制页面（`pages/drone-control/drone-control`）是逍遥境无人机租赁平台的核心功能页面，用户在订单管理页面点击"继续操控"后进入此页面。该页面提供完整的无人机操控体验，包括飞行控制、拍摄功能、实时数据监控和安全管理等功能。

### 页面功能特点
- **沉浸式操控体验**：全屏横向布局，模拟真实无人机操控界面
- **实时飞行数据**：动态显示高度、速度、电池、信号等关键参数
- **专业拍摄功能**：支持拍照、录像，实时统计拍摄作品数量
- **安全控制机制**：提供返航、紧急停止、退出确认等安全功能
- **设备状态管理**：连接状态、飞行状态、录制状态的完整管理

## 文件结构

### 核心文件组成
- **drone-control.js**：700行，包含完整的无人机控制逻辑
- **drone-control.wxml**：177行，专业的操控界面布局
- **drone-control.wxss**：样式文件，实现沉浸式操控体验
- **drone-control.json**：页面配置，设置横屏显示等

### 核心依赖模块
```javascript
const app = getApp()
const auth = require('../../utils/auth.js')
const { mockEquipment } = require('../../utils/mockData.js')
const galleryManager = require('../../utils/galleryManager.js')
const orderManager = require('../../utils/orderManager.js')
```

**依赖说明**：
- **auth模块**：用户认证和登录状态检查
- **mockEquipment**：设备信息和参数配置
- **galleryManager**：拍摄作品的保存和管理
- **orderManager**：订单信息获取和拍摄统计更新

## 数据结构详解

### 1. 订单和设备信息
```javascript
// 订单和设备信息
orderId: null,           // 当前操控的订单ID
equipmentId: null,       // 设备ID，用于获取设备配置
equipment: null,         // 完整的设备信息对象
```

**数据说明**：
- **如果**：用户从订单页面进入，那么orderId和equipmentId通过URL参数传递
- **如果**：需要显示设备信息，那么根据equipmentId从mockEquipment中获取
- **如果**：需要恢复拍摄统计，那么根据orderId从订单中获取之前的数据

### 2. 飞行状态管理
```javascript
// 飞行状态
isConnected: false,      // 设备连接状态
isFlying: false,         // 飞行状态（是否在空中）
flightMode: 'manual',    // 飞行模式：manual手动, auto自动, return返航
```

**状态说明**：
- **如果**：设备未连接，那么所有控制功能都不可用
- **如果**：设备已连接但未起飞，那么只能执行起飞操作
- **如果**：无人机正在飞行，那么可以执行拍摄、降落、返航等操作

### 3. 实时飞行数据
```javascript
// 飞行数据
flightData: {
  altitude: 125.0,       // 飞行高度（米）
  speed: 15.0,          // 飞行速度（km/h）
  battery: 83,          // 电池电量（百分比）
  signal: 95,           // 信号强度（百分比）
  distance: 2.3,        // 距离起飞点距离（公里）
  windSpeed: 3.0,       // 风速（m/s）
  flightTime: 0         // 飞行时间（秒）
}
```

**数据更新逻辑**：
- **如果**：无人机正在飞行，那么每秒更新一次飞行数据
- **如果**：飞行时间增加，那么电池电量相应减少
- **如果**：无人机移动，那么高度、速度、距离会动态变化

### 4. 相机设置参数
```javascript
// 相机设置
cameraSettings: {
  iso: '200',           // ISO感光度
  shutter: '1/250',     // 快门速度
  aperture: 'f/2.8',    // 光圈值
  focal: '24mm'         // 焦距
}
```

### 5. 控制状态数据
```javascript
// 控制状态
recording: false,        // 录制状态
photoCount: 0,          // 拍照数量统计
videoCount: 0,          // 录像数量统计
```

**统计逻辑**：
- **如果**：用户拍照，那么photoCount增加1
- **如果**：用户停止录像，那么videoCount增加1
- **如果**：页面卸载，那么保存拍摄统计到订单和作品集

### 6. 页面状态管理
```javascript
// 页面状态
showSettingsModal: false, // 设置弹窗显示状态
loading: false,          // 加载状态
showSettings: false,     // 设置面板显示状态
formattedFlightTime: '0:00' // 格式化的飞行时间显示
```

## 页面生命周期详解

### 1. 页面加载（onLoad）
```javascript
onLoad(options) {
  console.log('无人机控制页面加载', options)

  this.setData({
    orderId: options.orderId,
    equipmentId: options.equipmentId
  })

  this.checkAuth()
  this.loadEquipmentInfo()
  this.initDroneConnection()
}
```

**详细执行逻辑**：

1. **参数接收**：
   - **如果**：从订单页面跳转，那么接收orderId和equipmentId参数
   - **数据设置**：将URL参数保存到页面data中

2. **认证检查**：
   - **调用**：this.checkAuth()方法
   - **如果**：用户未登录，那么跳转到登录页面
   - **如果**：用户已登录，那么继续执行后续初始化

3. **设备信息加载**：
   - **调用**：this.loadEquipmentInfo()方法
   - **如果**：需要显示设备信息，那么从mockEquipment中获取设备配置

4. **无人机连接初始化**：
   - **调用**：this.initDroneConnection()方法
   - **如果**：需要开始操控，那么建立与无人机的模拟连接

### 2. 页面渲染完成（onReady）
```javascript
onReady() {
  // 保持屏幕常亮
  wx.setKeepScreenOn({
    keepScreenOn: true
  })
}
```

**详细执行逻辑**：

1. **屏幕常亮设置**：
   - **如果**：用户需要长时间操控无人机，那么保持屏幕常亮
   - **用户体验**：避免操控过程中屏幕自动熄灭影响操作

### 3. 页面显示（onShow）
```javascript
onShow() {
  // 恢复连接
  if (this.data.isConnected) {
    this.resumeConnection()
  }
}
```

**详细执行逻辑**：

1. **连接状态恢复**：
   - **如果**：之前已建立连接，那么恢复数据更新定时器
   - **如果**：用户从其他页面返回，那么继续飞行数据的实时更新

### 4. 页面隐藏（onHide）
```javascript
onHide() {
  // 暂停连接但不断开
  this.pauseConnection()
}
```

**详细执行逻辑**：

1. **连接暂停**：
   - **如果**：用户切换到其他页面，那么暂停数据更新以节省资源
   - **保持状态**：不断开连接，保持无人机状态

### 5. 页面卸载（onUnload）
```javascript
onUnload() {
  // 保存拍摄统计和作品
  this.saveShootingResults()

  // 断开连接
  this.disconnectDrone()

  // 取消屏幕常亮
  wx.setKeepScreenOn({
    keepScreenOn: false
  })
}
```

**详细执行逻辑**：

1. **拍摄结果保存**：
   - **调用**：this.saveShootingResults()方法
   - **如果**：用户进行了拍摄，那么保存拍摄统计和作品到系统中

2. **连接断开**：
   - **调用**：this.disconnectDrone()方法
   - **如果**：页面即将销毁，那么清理所有定时器和连接状态

3. **屏幕设置恢复**：
   - **如果**：页面退出，那么取消屏幕常亮设置
   - **系统恢复**：恢复系统默认的屏幕管理

## 核心功能详解

### 1. 用户认证检查（第157-163行）
```javascript
checkAuth() {
  if (!auth.checkLoginStatus()) {
    auth.redirectToLogin('/pages/drone-control/drone-control')
    return false
  }
  return true
}
```

**详细执行逻辑**：

1. **登录状态检查**：
   - **如果**：用户未登录，那么跳转到登录页面
   - **参数传递**：将当前页面路径作为登录后的返回地址

2. **认证结果返回**：
   - **如果**：用户已登录，那么返回true继续执行
   - **如果**：用户未登录，那么返回false并跳转登录

### 2. 设备信息加载（第168-208行）
```javascript
async loadEquipmentInfo() {
  try {
    const equipment = mockEquipment.find(item => item.id === this.data.equipmentId)

    if (!equipment) {
      throw new Error('设备不存在')
    }

    // 获取订单信息以获取位置数据和拍摄统计
    let locationName = '未知地点'
    let photoCount = 0
    let videoCount = 0

    if (this.data.orderId) {
      const order = orderManager.getOrderById(this.data.orderId)
      if (order) {
        locationName = order.locationName || '未知地点'
        // 恢复之前的拍摄统计
        photoCount = order.photoCount || 0
        videoCount = order.videoCount || 0
      }
    }

    this.setData({
      equipment,
      equipmentName: equipment.name,
      locationName: locationName,
      photoCount: photoCount,
      videoCount: videoCount,
      'flightData.battery': equipment.battery || 100
    })

    console.log('设备信息加载完成：', equipment.name, '位置：', locationName, `拍摄统计：${photoCount}张照片，${videoCount}个视频`)
  } catch (error) {
    console.error('加载设备信息失败：', error)
    wx.showToast({
      title: '设备信息错误',
      icon: 'error'
    })
  }
}
```

**详细执行逻辑**：

1. **设备信息查找**：
   - **数据查找**：在mockEquipment中根据equipmentId查找设备信息
   - **如果**：找不到设备，那么抛出错误并显示提示

2. **订单信息获取**：
   - **如果**：有订单ID，那么获取订单中的位置信息和拍摄统计
   - **数据恢复**：恢复用户之前的拍摄进度，支持断点续拍

3. **页面数据设置**：
   - **设备信息**：设置设备名称、位置名称
   - **拍摄统计**：恢复之前的拍照和录像数量
   - **电池状态**：从设备配置中获取电池电量

4. **错误处理**：
   - **如果**：加载失败，那么显示错误提示
   - **用户反馈**：明确告知用户设备信息加载出现问题

### 3. 无人机连接初始化（第213-249行）
```javascript
async initDroneConnection() {
  this.setData({ loading: true })

  try {
    // 模拟连接过程
    wx.showLoading({ title: '连接设备中...' })

    await new Promise(resolve => setTimeout(resolve, 3000))

    this.setData({
      isConnected: true,
      controlEnabled: true,
      loading: false,
      formattedFlightTime: '0:00'
    })

    // 开始飞行数据更新
    this.startFlightDataUpdate()

    wx.hideLoading()
    wx.showToast({
      title: '设备连接成功',
      icon: 'success'
    })

    console.log('无人机连接成功')
  } catch (error) {
    console.error('连接失败：', error)
    wx.hideLoading()
    wx.showToast({
      title: '连接失败',
      icon: 'error'
    })

    this.setData({ loading: false })
  }
}
```

**详细执行逻辑**：

1. **连接状态设置**：
   - **如果**：开始连接过程，那么设置loading为true
   - **用户反馈**：显示"连接设备中..."的loading提示

2. **连接过程模拟**：
   - **延迟处理**：使用3秒延迟模拟真实的设备连接过程
   - **如果**：在真实环境中，那么这里会建立与无人机的实际通信连接

3. **连接成功处理**：
   - **状态更新**：设置isConnected为true，启用控制功能
   - **数据初始化**：重置飞行时间显示
   - **定时器启动**：开始飞行数据的实时更新

4. **用户反馈**：
   - **成功提示**：显示"设备连接成功"的toast
   - **界面更新**：隐藏loading状态，显示操控界面

5. **错误处理**：
   - **如果**：连接失败，那么显示错误提示并重置loading状态
   - **降级处理**：连接失败时用户仍可查看界面但无法操控

### 4. 飞行数据更新系统（第254-289行）
```javascript
startFlightDataUpdate() {
  this.flightDataTimer = setInterval(() => {
    if (this.data.isConnected) {
      this.updateFlightData()
    }
  }, 1000)
}

updateFlightData() {
  const flightData = { ...this.data.flightData }

  // 模拟飞行数据变化
  if (this.data.isFlying) {
    flightData.flightTime += 1
    flightData.battery = Math.max(0, flightData.battery - 0.1)
    flightData.altitude += (Math.random() - 0.5) * 2
    flightData.speed = Math.random() * 15
    flightData.distance += flightData.speed / 3600
  }

  flightData.signal = 90 + Math.random() * 10
  flightData.gps = 10 + Math.random() * 5

  // 格式化飞行时间
  const minutes = Math.floor(flightData.flightTime / 60)
  const seconds = Math.floor(flightData.flightTime % 60)
  const formattedFlightTime = `${minutes}:${seconds.toString().padStart(2, '0')}`

  this.setData({
    flightData,
    formattedFlightTime
  })
}
```

**详细执行逻辑**：

1. **定时器启动**：
   - **更新频率**：每1秒更新一次飞行数据
   - **如果**：设备已连接，那么执行数据更新

2. **飞行状态数据更新**：
   - **如果**：无人机正在飞行，那么更新飞行时间、电池、高度、速度、距离
   - **电池消耗**：每秒减少0.1%的电池电量
   - **高度变化**：模拟飞行过程中的高度微调
   - **速度计算**：随机生成0-15km/h的飞行速度
   - **距离累计**：根据速度累计飞行距离

3. **通信状态更新**：
   - **信号强度**：在90-100%之间随机变化
   - **GPS状态**：在10-15颗卫星之间变化

4. **时间格式化**：
   - **分钟计算**：将总秒数转换为分钟数
   - **秒数计算**：计算剩余秒数并补零显示
   - **格式显示**：以"分:秒"格式显示飞行时间

### 5. 起飞控制（第294-316行）
```javascript
takeOff() {
  if (!this.data.isConnected || this.data.isFlying) return

  wx.showModal({
    title: '确认起飞',
    content: '请确保周围环境安全，确认起飞？',
    success: (res) => {
      if (res.confirm) {
        this.setData({
          isFlying: true,
          'flightData.altitude': 5
        })

        wx.showToast({
          title: '起飞成功',
          icon: 'success'
        })

        console.log('无人机起飞')
      }
    }
  })
}
```

**详细执行逻辑**：

1. **前置条件检查**：
   - **如果**：设备未连接或已在飞行，那么直接返回不执行起飞
   - **安全保护**：防止重复起飞或在未连接状态下起飞

2. **安全确认**：
   - **用户确认**：显示确认对话框提醒用户检查周围环境
   - **安全提示**：强调安全操作的重要性

3. **起飞执行**：
   - **如果**：用户确认起飞，那么设置isFlying为true
   - **高度设置**：将初始飞行高度设为5米
   - **状态更新**：启用飞行状态，开始飞行数据的动态更新

4. **用户反馈**：
   - **成功提示**：显示"起飞成功"的toast
   - **日志记录**：记录起飞操作到控制台

### 6. 降落控制（第321-336行）
```javascript
land() {
  if (!this.data.isFlying) return

  this.setData({
    isFlying: false,
    'flightData.altitude': 0,
    'flightData.speed': 0
  })

  wx.showToast({
    title: '降落成功',
    icon: 'success'
  })

  console.log('无人机降落')
}
```

**详细执行逻辑**：

1. **飞行状态检查**：
   - **如果**：无人机未在飞行，那么直接返回不执行降落
   - **状态保护**：防止在地面状态下执行降落操作

2. **降落执行**：
   - **状态更新**：设置isFlying为false，停止飞行状态
   - **参数重置**：将高度设为0，速度设为0
   - **数据清理**：停止飞行相关的数据更新

3. **用户反馈**：
   - **成功提示**：显示"降落成功"的toast
   - **日志记录**：记录降落操作到控制台

### 7. 返航控制（第341-366行）
```javascript
returnHome() {
  if (!this.data.isFlying) return

  wx.showModal({
    title: '确认返航',
    content: '无人机将自动返回起飞点，确认返航？',
    success: (res) => {
      if (res.confirm) {
        this.setData({ flightMode: 'return' })

        wx.showToast({
          title: '开始返航',
          icon: 'success'
        })

        // 模拟返航过程
        setTimeout(() => {
          this.land()
          this.setData({ flightMode: 'manual' })
        }, 5000)

        console.log('无人机返航')
      }
    }
  })
}
```

**详细执行逻辑**：

1. **飞行状态检查**：
   - **如果**：无人机未在飞行，那么直接返回不执行返航
   - **前置条件**：只有在飞行状态下才能执行返航操作

2. **返航确认**：
   - **用户确认**：显示确认对话框说明返航操作
   - **操作说明**：明确告知用户无人机将自动返回起飞点

3. **返航执行**：
   - **如果**：用户确认返航，那么设置飞行模式为'return'
   - **模式切换**：从手动模式切换到自动返航模式

4. **返航过程模拟**：
   - **延迟处理**：使用5秒延迟模拟返航飞行过程
   - **自动降落**：返航完成后自动执行降落操作
   - **模式恢复**：降落后恢复为手动模式

5. **用户反馈**：
   - **开始提示**：显示"开始返航"的toast
   - **过程可见**：用户可以看到返航模式的状态变化

### 8. 拍照功能（第536-547行）
```javascript
takePhoto() {
  const photoCount = this.data.photoCount + 1
  this.setData({ photoCount })

  // 闪光效果
  wx.showToast({
    title: `拍照成功！第${photoCount}张`,
    icon: 'success'
  })

  console.log('拍照', photoCount)
}
```

**详细执行逻辑**：

1. **拍照计数**：
   - **数量增加**：每次拍照将photoCount增加1
   - **实时统计**：立即更新页面显示的拍照数量

2. **用户反馈**：
   - **成功提示**：显示包含拍照序号的成功提示
   - **进度显示**：让用户知道当前已拍摄的照片数量

3. **操作记录**：
   - **日志记录**：记录拍照操作和当前总数到控制台

### 9. 录像功能（第552-571行）
```javascript
toggleRecording() {
  const recording = !this.data.recording
  this.setData({ recording })

  if (recording) {
    wx.showToast({
      title: '开始录制',
      icon: 'success'
    })
  } else {
    const videoCount = this.data.videoCount + 1
    this.setData({ videoCount })
    wx.showToast({
      title: `录制结束！第${videoCount}个视频`,
      icon: 'success'
    })
  }

  console.log('录制状态', recording)
}
```

**详细执行逻辑**：

1. **录制状态切换**：
   - **状态反转**：将当前录制状态取反
   - **即时更新**：立即更新页面的录制状态显示

2. **开始录制处理**：
   - **如果**：开始录制，那么显示"开始录制"提示
   - **状态指示**：页面会显示录制中的红色指示器

3. **结束录制处理**：
   - **如果**：结束录制，那么将videoCount增加1
   - **成功提示**：显示包含视频序号的结束提示
   - **统计更新**：实时更新录制视频的总数量

4. **操作记录**：
   - **状态日志**：记录当前录制状态到控制台

### 10. 设置功能（第606-615行）
```javascript
showSettings() {
  this.setData({ showSettingsModal: true })
}

hideSettings() {
  this.setData({ showSettingsModal: false })
}
```

**详细执行逻辑**：

1. **设置弹窗显示**：
   - **如果**：用户点击设置按钮，那么显示设置弹窗
   - **模态显示**：以弹窗形式显示相机设置选项

2. **设置弹窗隐藏**：
   - **如果**：用户点击关闭或确定，那么隐藏设置弹窗
   - **状态恢复**：返回正常的操控界面

### 11. 镜头控制（第620-637行）
```javascript
zoomIn() {
  wx.showToast({
    title: '放大镜头',
    icon: 'success'
  })
  console.log('放大')
}

zoomOut() {
  wx.showToast({
    title: '缩小镜头',
    icon: 'success'
  })
  console.log('缩小')
}
```

**详细执行逻辑**：

1. **镜头放大**：
   - **如果**：用户点击放大按钮，那么执行镜头放大操作
   - **用户反馈**：显示"放大镜头"的操作提示

2. **镜头缩小**：
   - **如果**：用户点击缩小按钮，那么执行镜头缩小操作
   - **用户反馈**：显示"缩小镜头"的操作提示

### 12. 模式切换（第642-653行）
```javascript
changeMode() {
  wx.showActionSheet({
    itemList: ['自动模式', '手动模式', '运动模式', '电影模式'],
    success: (res) => {
      const modes = ['自动模式', '手动模式', '运动模式', '电影模式']
      wx.showToast({
        title: `切换到${modes[res.tapIndex]}`,
        icon: 'success'
      })
    }
  })
}
```

**详细执行逻辑**：

1. **模式选择界面**：
   - **选项显示**：显示4种飞行模式供用户选择
   - **模式说明**：自动、手动、运动、电影四种不同的飞行模式

2. **模式切换确认**：
   - **如果**：用户选择模式，那么显示切换成功的提示
   - **模式应用**：根据选择的模式调整飞行参数和控制方式

### 13. 紧急停止（第483-506行）
```javascript
emergencyStop() {
  wx.showModal({
    title: '紧急停止',
    content: '这将立即停止所有操作，确认执行？',
    confirmColor: '#ef4444',
    success: (res) => {
      if (res.confirm) {
        this.setData({
          isFlying: false,
          'flightData.altitude': 0,
          'flightData.speed': 0,
          flightMode: 'manual'
        })

        wx.showToast({
          title: '紧急停止执行',
          icon: 'success'
        })

        console.log('紧急停止')
      }
    }
  })
}
```

**详细执行逻辑**：

1. **紧急确认**：
   - **警告对话框**：显示红色确认按钮的紧急停止对话框
   - **风险提示**：明确告知用户这将立即停止所有操作

2. **紧急停止执行**：
   - **如果**：用户确认执行，那么立即停止所有飞行操作
   - **状态重置**：将飞行状态、高度、速度全部重置为0
   - **模式恢复**：恢复为手动模式

3. **安全保护**：
   - **即时生效**：紧急停止立即生效，不需要等待
   - **全面重置**：确保无人机处于安全的停止状态

### 14. 退出控制（第511-531行）
```javascript
exitControl() {
  wx.showModal({
    title: '退出操控',
    content: '确认退出无人机操控？\n无人机将自动返航。',
    success: (res) => {
      if (res.confirm) {
        wx.showToast({
          title: '无人机正在返航',
          icon: 'success'
        })

        setTimeout(() => {
          // 返回到订单页面（tabBar页面需要使用switchTab）
          wx.switchTab({
            url: '/pages/orders/orders'
          })
        }, 2000)
      }
    }
  })
}
```

**详细执行逻辑**：

1. **退出确认**：
   - **用户确认**：显示退出操控的确认对话框
   - **安全说明**：告知用户无人机将自动返航

2. **退出处理**：
   - **如果**：用户确认退出，那么显示返航提示
   - **延迟跳转**：等待2秒后跳转到订单页面
   - **页面导航**：使用switchTab跳转到tabBar页面

3. **安全机制**：
   - **自动返航**：确保无人机在用户退出后安全返航
   - **状态保存**：退出前保存拍摄统计和作品

### 15. 拍摄结果保存（第658-699行）
```javascript
async saveShootingResults() {
  try {
    const { photoCount, videoCount, orderId, equipmentId, equipmentName, locationName } = this.data

    // 总是保存拍摄统计，即使为0（用户可能删除了之前的拍摄）

    console.log(`保存拍摄结果：${photoCount}张照片，${videoCount}个视频`)

    // 更新订单的拍摄统计
    if (orderId) {
      const updateResult = await orderManager.updateOrderShootingStats(orderId, {
        photoCount,
        videoCount,
        totalWorks: photoCount + videoCount
      })

      if (updateResult.success) {
        console.log('订单拍摄统计更新成功')
      }
    }

    // 只有在有拍摄内容时才保存作品到作品集
    if (photoCount > 0 || videoCount > 0) {
      const saveResult = await galleryManager.saveBatchWorks({
        orderId: orderId,
        equipmentId: equipmentId,
        equipmentName: equipmentName,
        locationId: 'location_001', // TODO: 从订单获取实际位置ID
        locationName: locationName || '未知地点',
        photoCount: photoCount,
        videoCount: videoCount
      })

      if (saveResult.success) {
        console.log(`作品保存成功：共${saveResult.totalCount}个作品`)
      }
    }

  } catch (error) {
    console.error('保存拍摄结果失败：', error)
  }
}
```

**详细执行逻辑**：

1. **数据提取**：
   - **拍摄统计**：获取当前的拍照和录像数量
   - **订单信息**：获取订单ID、设备信息、位置信息

2. **订单统计更新**：
   - **如果**：有订单ID，那么更新订单的拍摄统计
   - **统计数据**：包括照片数量、视频数量、总作品数
   - **持久化**：确保拍摄统计保存到订单系统中

3. **作品集保存**：
   - **如果**：有拍摄内容，那么保存作品到作品集系统
   - **批量保存**：使用galleryManager批量保存拍摄作品
   - **关联信息**：保存订单、设备、位置的关联关系

4. **条件保存**：
   - **统计总是保存**：即使拍摄数量为0也更新订单统计
   - **作品按需保存**：只有在有实际拍摄内容时才保存到作品集

5. **错误处理**：
   - **异常捕获**：捕获保存过程中的任何错误
   - **日志记录**：记录保存失败的详细信息

## WXML结构详解

### 1. 页面整体容器（第2行）
```xml
<view class="drone-control-container">
  <!-- 无人机控制界面内容 -->
</view>
```

**结构说明**：
- **drone-control-container**：整个无人机控制页面的根容器
- **如果**：需要全屏沉浸式体验，那么使用此容器统一管理布局

### 2. 视频流区域（第4-89行）
```xml
<view class="video-stream">
  <!-- 模拟航拍视频背景 -->
  <image
    class="video-background"
    src="http://iph.href.lu/1920x1080"
    mode="aspectFill"
  />

  <!-- 浮动云朵效果 -->
  <view class="floating-clouds"></view>

  <!-- HUD 覆盖层 -->
  <view class="hud-overlay">
    <!-- 界面元素 -->
  </view>
</view>
```

**详细说明**：

#### 视频背景设计
1. **模拟航拍画面**：
   - **背景图片**：使用1920x1080高清占位图模拟航拍视频流
   - **如果**：在真实环境中，那么这里会显示无人机的实时视频流
   - **显示模式**：使用aspectFill保持画面比例并填充整个区域

2. **视觉效果**：
   - **浮动云朵**：添加动态云朵效果增强沉浸感
   - **如果**：需要更真实的体验，那么添加动态视觉元素

#### HUD覆盖层设计
1. **十字准心（第18行）**：
```xml
<view class="crosshair"></view>
```
- **如果**：需要精确瞄准，那么显示中心十字准心
- **专业体验**：模拟真实无人机操控界面的瞄准辅助

### 3. 顶部状态栏（第21-45行）
```xml
<view class="top-status">
  <view class="status-left">
    <view class="exit-btn" bindtap="exitControl">
      <text class="exit-icon">✕</text>
      <text class="exit-text">退出</text>
    </view>
    <view class="signal-status">
      <text class="signal-icon">📶</text>
      <text class="signal-text">信号强度: {{flightData.signal.toFixed(0)}}%</text>
    </view>
  </view>
  <view class="status-right">
    <view class="battery-display">
      <text class="battery-icon">🔋</text>
      <view class="battery-bar">
        <view class="battery-fill" style="width: {{flightData.battery}}"></view>
      </view>
      <text class="battery-text">{{flightData.battery.toFixed(0)}}%</text>
    </view>
    <view class="flight-time-display">
      <text class="time-icon">🕐</text>
      <text class="time-text">{{formattedFlightTime}}</text>
    </view>
  </view>
</view>
```

**详细说明**：

#### 左侧状态区域
1. **退出按钮**：
   - **功能**：点击退出无人机操控
   - **事件绑定**：bindtap="exitControl"
   - **如果**：用户需要退出，那么触发安全退出流程

2. **信号状态**：
   - **实时显示**：显示当前信号强度百分比
   - **数据绑定**：{{flightData.signal.toFixed(0)}}%
   - **如果**：信号强度变化，那么实时更新显示

#### 右侧状态区域
1. **电池显示**：
   - **电池图标**：使用🔋图标表示电池状态
   - **电池条**：动态宽度显示电池电量
   - **百分比**：精确显示电池百分比数值
   - **如果**：电池电量变化，那么电池条宽度相应调整

2. **飞行时间**：
   - **时间图标**：使用🕐图标表示时间
   - **格式化时间**：显示"分:秒"格式的飞行时间
   - **如果**：飞行时间增加，那么实时更新显示

### 4. 飞行参数显示（第47-65行）
```xml
<view class="flight-params">
  <view class="param-item">
    <text class="param-label">高度:</text>
    <text class="param-value">{{flightData.altitude.toFixed(1)}}m</text>
  </view>
  <view class="param-item">
    <text class="param-label">速度:</text>
    <text class="param-value">{{flightData.speed.toFixed(1)}}km/h</text>
  </view>
  <view class="param-item">
    <text class="param-label">距离:</text>
    <text class="param-value">{{flightData.distance.toFixed(1)}}km</text>
  </view>
  <view class="param-item">
    <text class="param-label">风速:</text>
    <text class="param-value">{{flightData.windSpeed.toFixed(1)}}m/s</text>
  </view>
</view>
```

**详细说明**：

#### 参数项设计
1. **高度显示**：
   - **标签**："高度:"
   - **数值**：显示精确到小数点后1位的高度值
   - **单位**：米(m)

2. **速度显示**：
   - **标签**："速度:"
   - **数值**：显示精确到小数点后1位的速度值
   - **单位**：公里每小时(km/h)

3. **距离显示**：
   - **标签**："距离:"
   - **数值**：显示距离起飞点的距离
   - **单位**：公里(km)

4. **风速显示**：
   - **标签**："风速:"
   - **数值**：显示当前风速
   - **单位**：米每秒(m/s)

### 5. 录制状态指示（第83-88行）
```xml
<view class="recording-indicator {{recording ? 'active' : ''}}" wx:if="{{recording}}">
  <text class="recording-dot">●</text>
  <text class="recording-text">正在录制</text>
</view>
```

**详细说明**：

#### 录制指示器
1. **显示条件**：
   - **wx:if="{{recording}}"**：只有在录制状态下才显示
   - **如果**：用户开始录制，那么显示录制指示器

2. **动态样式**：
   - **active状态**：根据recording状态添加active样式类
   - **视觉效果**：录制时显示红色闪烁效果

3. **指示内容**：
   - **录制点**：使用●符号表示录制状态
   - **文字说明**："正在录制"文字提示

### 6. 操作杆控制（第91-103行）
```xml
<!-- 左侧操作杆 -->
<view class="left-joystick-container">
  <view class="control-joystick" id="leftJoystick">
    <view class="joystick-handle"></view>
  </view>
</view>

<!-- 右侧操作杆 -->
<view class="right-joystick-container">
  <view class="control-joystick" id="rightJoystick">
    <view class="joystick-handle"></view>
  </view>
</view>
```

**详细说明**：

#### 双操作杆设计
1. **左侧操作杆**：
   - **功能**：控制无人机的升降和旋转
   - **位置**：屏幕左下角区域
   - **如果**：用户需要控制高度和方向，那么使用左侧操作杆

2. **右侧操作杆**：
   - **功能**：控制无人机的前后左右移动
   - **位置**：屏幕右下角区域
   - **如果**：用户需要控制位置移动，那么使用右侧操作杆

3. **操作杆结构**：
   - **外圈容器**：control-joystick类定义操作杆的外圈
   - **内圈手柄**：joystick-handle类定义可拖拽的手柄
   - **交互设计**：支持触摸拖拽操作

### 7. 底部控制面板（第105-120行）
```xml
<view class="bottom-control-panel">
  <!-- 统一的四个控制按钮 -->
  <view class="control-btn photo-btn" bindtap="takePhoto">
    <text class="control-icon">📷</text>
  </view>
  <view class="control-btn record-btn {{recording ? 'recording' : ''}}" bindtap="toggleRecording">
    <text class="control-icon">{{recording ? '⏹' : '🎥'}}</text>
  </view>
  <view class="control-btn return-btn" bindtap="returnHome">
    <text class="control-icon">🏠</text>
  </view>
  <view class="control-btn settings-btn" bindtap="showSettings">
    <text class="control-icon">⚙️</text>
  </view>
</view>
```

**详细说明**：

#### 控制按钮设计
1. **拍照按钮**：
   - **图标**：📷相机图标
   - **功能**：执行拍照操作
   - **事件**：bindtap="takePhoto"

2. **录制按钮**：
   - **动态图标**：录制时显示⏹停止图标，未录制时显示🎥录像图标
   - **动态样式**：录制时添加recording样式类
   - **功能**：切换录制状态
   - **事件**：bindtap="toggleRecording"

3. **返航按钮**：
   - **图标**：🏠房屋图标表示返回起飞点
   - **功能**：执行自动返航操作
   - **事件**：bindtap="returnHome"

4. **设置按钮**：
   - **图标**：⚙️齿轮图标表示设置
   - **功能**：显示相机设置弹窗
   - **事件**：bindtap="showSettings"

### 8. 侧边功能栏（第122-133行）
```xml
<view class="side-controls">
  <view class="side-btn zoom-in-btn" bindtap="zoomIn">
    <text class="side-icon">+</text>
  </view>
  <view class="side-btn zoom-out-btn" bindtap="zoomOut">
    <text class="side-icon">-</text>
  </view>
  <view class="side-btn mode-btn" bindtap="changeMode">
    <text class="side-icon">👁</text>
  </view>
</view>
```

**详细说明**：

#### 侧边控制按钮
1. **放大按钮**：
   - **图标**：+加号表示放大
   - **功能**：放大镜头焦距
   - **事件**：bindtap="zoomIn"

2. **缩小按钮**：
   - **图标**：-减号表示缩小
   - **功能**：缩小镜头焦距
   - **事件**：bindtap="zoomOut"

3. **模式按钮**：
   - **图标**：👁眼睛图标表示视觉模式
   - **功能**：切换飞行模式
   - **事件**：bindtap="changeMode"

### 9. 设置弹窗（第136-175行）
```xml
<view class="settings-modal" wx:if="{{showSettingsModal}}" bindtap="hideSettings">
  <view class="settings-content" catchtap="">
    <view class="settings-header">
      <text class="settings-title">相机设置</text>
      <text class="settings-close" bindtap="hideSettings">✕</text>
    </view>

    <view class="settings-list">
      <view class="setting-item">
        <text class="setting-label">ISO</text>
        <text class="setting-value">自动/手动</text>
      </view>
      <!-- 其他设置项 -->
    </view>

    <view class="settings-actions">
      <button class="settings-confirm-btn" bindtap="hideSettings">确定</button>
    </view>
  </view>
</view>
```

**详细说明**：

#### 弹窗结构设计
1. **显示条件**：
   - **wx:if="{{showSettingsModal}}"**：根据状态控制弹窗显示
   - **如果**：用户点击设置按钮，那么显示设置弹窗

2. **弹窗交互**：
   - **背景点击**：点击弹窗背景关闭弹窗
   - **内容区域**：使用catchtap=""阻止事件冒泡
   - **关闭按钮**：右上角✕按钮关闭弹窗

3. **设置项目**：
   - **ISO设置**：感光度设置选项
   - **白平衡**：色彩平衡设置
   - **曝光**：曝光补偿设置
   - **格式**：拍摄格式设置
   - **分辨率**：视频分辨率设置

## 业务流程分析

### 1. 正常操控流程
1. **如果**：用户从订单页面进入，那么首先检查登录状态和设备连接
2. **如果**：设备连接成功，那么显示操控界面并开始实时数据更新
3. **如果**：用户需要起飞，那么确认安全后执行起飞操作
4. **如果**：无人机在飞行中，那么用户可以进行拍摄、录像等操作
5. **如果**：用户完成拍摄，那么可以选择返航或继续操控
6. **如果**：用户退出操控，那么保存拍摄结果并安全返航

### 2. 拍摄作品管理流程
1. **如果**：用户进行拍照，那么实时增加拍照计数
2. **如果**：用户进行录像，那么切换录制状态并在结束时增加视频计数
3. **如果**：页面卸载，那么保存拍摄统计到订单系统
4. **如果**：有拍摄内容，那么同时保存作品到作品集系统

### 3. 安全控制流程
1. **如果**：出现紧急情况，那么用户可以执行紧急停止
2. **如果**：需要返航，那么无人机自动返回起飞点并降落
3. **如果**：用户退出操控，那么确保无人机安全返航后才允许退出
4. **如果**：设备连接断开，那么停止所有操作并提示用户

### 4. 数据同步流程
1. **如果**：无人机正在飞行，那么每秒更新飞行数据
2. **如果**：电池电量低，那么提醒用户及时返航
3. **如果**：信号强度弱，那么显示信号警告
4. **如果**：拍摄统计变化，那么实时更新显示

## 总结

无人机控制页面作为整个租赁平台的核心操控界面，实现了以下关键功能：

1. **专业的操控体验**：如果用户需要操控无人机，那么提供接近真实设备的操控界面和交互体验
2. **完整的飞行管理**：如果无人机需要飞行，那么提供起飞、飞行、返航、降落的完整生命周期管理
3. **实时的数据监控**：如果需要了解飞行状态，那么提供高度、速度、电池、信号等关键参数的实时显示
4. **专业的拍摄功能**：如果用户需要拍摄，那么提供拍照、录像功能并实时统计作品数量
5. **完善的安全机制**：如果出现异常情况，那么提供紧急停止、自动返航等安全保护功能
6. **智能的作品管理**：如果用户完成拍摄，那么自动保存拍摄统计和作品到系统中

整个页面的设计严格遵循了"如果...那么..."的条件逻辑，确保在各种操控状态、飞行情况和异常场景下都能提供合适的响应和体验，同时保证了无人机操控的安全性和拍摄作品的完整管理。
