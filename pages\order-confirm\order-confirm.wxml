<!--pages/order-confirm/order-confirm.wxml-->
<view class="confirm-container">
  <!-- 重新预订提示 -->
  <view class="reorder-tip" wx:if="{{isReorder}}">
    <view class="tip-icon">🔄</view>
    <view class="tip-content">
      <text class="tip-title">重新预订</text>
      <text class="tip-desc">设备和地点已为您选好，请重新选择时间</text>
    </view>
  </view>

  <!-- 设备信息 -->
  <view class="info-card">
    <text class="card-title">租赁设备</text>
    <view class="equipment-info">
      <image
        class="equipment-image"
        src="{{equipment.image || 'http://iph.href.lu/320x256'}}"
        mode="aspectFill"
      />
      <view class="equipment-details">
        <text class="equipment-name">{{equipment.name}}</text>
        <text class="equipment-desc">{{equipment.description}}</text>
        <text class="equipment-price">¥{{equipment.price}}<text class="price-unit">/小时</text></text>
      </view>
    </view>
  </view>

  <!-- 拍摄地点 -->
  <view class="info-card">
    <text class="card-title">拍摄地点</text>
    <view class="location-info">
      <image
        class="location-image"
        src="{{location.image || 'http://iph.href.lu/320x256'}}"
        mode="aspectFill"
      />
      <view class="location-details">
        <text class="location-name">{{location.name}}</text>
        <text class="location-desc">{{location.description}}</text>
        <view class="location-meta">
          <text class="location-distance">距离您 {{location.distance}}km</text>
          <text class="location-rating">⭐ {{location.rating}} ({{location.reviewCount}})</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 时间选择 -->
  <view class="info-card">
    <text class="card-title">选择时间</text>

    <!-- 日期选择 -->
    <view class="date-section">
      <text class="form-label">拍摄日期</text>
      <picker
        mode="date"
        value="{{startDate}}"
        bindchange="onDateChange"
        start="{{startDate}}"
      >
        <view class="date-picker">{{startDate || '请选择日期'}}</view>
      </picker>
    </view>

    <!-- 时间段选择 -->
    <view class="time-section">
      <text class="form-label">时间段</text>
      <view class="time-slots">
        <view
          class="time-slot {{selectedTimeSlot === item.value ? 'selected' : ''}} {{!item.available ? 'disabled' : ''}}"
          wx:for="{{timeSlots}}"
          wx:key="value"
          bindtap="selectTimeSlot"
          data-time="{{item.value}}"
          data-available="{{item.available}}"
        >
          <text class="time-value">{{item.label}}</text>
          <text class="time-status">{{item.available ? '可用' : '已预约'}}</text>
        </view>
      </view>
    </view>

    <!-- 租赁时长 -->
    <view class="duration-section">
      <text class="form-label">租赁时长</text>
      <view class="duration-options">
        <view
          class="duration-btn {{selectedDuration === item ? 'selected' : ''}}"
          wx:for="{{durationOptions}}"
          wx:key="*this"
          bindtap="selectDuration"
          data-duration="{{item}}"
        >
          {{item}}小时
        </view>
      </view>
    </view>
  </view>

  <!-- 费用明细 -->
  <view class="info-card">
    <text class="card-title">费用明细</text>
    <view class="fee-list">
      <view class="fee-item">
        <text class="fee-label">设备租赁费</text>
        <text class="fee-value">¥{{equipment.price}} × {{selectedDuration}}小时</text>
      </view>
      <view class="fee-item">
        <text class="fee-label">服务费</text>
        <text class="fee-value">¥{{serviceFee}}</text>
      </view>
      <view class="fee-item">
        <text class="fee-label">保险费</text>
        <text class="fee-value">¥{{insuranceFee}}</text>
      </view>
      <view class="fee-divider"></view>
      <view class="fee-item total">
        <text class="fee-label">总计</text>
        <text class="fee-value">¥{{totalFee}}</text>
      </view>
    </view>
  </view>

  <!-- 余额检查 -->
  <view class="balance-card {{userInfo.balance >= totalFee ? 'sufficient' : 'insufficient'}}">
    <view class="balance-icon">💰</view>
    <view class="balance-info">
      <text class="balance-title">账户余额</text>
      <text class="balance-amount">¥{{userInfo.balance}}</text>
    </view>
    <view class="balance-status">
      <text class="status-icon">{{userInfo.balance >= totalFee ? '✓' : '⚠'}}</text>
      <text class="status-text">{{userInfo.balance >= totalFee ? '余额充足' : '余额不足'}}</text>
    </view>
  </view>
</view>

<!-- 底部操作栏 -->
<view class="bottom-bar">
  <view class="bottom-total">
    <text class="total-label">总计</text>
    <text class="total-amount">¥{{totalFee}}</text>
  </view>
  <button
    class="confirm-btn {{canSubmit ? '' : 'disabled'}}"
    bindtap="submitOrder"
    loading="{{submitting}}"
    disabled="{{!canSubmit || submitting}}"
  >
    {{submitButtonText}}
  </button>
</view>