# 无人机控制页面接口文档

## 页面概述
无人机控制页面，用户可以连接设备、控制飞行、拍摄照片和视频，实时查看飞行数据。

## 当前实现分析

### 页面文件位置
- `pages/drone-control/drone-control.js` - 无人机控制页逻辑
- `utils/galleryManager.js` - 作品集管理
- `utils/orderManager.js` - 订单管理

### 当前功能流程
1. **设备连接**：初始化无人机连接，获取设备状态
2. **飞行控制**：起飞、降落、返航等基本控制
3. **实时数据**：显示高度、速度、电量、信号等飞行数据
4. **拍摄功能**：拍照、录像，统计拍摄数量
5. **作品保存**：将拍摄的照片和视频保存到作品集
6. **订单更新**：更新订单的拍摄统计数据

## 需要替换的接口

### 1. 设备连接接口

#### 接口信息
- **接口名称**: 连接无人机设备
- **请求方法**: POST
- **接口路径**: `/api/drone/connect`
- **当前模拟位置**: `pages/drone-control/drone-control.js` 第213-249行 `initDroneConnection` 方法

#### 请求头
```
Authorization: Bearer {token}
```

#### 请求参数
```json
{
  "orderId": "order_001",
  "equipmentId": "dji_air3",
  "userId": "user_001"
}
```

#### 响应数据
```json
{
  "code": 200,
  "message": "连接成功",
  "data": {
    "connectionId": "conn_1706345678901",
    "deviceStatus": {
      "connected": true,
      "battery": 85,
      "signal": 95,
      "gpsSignal": 12,
      "temperature": 25,
      "firmware": "v1.2.3"
    },
    "flightData": {
      "altitude": 0,
      "speed": 0,
      "distance": 0,
      "flightTime": 0,
      "isFlying": false
    },
    "cameraSettings": {
      "iso": "200",
      "shutter": "1/250",
      "aperture": "f/2.8",
      "focal": "24mm",
      "mode": "auto"
    }
  }
}
```

### 2. 实时飞行数据接口

#### 接口信息
- **接口名称**: 获取实时飞行数据
- **请求方法**: GET
- **接口路径**: `/api/drone/flight-data/{connectionId}`
- **当前模拟位置**: `pages/drone-control/drone-control.js` 第265-289行 `updateFlightData` 方法

#### 请求头
```
Authorization: Bearer {token}
```

#### 请求参数
```
connectionId: 连接ID（路径参数）
```

#### 响应数据
```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "altitude": 125.5,
    "speed": 15.2,
    "battery": 83,
    "signal": 95,
    "gpsSignal": 12,
    "distance": 2.3,
    "windSpeed": 3.2,
    "flightTime": 1800,
    "isFlying": true,
    "flightMode": "manual",
    "temperature": 26,
    "humidity": 65,
    "timestamp": "2024-01-27T10:30:00.000Z"
  }
}
```

### 3. 飞行控制指令接口

#### 接口信息
- **接口名称**: 发送飞行控制指令
- **请求方法**: POST
- **接口路径**: `/api/drone/control`
- **当前模拟位置**: `pages/drone-control/drone-control.js` 第294-367行 `takeOff`, `land`, `returnHome` 方法

#### 请求头
```
Authorization: Bearer {token}
```

#### 请求参数
```json
{
  "connectionId": "conn_1706345678901",
  "command": "takeoff",
  "parameters": {
    "altitude": 10,
    "speed": 5
  }
}
```

#### 支持的指令类型
- `takeoff` - 起飞
- `land` - 降落
- `return_home` - 返航
- `hover` - 悬停
- `emergency_stop` - 紧急停止

#### 响应数据
```json
{
  "code": 200,
  "message": "指令执行成功",
  "data": {
    "commandId": "cmd_1706345678901",
    "command": "takeoff",
    "status": "executing",
    "estimatedTime": 10,
    "currentStatus": {
      "altitude": 5.2,
      "isFlying": true,
      "flightMode": "auto"
    }
  }
}
```

### 4. 拍摄控制接口

#### 接口信息
- **接口名称**: 控制拍照和录像
- **请求方法**: POST
- **接口路径**: `/api/drone/capture`
- **当前模拟位置**: `pages/drone-control/drone-control.js` 第536-555行 `takePhoto` 方法

#### 请求头
```
Authorization: Bearer {token}
```

#### 请求参数
```json
{
  "connectionId": "conn_1706345678901",
  "type": "photo",
  "settings": {
    "iso": "200",
    "shutter": "1/250",
    "aperture": "f/2.8",
    "format": "RAW+JPEG"
  }
}
```

#### 拍摄类型
- `photo` - 拍照
- `video_start` - 开始录像
- `video_stop` - 停止录像

#### 响应数据
```json
{
  "code": 200,
  "message": "拍摄成功",
  "data": {
    "captureId": "cap_1706345678901",
    "type": "photo",
    "filename": "DJI_20240127_103000.jpg",
    "fileSize": 8.5,
    "resolution": "4000x3000",
    "location": {
      "latitude": 30.0041,
      "longitude": 120.5804,
      "altitude": 125.5
    },
    "timestamp": "2024-01-27T10:30:00.000Z",
    "downloadUrl": "https://storage.example.com/photos/DJI_20240127_103000.jpg",
    "thumbnailUrl": "https://storage.example.com/thumbnails/DJI_20240127_103000_thumb.jpg"
  }
}
```

### 5. 保存作品到作品集接口

#### 接口信息
- **接口名称**: 保存拍摄作品到用户作品集
- **请求方法**: POST
- **接口路径**: `/api/gallery/save-work`
- **当前模拟位置**: `pages/drone-control/drone-control.js` 第658-700行 `saveShootingResults` 方法

#### 请求头
```
Authorization: Bearer {token}
```

#### 请求参数
```json
{
  "orderId": "order_001",
  "equipmentId": "dji_air3",
  "equipmentName": "DJI Air 3",
  "locationName": "书圣故里",
  "captureId": "cap_1706345678901",
  "type": "photo",
  "filename": "DJI_20240127_103000.jpg",
  "fileSize": 8.5,
  "downloadUrl": "https://storage.example.com/photos/DJI_20240127_103000.jpg",
  "thumbnailUrl": "https://storage.example.com/thumbnails/DJI_20240127_103000_thumb.jpg",
  "metadata": {
    "resolution": "4000x3000",
    "location": {
      "latitude": 30.0041,
      "longitude": 120.5804,
      "altitude": 125.5
    },
    "cameraSettings": {
      "iso": "200",
      "shutter": "1/250",
      "aperture": "f/2.8"
    }
  }
}
```

#### 响应数据
```json
{
  "code": 200,
  "message": "作品保存成功",
  "data": {
    "workId": "work_1706345678901",
    "galleryId": "gallery_user_001",
    "saveTime": "2024-01-27T10:30:00.000Z"
  }
}
```

### 6. 更新订单拍摄统计接口

#### 接口信息
- **接口名称**: 更新订单的拍摄统计数据
- **请求方法**: PUT
- **接口路径**: `/api/orders/shooting-stats/{orderId}`
- **当前模拟位置**: `utils/orderManager.js` `updateOrderShootingStats` 方法

#### 请求头
```
Authorization: Bearer {token}
```

#### 请求参数
```json
{
  "photoCount": 12,
  "videoCount": 3,
  "totalWorks": 15,
  "lastShootingTime": "2024-01-27T10:30:00.000Z"
}
```

#### 响应数据
```json
{
  "code": 200,
  "message": "统计更新成功",
  "data": {
    "orderId": "order_001",
    "photoCount": 12,
    "videoCount": 3,
    "totalWorks": 15,
    "updateTime": "2024-01-27T10:30:00.000Z"
  }
}
```

### 7. 断开设备连接接口

#### 接口信息
- **接口名称**: 断开无人机连接
- **请求方法**: POST
- **接口路径**: `/api/drone/disconnect`
- **当前模拟位置**: `pages/drone-control/drone-control.js` 第107-118行 `onUnload` 方法

#### 请求头
```
Authorization: Bearer {token}
```

#### 请求参数
```json
{
  "connectionId": "conn_1706345678901",
  "orderId": "order_001",
  "finalStats": {
    "totalFlightTime": 1800,
    "photoCount": 12,
    "videoCount": 3,
    "finalBattery": 65
  }
}
```

#### 响应数据
```json
{
  "code": 200,
  "message": "断开连接成功",
  "data": {
    "disconnectTime": "2024-01-27T11:00:00.000Z",
    "sessionSummary": {
      "duration": 1800,
      "photoCount": 12,
      "videoCount": 3,
      "flightDistance": 5.2
    }
  }
}
```

## 替换指导

### 1. 修改设备连接
**文件**: `pages/drone-control/drone-control.js`
**位置**: 第213-249行 `initDroneConnection` 方法

**替换为**:
```javascript
async initDroneConnection() {
  this.setData({ loading: true })

  try {
    wx.showLoading({ title: '连接设备中...' })

    const response = await request.post('/api/drone/connect', {
      orderId: this.data.orderId,
      equipmentId: this.data.equipmentId,
      userId: auth.getCurrentUser().id
    })

    if (response && response.data) {
      this.setData({
        isConnected: true,
        controlEnabled: true,
        loading: false,
        connectionId: response.data.connectionId,
        flightData: response.data.flightData,
        cameraSettings: response.data.cameraSettings
      })

      // 开始飞行数据更新
      this.startFlightDataUpdate()

      wx.hideLoading()
      wx.showToast({
        title: '设备连接成功',
        icon: 'success'
      })
    }
  } catch (error) {
    console.error('连接失败：', error)
    wx.hideLoading()
    wx.showToast({
      title: '连接失败',
      icon: 'error'
    })
    // 使用模拟连接作为降级方案
    this.initDroneConnectionLocal()
  }
}
```

### 2. 修改实时数据更新
**文件**: `pages/drone-control/drone-control.js`
**位置**: 第265-289行 `updateFlightData` 方法

**替换为**:
```javascript
async updateFlightData() {
  if (!this.data.connectionId) return

  try {
    const response = await request.get(`/api/drone/flight-data/${this.data.connectionId}`)
    
    if (response && response.data) {
      // 格式化飞行时间
      const minutes = Math.floor(response.data.flightTime / 60)
      const seconds = Math.floor(response.data.flightTime % 60)
      const formattedFlightTime = `${minutes}:${seconds.toString().padStart(2, '0')}`

      this.setData({
        flightData: response.data,
        formattedFlightTime,
        isFlying: response.data.isFlying
      })
    }
  } catch (error) {
    console.error('获取飞行数据失败：', error)
    // 使用本地模拟数据
    this.updateFlightDataLocal()
  }
}
```

### 3. 修改拍摄功能
**文件**: `pages/drone-control/drone-control.js`
**位置**: 第536-555行 `takePhoto` 方法

**替换为**:
```javascript
async takePhoto() {
  if (!this.data.isFlying) {
    wx.showToast({
      title: '请先起飞',
      icon: 'none'
    })
    return
  }

  try {
    const response = await request.post('/api/drone/capture', {
      connectionId: this.data.connectionId,
      type: 'photo',
      settings: this.data.cameraSettings
    })

    if (response && response.data) {
      const photoCount = this.data.photoCount + 1
      this.setData({ photoCount })

      // 保存到作品集
      await this.saveWorkToGallery(response.data)

      wx.showToast({
        title: `拍照成功！第${photoCount}张`,
        icon: 'success'
      })
    }
  } catch (error) {
    console.error('拍照失败：', error)
    wx.showToast({
      title: '拍照失败',
      icon: 'error'
    })
  }
}
```

## 错误处理和降级方案

### 降级方案实现
```javascript
// 设备连接降级方案
initDroneConnectionLocal() {
  setTimeout(() => {
    this.setData({
      isConnected: true,
      controlEnabled: true,
      loading: false,
      connectionId: 'local_connection'
    })
    this.startFlightDataUpdate()
  }, 2000)
}

// 飞行数据降级方案
updateFlightDataLocal() {
  const flightData = { ...this.data.flightData }
  
  if (this.data.isFlying) {
    flightData.flightTime += 1
    flightData.battery = Math.max(0, flightData.battery - 0.1)
    flightData.altitude += (Math.random() - 0.5) * 2
  }
  
  this.setData({ flightData })
}
```

## 注意事项

1. **实时性要求**: 飞行数据需要高频率更新（建议1秒一次）
2. **安全控制**: 所有飞行指令需要安全验证
3. **网络稳定性**: 需要处理网络中断的情况
4. **电量监控**: 低电量时需要强制返航
5. **文件管理**: 拍摄文件需要合理的存储和管理
6. **权限控制**: 确保只有授权用户能控制设备

## 测试建议

1. 测试设备连接和断开流程
2. 测试各种飞行控制指令
3. 测试拍摄功能和文件保存
4. 测试网络异常时的降级方案
5. 测试实时数据的准确性
6. 测试安全控制和异常处理
