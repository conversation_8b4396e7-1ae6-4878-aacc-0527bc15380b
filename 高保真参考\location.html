<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>选择拍摄地点 - 逍遥境</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body { height: 852px; overflow: hidden; }
        .status-bar {
            background: black;
            color: white;
            height: 47px;
            font-size: 14px;
            font-weight: 600;
        }
        .content-area {
            height: calc(852px - 47px);
            position: relative;
        }
        .map-container {
            background: linear-gradient(45deg, #e8f5e8 0%, #f0f8f0 50%, #e0f0e0 100%);
            position: relative;
            overflow: hidden;
        }
        .marker {
            position: absolute;
            transform: translate(-50%, -100%);
            cursor: pointer;
            transition: all 0.3s ease;
        }
        .marker:hover {
            transform: translate(-50%, -100%) scale(1.1);
        }
        .location-card {
            transform: translateY(100%);
            transition: transform 0.3s ease;
        }
        .location-card.show {
            transform: translateY(0);
        }
        .confirm-btn {
            background: linear-gradient(135deg, #1f2937 0%, #374151 100%);
            transition: all 0.3s ease;
        }
        .confirm-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(31, 41, 55, 0.3);
        }
    </style>
</head>
<body class="bg-gray-50">
    <!-- iOS 状态栏 -->
    <div class="status-bar flex justify-between items-center px-6">
        <div class="flex items-center space-x-1">
            <span>9:41</span>
        </div>
        <div class="flex items-center space-x-1">
            <i class="fas fa-signal text-xs"></i>
            <i class="fas fa-wifi text-xs"></i>
            <div class="w-6 h-3 border border-white rounded-sm">
                <div class="w-4 h-1 bg-white rounded-sm mt-0.5 ml-0.5"></div>
            </div>
        </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="content-area">
        <!-- 顶部搜索栏 -->
        <div class="absolute top-0 left-0 right-0 z-20 bg-white shadow-sm">
            <div class="flex items-center px-4 py-3">
                <button onclick="goBack()" class="mr-3">
                    <i class="fas fa-chevron-left text-xl text-gray-600"></i>
                </button>
                <div class="flex-1 relative">
                    <input type="text" placeholder="搜索拍摄地点..." 
                           class="w-full bg-gray-100 rounded-full px-4 py-2 text-gray-600 focus:outline-none focus:ring-2 focus:ring-gray-300">
                    <i class="fas fa-search absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400"></i>
                </div>
            </div>
        </div>

        <!-- 地图区域 -->
        <div class="map-container h-full relative">
            <!-- 模拟地图背景 -->
            <div class="absolute inset-0 bg-gradient-to-br from-green-100 to-blue-100 opacity-30"></div>
            
            <!-- 道路线条 -->
            <svg class="absolute inset-0 w-full h-full opacity-20">
                <path d="M 0 300 Q 200 250 400 300" stroke="#666" stroke-width="8" fill="none"/>
                <path d="M 100 0 Q 150 200 200 400" stroke="#666" stroke-width="6" fill="none"/>
                <path d="M 300 100 Q 350 250 400 350" stroke="#666" stroke-width="6" fill="none"/>
            </svg>

            <!-- 当前位置标记 -->
            <div class="marker" style="top: 60%; left: 50%;">
                <div class="w-4 h-4 bg-blue-500 rounded-full border-2 border-white shadow-lg">
                    <div class="w-2 h-2 bg-blue-600 rounded-full m-0.5"></div>
                </div>
                <div class="text-xs text-center mt-1 bg-white px-2 py-1 rounded shadow text-gray-700">
                    我的位置
                </div>
            </div>

            <!-- 书圣故里标记 -->
            <div class="marker" style="top: 40%; left: 35%;" onclick="selectLocation('shusheng')">
                <div class="w-8 h-8 bg-red-500 rounded-full flex items-center justify-center text-white shadow-lg">
                    <i class="fas fa-camera text-sm"></i>
                </div>
                <div class="text-xs text-center mt-1 bg-white px-2 py-1 rounded shadow text-gray-700">
                    书圣故里
                </div>
            </div>

            <!-- 鲁迅故里标记 -->
            <div class="marker" style="top: 30%; left: 60%;" onclick="selectLocation('luxun')">
                <div class="w-8 h-8 bg-red-500 rounded-full flex items-center justify-center text-white shadow-lg">
                    <i class="fas fa-camera text-sm"></i>
                </div>
                <div class="text-xs text-center mt-1 bg-white px-2 py-1 rounded shadow text-gray-700">
                    鲁迅故里
                </div>
            </div>

            <!-- 兰亭景区标记 -->
            <div class="marker" style="top: 25%; left: 30%;" onclick="selectLocation('lanting')">
                <div class="w-8 h-8 bg-red-500 rounded-full flex items-center justify-center text-white shadow-lg">
                    <i class="fas fa-camera text-sm"></i>
                </div>
                <div class="text-xs text-center mt-1 bg-white px-2 py-1 rounded shadow text-gray-700">
                    兰亭景区
                </div>
            </div>

            <!-- 沈园标记 -->
            <div class="marker" style="top: 45%; left: 65%;" onclick="selectLocation('shenyuan')">
                <div class="w-8 h-8 bg-red-500 rounded-full flex items-center justify-center text-white shadow-lg">
                    <i class="fas fa-camera text-sm"></i>
                </div>
                <div class="text-xs text-center mt-1 bg-white px-2 py-1 rounded shadow text-gray-700">
                    沈园
                </div>
            </div>

            <!-- 东湖景区标记 -->
            <div class="marker" style="top: 55%; left: 75%;" onclick="selectLocation('donghu')">
                <div class="w-8 h-8 bg-red-500 rounded-full flex items-center justify-center text-white shadow-lg">
                    <i class="fas fa-camera text-sm"></i>
                </div>
                <div class="text-xs text-center mt-1 bg-white px-2 py-1 rounded shadow text-gray-700">
                    东湖景区
                </div>
            </div>
        </div>

        <!-- 右侧功能按钮 -->
        <div class="absolute right-4 top-20 z-10 space-y-3">
            <button class="w-12 h-12 bg-white rounded-full shadow-lg flex items-center justify-center">
                <i class="fas fa-location-arrow text-gray-600"></i>
            </button>
            <button class="w-12 h-12 bg-white rounded-full shadow-lg flex items-center justify-center">
                <i class="fas fa-plus text-gray-600"></i>
            </button>
            <button class="w-12 h-12 bg-white rounded-full shadow-lg flex items-center justify-center">
                <i class="fas fa-minus text-gray-600"></i>
            </button>
        </div>

        <!-- 底部地点详情卡片 -->
        <div id="locationCard" class="location-card absolute bottom-0 left-0 right-0 bg-white rounded-t-3xl shadow-lg p-6">
            <div class="w-12 h-1 bg-gray-300 rounded-full mx-auto mb-4"></div>
            
            <div id="locationInfo" class="hidden">
                <div class="flex items-start space-x-4 mb-4">
                    <img id="locationImage" src="" alt="" class="w-20 h-16 object-cover rounded-xl">
                    <div class="flex-1">
                        <h3 id="locationName" class="text-lg font-semibold text-gray-800 mb-1"></h3>
                        <p id="locationDesc" class="text-sm text-gray-500 mb-2"></p>
                        <div class="flex items-center space-x-4 text-xs text-gray-500">
                            <div class="flex items-center space-x-1">
                                <i class="fas fa-map-marker-alt"></i>
                                <span id="locationDistance"></span>
                            </div>
                            <div class="flex items-center space-x-1">
                                <i class="fas fa-star text-yellow-400"></i>
                                <span id="locationRating"></span>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="grid grid-cols-3 gap-4 mb-4">
                    <div class="bg-gray-50 rounded-xl p-3 text-center">
                        <i class="fas fa-eye text-gray-600 mb-1"></i>
                        <div class="text-xs text-gray-600">风景优美</div>
                    </div>
                    <div class="bg-gray-50 rounded-xl p-3 text-center">
                        <i class="fas fa-clock text-gray-600 mb-1"></i>
                        <div class="text-xs text-gray-600">全天开放</div>
                    </div>
                    <div class="bg-gray-50 rounded-xl p-3 text-center">
                        <i class="fas fa-shield-alt text-gray-600 mb-1"></i>
                        <div class="text-xs text-gray-600">安全飞行</div>
                    </div>
                </div>

                <button onclick="confirmLocation()" class="confirm-btn w-full text-white py-3 rounded-xl font-medium">
                    确认选择此地点
                </button>
            </div>

            <div id="defaultInfo">
                <div class="text-center py-8">
                    <i class="fas fa-map-marker-alt text-gray-300 text-3xl mb-4"></i>
                    <h3 class="text-lg font-medium text-gray-600 mb-2">选择拍摄地点</h3>
                    <p class="text-sm text-gray-400">点击地图上的标记来选择您想要的拍摄地点</p>
                </div>
            </div>
        </div>
    </div>

    <script>
        const locations = {
            shusheng: {
                name: '书圣故里',
                desc: '古典园林风格，文化底蕴深厚',
                distance: '距离您 2.3km',
                rating: '4.9 (328)',
                image: 'https://images.unsplash.com/photo-1508804185872-d7badad00f7d?w=300&h=200&fit=crop'
            },
            luxun: {
                name: '鲁迅故里',
                desc: '历史古镇，传统建筑群',
                distance: '距离您 3.1km',
                rating: '4.8 (156)',
                image: 'https://images.unsplash.com/photo-1558618047-3c8c76ca7d13?w=300&h=200&fit=crop'
            },
            lanting: {
                name: '兰亭景区',
                desc: '山水园林，书法圣地',
                distance: '距离您 8.5km',
                rating: '4.7 (203)',
                image: 'https://images.unsplash.com/photo-1574958269340-fa927503f3dd?w=300&h=200&fit=crop'
            },
            shenyuan: {
                name: '沈园',
                desc: '宋代名园，诗词文化',
                distance: '距离您 1.8km',
                rating: '4.6 (98)',
                image: 'https://images.unsplash.com/photo-1580993446442-c5b396fa4157?w=300&h=200&fit=crop'
            },
            donghu: {
                name: '东湖景区',
                desc: '湖光山色，自然风光',
                distance: '距离您 5.2km',
                rating: '4.8 (267)',
                image: 'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=300&h=200&fit=crop'
            }
        };

        let selectedLocation = null;

        function goBack() {
            window.history.back();
        }

        function selectLocation(locationId) {
            selectedLocation = locationId;
            const location = locations[locationId];
            
            document.getElementById('locationName').textContent = location.name;
            document.getElementById('locationDesc').textContent = location.desc;
            document.getElementById('locationDistance').textContent = location.distance;
            document.getElementById('locationRating').textContent = location.rating;
            document.getElementById('locationImage').src = location.image;

            document.getElementById('defaultInfo').classList.add('hidden');
            document.getElementById('locationInfo').classList.remove('hidden');
            document.getElementById('locationCard').classList.add('show');
        }

        function confirmLocation() {
            if (selectedLocation) {
                const locationName = locations[selectedLocation].name;
                alert(`已选择拍摄地点：${locationName}\n正在跳转到下单页面...`);
                // 模拟跳转到下单确认页
            }
        }
    </script>
</body>
</html> 