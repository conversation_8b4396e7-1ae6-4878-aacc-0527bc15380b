/* pages/recharge/recharge.wxss */

.recharge-container {
  background: linear-gradient(180deg, #f8fafc 0%, #f1f5f9 100%);
  min-height: 100vh;
  padding-bottom: 300rpx;
  box-sizing: border-box;
}

/* 顶部导航 */
.nav-header {
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  padding: 32rpx 48rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.06);
  border-bottom: 1rpx solid rgba(148, 163, 184, 0.1);
  position: relative;
}

.nav-header::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 1rpx;
  background: linear-gradient(90deg,
    rgba(31, 41, 55, 0) 0%,
    rgba(31, 41, 55, 0.1) 50%,
    rgba(31, 41, 55, 0) 100%
  );
}

.nav-content {
  display: flex;
  align-items: center;
  gap: 32rpx;
}

.back-btn {
  width: 64rpx;
  height: 64rpx;
  background: transparent;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
}

.back-icon {
  font-size: 40rpx;
  color: #6b7280;
}

.nav-title {
  font-size: 40rpx;
  font-weight: 600;
  background: linear-gradient(135deg, #1f2937 0%, #374151 50%, #4f46e5 100%);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  line-height: 1.2;
}

/* 当前余额卡片 */
.balance-card {
  background: linear-gradient(135deg,
    rgba(16, 185, 129, 0.9) 0%,
    rgba(5, 150, 105, 0.9) 100%
  );
  backdrop-filter: blur(20rpx);
  margin: 32rpx;
  border-radius: 28rpx;
  padding: 36rpx;
  color: #ffffff;
  border: 1rpx solid rgba(255, 255, 255, 0.1);
  box-shadow: 0 8rpx 32rpx rgba(16, 185, 129, 0.2);
  position: relative;
  overflow: hidden;
}

.balance-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1rpx;
  background: linear-gradient(90deg,
    rgba(255, 255, 255, 0) 0%,
    rgba(255, 255, 255, 0.4) 50%,
    rgba(255, 255, 255, 0) 100%
  );
}

.balance-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.balance-info {
  flex: 1;
}

.balance-title {
  font-size: 32rpx;
  font-weight: 500;
  opacity: 0.9;
  margin-bottom: 8rpx;
  display: block;
  letter-spacing: 0.5rpx;
}

.balance-amount {
  font-size: 64rpx;
  font-weight: 600;
  margin-bottom: 6rpx;
  display: block;
  letter-spacing: -1rpx;
  line-height: 1.1;
}

.balance-desc {
  font-size: 26rpx;
  opacity: 0.8;
  display: block;
  font-weight: 300;
}

.wallet-icon {
  flex-shrink: 0;
  width: 80rpx;
  height: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.wallet-icon:active {
  transform: scale(0.9);
}

.wallet-emoji {
  font-size: 64rpx;
  opacity: 0.8;
}

/* 充值金额选择 */
.amount-section {
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(20rpx);
  margin: 32rpx 16rpx;
  border-radius: 28rpx;
  padding: 36rpx 16rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.08);
  border: 1rpx solid rgba(255, 255, 255, 0.2);
  overflow: hidden;
  position: relative;
}

.amount-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1rpx;
  background: linear-gradient(90deg,
    rgba(31, 41, 55, 0) 0%,
    rgba(31, 41, 55, 0.1) 50%,
    rgba(31, 41, 55, 0) 100%
  );
}

.section-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 28rpx;
  display: block;
  letter-spacing: 0.5rpx;
}

.amount-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 12rpx;
  margin-bottom: 28rpx;
  width: 100%;
  box-sizing: border-box;
}

.amount-btn {
  background: rgba(249, 250, 251, 0.8);
  backdrop-filter: blur(10rpx);
  border: 1rpx solid rgba(229, 231, 235, 0.5);
  border-radius: 20rpx;
  padding: 20rpx 12rpx;
  text-align: center;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 110rpx;
  box-sizing: border-box;
  flex: 0 0 calc(45% - 6rpx);
  max-width: calc(45% - 6rpx);
  overflow: hidden;
  position: relative;
}

.amount-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg,
    rgba(255, 255, 255, 0.1) 0%,
    rgba(255, 255, 255, 0.05) 100%
  );
  opacity: 0;
  transition: opacity 0.3s ease;
}

.amount-btn:active::before {
  opacity: 1;
}

.amount-btn:active {
  transform: scale(0.98);
}

.amount-btn.selected {
  background: rgba(31, 41, 55, 0.9);
  backdrop-filter: blur(10rpx);
  border-color: rgba(31, 41, 55, 0.8);
  box-shadow: 0 4rpx 16rpx rgba(31, 41, 55, 0.2);
}

.amount-btn.selected .amount-value,
.amount-btn.selected .amount-desc {
  color: #ffffff;
}

.amount-value {
  font-size: 36rpx;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 6rpx;
  display: block;
  letter-spacing: -0.5rpx;
}

.amount-desc {
  font-size: 22rpx;
  color: #6b7280;
  font-weight: 400;
  opacity: 0.8;
}

/* 自定义金额 */
.custom-amount {
  margin-top: 28rpx;
  padding: 0 8rpx;
}

.custom-label {
  font-size: 28rpx;
  font-weight: 500;
  color: #1f2937;
  margin-bottom: 16rpx;
  display: block;
  letter-spacing: 0.5rpx;
}

.custom-input-wrapper {
  position: relative;
  display: flex;
  align-items: center;
  background: rgba(249, 250, 251, 0.8);
  backdrop-filter: blur(10rpx);
  border: 1rpx solid rgba(229, 231, 235, 0.5);
  border-radius: 20rpx;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  height: 88rpx;
  overflow: hidden;
}

.custom-input-wrapper::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg,
    rgba(255, 255, 255, 0.1) 0%,
    rgba(255, 255, 255, 0.05) 100%
  );
  opacity: 0;
  transition: opacity 0.3s ease;
}

.custom-input-wrapper:focus-within {
  border-color: rgba(31, 41, 55, 0.6);
  background: rgba(255, 255, 255, 0.9);
  box-shadow: 0 0 0 3rpx rgba(31, 41, 55, 0.1);
  transform: translateY(-2rpx);
}

.custom-input-wrapper:focus-within::before {
  opacity: 1;
}

.currency-symbol {
  padding: 0 16rpx 0 20rpx;
  font-size: 28rpx;
  font-weight: 600;
  color: #1f2937;
  flex-shrink: 0;
  height: 88rpx;
  line-height: 88rpx;
  z-index: 1;
  position: relative;
}

.custom-input {
  flex: 1;
  border: none;
  background: transparent;
  padding: 0 20rpx 0 0;
  font-size: 28rpx;
  color: #1f2937;
  font-weight: 500;
  box-sizing: border-box;
  min-width: 0;
  height: 88rpx;
  line-height: 88rpx;
  text-align: left;
  vertical-align: middle;
  z-index: 1;
  position: relative;
}

.custom-input::placeholder {
  color: #9ca3af;
  font-weight: 400;
  opacity: 0.7;
}

.amount-tip {
  font-size: 24rpx;
  color: #6b7280;
  margin-top: 12rpx;
  display: block;
  line-height: 1.4;
  padding: 0 8rpx;
  opacity: 0.8;
}

/* 支付方式选择 */
.payment-section {
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(20rpx);
  margin: 32rpx;
  border-radius: 28rpx;
  padding: 36rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.08);
  border: 1rpx solid rgba(255, 255, 255, 0.2);
  position: relative;
  overflow: hidden;
}

.payment-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1rpx;
  background: linear-gradient(90deg,
    rgba(31, 41, 55, 0) 0%,
    rgba(31, 41, 55, 0.1) 50%,
    rgba(31, 41, 55, 0) 100%
  );
}

.payment-list {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.payment-method {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24rpx;
  border: 1rpx solid rgba(229, 231, 235, 0.5);
  border-radius: 20rpx;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  background: rgba(249, 250, 251, 0.6);
  backdrop-filter: blur(10rpx);
  position: relative;
  overflow: hidden;
}

.payment-method::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg,
    rgba(255, 255, 255, 0.1) 0%,
    rgba(255, 255, 255, 0.05) 100%
  );
  opacity: 0;
  transition: opacity 0.3s ease;
}

.payment-method:active::before {
  opacity: 1;
}

.payment-method:active {
  transform: scale(0.98);
}

.payment-method.selected {
  border-color: rgba(31, 41, 55, 0.6);
  background: rgba(31, 41, 55, 0.05);
  box-shadow: 0 4rpx 16rpx rgba(31, 41, 55, 0.1);
}

.payment-left {
  display: flex;
  align-items: center;
  gap: 20rpx;
}

.payment-icon-wrapper {
  width: 80rpx;
  height: 80rpx;
  border-radius: 20rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  backdrop-filter: blur(10rpx);
  border: 1rpx solid rgba(255, 255, 255, 0.3);
}

.payment-icon-wrapper.green {
  background: rgba(220, 252, 231, 0.8);
}

.payment-icon-wrapper.blue {
  background: rgba(219, 234, 254, 0.8);
}

.payment-icon {
  font-size: 36rpx;
}

.payment-info {
  display: flex;
  flex-direction: column;
  gap: 6rpx;
}

.payment-name {
  font-size: 28rpx;
  font-weight: 600;
  color: #1f2937;
  letter-spacing: 0.5rpx;
}

.payment-desc {
  font-size: 24rpx;
  color: #6b7280;
  opacity: 0.8;
}

.payment-radio {
  width: 44rpx;
  height: 44rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.radio-outer {
  width: 44rpx;
  height: 44rpx;
  border: 2rpx solid rgba(209, 213, 219, 0.8);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  backdrop-filter: blur(10rpx);
  transition: all 0.3s ease;
}

.radio-inner {
  width: 20rpx;
  height: 20rpx;
  border-radius: 50%;
  background: transparent;
  transition: all 0.3s ease;
}

.radio-inner.selected {
  background: #1f2937;
  box-shadow: 0 2rpx 8rpx rgba(31, 41, 55, 0.3);
}

/* 充值优惠活动 */
.promo-section {
  background: linear-gradient(135deg,
    rgba(254, 243, 199, 0.9) 0%,
    rgba(253, 230, 138, 0.9) 100%
  );
  backdrop-filter: blur(20rpx);
  margin: 32rpx 16rpx 80rpx 16rpx;
  border-radius: 28rpx;
  padding: 32rpx 28rpx 36rpx 28rpx;
  border: 1rpx solid rgba(251, 191, 36, 0.3);
  box-sizing: border-box;
  overflow: hidden;
  min-height: auto;
  position: relative;
  box-shadow: 0 8rpx 32rpx rgba(251, 191, 36, 0.15);
}

.promo-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1rpx;
  background: linear-gradient(90deg,
    rgba(251, 191, 36, 0) 0%,
    rgba(251, 191, 36, 0.4) 50%,
    rgba(251, 191, 36, 0) 100%
  );
}

.promo-header {
  display: flex;
  align-items: center;
  gap: 20rpx;
  margin-bottom: 20rpx;
}

.promo-icon {
  font-size: 36rpx;
}

.promo-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #92400e;
  letter-spacing: 0.5rpx;
}

.promo-list {
  display: flex;
  flex-direction: column;
  gap: 12rpx;
}

.promo-item {
  display: flex;
  align-items: flex-start;
  gap: 12rpx;
  min-height: 44rpx;
  padding: 6rpx 0;
  box-sizing: border-box;
}

.promo-check {
  font-size: 24rpx;
  color: #d97706;
  font-weight: 600;
  flex-shrink: 0;
  width: 28rpx;
  text-align: center;
  line-height: 1.2;
}

.promo-text {
  font-size: 26rpx;
  color: #92400e;
  line-height: 1.4;
  flex: 1;
  word-wrap: break-word;
  overflow-wrap: break-word;
  font-weight: 400;
}

/* 充值记录入口 */
.history-section {
  background: #ffffff;
  margin: 32rpx;
  border-radius: 32rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.04);
}

.history-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 32rpx;
}

.history-left {
  display: flex;
  align-items: center;
  gap: 24rpx;
}

.history-icon-wrapper {
  width: 80rpx;
  height: 80rpx;
  background: #f3f4f6;
  border-radius: 24rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.history-icon {
  font-size: 32rpx;
}

.history-text {
  font-size: 32rpx;
  font-weight: 500;
  color: #1f2937;
}

.history-arrow {
  font-size: 32rpx;
  color: #9ca3af;
}

/* 安全提示 */
.security-section {
  background: #eff6ff;
  border: 2rpx solid #bfdbfe;
  margin: 32rpx;
  border-radius: 32rpx;
  padding: 32rpx;
}

.security-header {
  display: flex;
  align-items: center;
  gap: 24rpx;
  margin-bottom: 16rpx;
}

.security-icon {
  font-size: 32rpx;
}

.security-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #1e40af;
}

.security-list {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.security-item {
  font-size: 28rpx;
  color: #1e40af;
  line-height: 1.5;
}

/* 底部操作栏 */
.bottom-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20rpx);
  border-top: 1rpx solid rgba(229, 231, 235, 0.5);
  padding: 40rpx;
  z-index: 100;
  box-shadow: 0 -8rpx 32rpx rgba(0, 0, 0, 0.08);
}

.bottom-info {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 20rpx;
}

.bottom-label {
  font-size: 28rpx;
  color: #6b7280;
  font-weight: 400;
}

.bottom-amount {
  font-size: 44rpx;
  font-weight: 600;
  background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  letter-spacing: -0.5rpx;
}

.confirm-btn {
  width: 100%;
  background: linear-gradient(135deg, #1f2937 0%, #374151 100%);
  color: #ffffff;
  border: none;
  border-radius: 20rpx;
  padding: 20rpx 0;
  font-size: 28rpx;
  font-weight: 500;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 4rpx 16rpx rgba(31, 41, 55, 0.2);
  position: relative;
  overflow: hidden;
}

.confirm-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg,
    rgba(255, 255, 255, 0.1) 0%,
    rgba(255, 255, 255, 0.05) 100%
  );
  opacity: 0;
  transition: opacity 0.3s ease;
}

.confirm-btn:not(.disabled):active {
  transform: translateY(-2rpx) scale(0.98);
  box-shadow: 0 8rpx 24rpx rgba(31, 41, 55, 0.3);
}

.confirm-btn:not(.disabled):active::before {
  opacity: 1;
}

.confirm-btn.disabled {
  background: rgba(209, 213, 219, 0.8);
  color: #9ca3af;
  transform: none;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}