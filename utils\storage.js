/**
 * 逍遥境小程序本地存储工具模块
 * 提供统一的本地存储管理、数据序列化、错误处理等功能
 */

/**
 * 存储用户信息
 * @param {object} userInfo 用户信息对象
 * @returns {boolean} 存储是否成功
 */
function setUserInfo(userInfo) {
  try {
    if (!userInfo || typeof userInfo !== 'object') {
      console.error('用户信息格式错误')
      return false
    }
    
    wx.setStorageSync('userInfo', userInfo)
    console.log('用户信息已存储：', userInfo.nickname || userInfo.id)
    return true
  } catch (error) {
    console.error('存储用户信息失败：', error)
    return false
  }
}

/**
 * 获取用户信息
 * @returns {object|null} 用户信息对象
 */
function getUserInfo() {
  try {
    const userInfo = wx.getStorageSync('userInfo')
    return userInfo || null
  } catch (error) {
    console.error('获取用户信息失败：', error)
    return null
  }
}

/**
 * 清除用户信息
 * @returns {boolean} 清除是否成功
 */
function clearUserInfo() {
  try {
    const keysToRemove = [
      'userInfo',
      'isLoggedIn',
      'token',
      'loginTime',
      'redirectUrl'
    ]
    
    keysToRemove.forEach(key => {
      wx.removeStorageSync(key)
    })
    
    console.log('用户相关数据已清除')
    return true
  } catch (error) {
    console.error('清除用户信息失败：', error)
    return false
  }
}

/**
 * 设置访问令牌
 * @param {string} token 访问令牌
 * @returns {boolean} 设置是否成功
 */
function setToken(token) {
  try {
    if (!token || typeof token !== 'string') {
      console.error('Token格式错误')
      return false
    }
    
    wx.setStorageSync('token', token)
    wx.setStorageSync('tokenTime', Date.now())
    console.log('Token已存储')
    return true
  } catch (error) {
    console.error('存储Token失败：', error)
    return false
  }
}

/**
 * 获取访问令牌
 * @returns {string|null} 访问令牌
 */
function getToken() {
  try {
    const token = wx.getStorageSync('token')
    const tokenTime = wx.getStorageSync('tokenTime')
    
    // 检查token是否过期（7天有效期）
    if (token && tokenTime) {
      const tokenAge = Date.now() - tokenTime
      const tokenExpired = tokenAge > 7 * 24 * 60 * 60 * 1000
      
      if (tokenExpired) {
        console.log('Token已过期，清除相关数据')
        clearUserInfo()
        return null
      }
    }
    
    return token || null
  } catch (error) {
    console.error('获取Token失败：', error)
    return null
  }
}

/**
 * 设置登录状态
 * @param {boolean} isLoggedIn 登录状态
 * @returns {boolean} 设置是否成功
 */
function setLoginStatus(isLoggedIn) {
  try {
    wx.setStorageSync('isLoggedIn', !!isLoggedIn)
    if (isLoggedIn) {
      wx.setStorageSync('loginTime', Date.now())
    }
    return true
  } catch (error) {
    console.error('设置登录状态失败：', error)
    return false
  }
}

/**
 * 获取登录状态
 * @returns {boolean} 登录状态
 */
function getLoginStatus() {
  try {
    const isLoggedIn = wx.getStorageSync('isLoggedIn')
    const userInfo = getUserInfo()
    const token = getToken()
    
    // 验证登录状态的完整性
    return !!(isLoggedIn && userInfo && token)
  } catch (error) {
    console.error('获取登录状态失败：', error)
    return false
  }
}

/**
 * 通用存储方法
 * @param {string} key 存储键
 * @param {any} value 存储值
 * @param {object} options 配置选项
 * @returns {boolean} 存储是否成功
 */
function setItem(key, value, options = {}) {
  try {
    if (!key || typeof key !== 'string') {
      console.error('存储键不能为空且必须是字符串')
      return false
    }
    
    const { 
      encrypt = false, 
      expire = null, 
      compress = false 
    } = options
    
    let finalValue = value
    
    // 数据预处理
    if (compress && typeof value === 'string' && value.length > 1000) {
      // 简单的压缩标记，实际项目中可以集成压缩库
      finalValue = { _compressed: true, data: value }
      console.log(`数据压缩存储：${key}`)
    }
    
    // 添加过期时间
    if (expire && typeof expire === 'number') {
      finalValue = {
        _expire: Date.now() + expire,
        _data: finalValue
      }
    }
    
    // 加密处理（这里只是示例，实际项目中需要使用真正的加密算法）
    if (encrypt) {
      finalValue = { _encrypted: true, data: finalValue }
      console.log(`数据加密存储：${key}`)
    }
    
    wx.setStorageSync(key, finalValue)
    console.log(`数据已存储：${key}`)
    return true
  } catch (error) {
    console.error(`存储数据失败：${key}`, error)
    return false
  }
}

/**
 * 通用获取方法
 * @param {string} key 存储键
 * @param {any} defaultValue 默认值
 * @returns {any} 存储的值或默认值
 */
function getItem(key, defaultValue = null) {
  try {
    if (!key || typeof key !== 'string') {
      console.error('存储键不能为空且必须是字符串')
      return defaultValue
    }
    
    let value = wx.getStorageSync(key)
    
    if (value === '' || value === null || value === undefined) {
      return defaultValue
    }
    
    // 处理加密数据
    if (value && typeof value === 'object' && value._encrypted) {
      console.log(`解密数据：${key}`)
      value = value.data
    }
    
    // 处理过期数据
    if (value && typeof value === 'object' && value._expire) {
      if (Date.now() > value._expire) {
        console.log(`数据已过期，清除：${key}`)
        removeItem(key)
        return defaultValue
      }
      value = value._data
    }
    
    // 处理压缩数据
    if (value && typeof value === 'object' && value._compressed) {
      console.log(`解压数据：${key}`)
      value = value.data
    }
    
    return value
  } catch (error) {
    console.error(`获取数据失败：${key}`, error)
    return defaultValue
  }
}

/**
 * 删除存储项
 * @param {string} key 存储键
 * @returns {boolean} 删除是否成功
 */
function removeItem(key) {
  try {
    if (!key || typeof key !== 'string') {
      console.error('存储键不能为空且必须是字符串')
      return false
    }
    
    wx.removeStorageSync(key)
    console.log(`数据已删除：${key}`)
    return true
  } catch (error) {
    console.error(`删除数据失败：${key}`, error)
    return false
  }
}

/**
 * 清除所有存储
 * @param {array} excludeKeys 需要保留的键列表
 * @returns {boolean} 清除是否成功
 */
function clearAll(excludeKeys = []) {
  try {
    const storageInfo = wx.getStorageInfoSync()
    const allKeys = storageInfo.keys || []
    
    allKeys.forEach(key => {
      if (!excludeKeys.includes(key)) {
        wx.removeStorageSync(key)
      }
    })
    
    console.log('存储已清除，保留键：', excludeKeys)
    return true
  } catch (error) {
    console.error('清除存储失败：', error)
    return false
  }
}

/**
 * 获取存储信息
 * @returns {object} 存储信息对象
 */
function getStorageInfo() {
  try {
    const info = wx.getStorageInfoSync()
    const result = {
      keys: info.keys || [],
      currentSize: info.currentSize || 0,
      limitSize: info.limitSize || 10240, // 默认10MB
      usagePercent: Math.round((info.currentSize / info.limitSize) * 100)
    }
    
    console.log('存储信息：', result)
    return result
  } catch (error) {
    console.error('获取存储信息失败：', error)
    return {
      keys: [],
      currentSize: 0,
      limitSize: 10240,
      usagePercent: 0
    }
  }
}

/**
 * 检查存储空间
 * @param {number} warningPercent 警告阈值百分比
 * @returns {object} 检查结果
 */
function checkStorageSpace(warningPercent = 80) {
  try {
    const info = getStorageInfo()
    const isWarning = info.usagePercent >= warningPercent
    const isFull = info.usagePercent >= 95
    
    const result = {
      ...info,
      isWarning,
      isFull,
      message: isFull ? '存储空间即将满' : 
               isWarning ? '存储空间使用较多' : '存储空间正常'
    }
    
    if (isWarning) {
      console.warn('存储空间警告：', result)
    }
    
    return result
  } catch (error) {
    console.error('检查存储空间失败：', error)
    return {
      keys: [],
      currentSize: 0,
      limitSize: 10240,
      usagePercent: 0,
      isWarning: false,
      isFull: false,
      message: '检查失败'
    }
  }
}

/**
 * 设置应用配置
 * @param {object} config 配置对象
 * @returns {boolean} 设置是否成功
 */
function setAppConfig(config) {
  return setItem('appConfig', config)
}

/**
 * 获取应用配置
 * @param {object} defaultConfig 默认配置
 * @returns {object} 配置对象
 */
function getAppConfig(defaultConfig = {}) {
  return getItem('appConfig', defaultConfig)
}

/**
 * 设置缓存数据（带过期时间）
 * @param {string} key 缓存键
 * @param {any} data 缓存数据
 * @param {number} ttl 过期时间（毫秒）
 * @returns {boolean} 设置是否成功
 */
function setCache(key, data, ttl = 24 * 60 * 60 * 1000) { // 默认24小时
  return setItem(`cache_${key}`, data, { expire: ttl })
}

/**
 * 获取缓存数据
 * @param {string} key 缓存键
 * @param {any} defaultValue 默认值
 * @returns {any} 缓存数据或默认值
 */
function getCache(key, defaultValue = null) {
  return getItem(`cache_${key}`, defaultValue)
}

/**
 * 删除缓存数据
 * @param {string} key 缓存键
 * @returns {boolean} 删除是否成功
 */
function removeCache(key) {
  return removeItem(`cache_${key}`)
}

/**
 * 清除所有缓存
 * @returns {boolean} 清除是否成功
 */
function clearCache() {
  try {
    const info = getStorageInfo()
    const cacheKeys = info.keys.filter(key => key.startsWith('cache_'))
    
    cacheKeys.forEach(key => {
      wx.removeStorageSync(key)
    })
    
    console.log(`已清除${cacheKeys.length}个缓存项`)
    return true
  } catch (error) {
    console.error('清除缓存失败：', error)
    return false
  }
}

/**
 * 同步存储信息到服务器（用于数据备份）
 * @param {array} keys 需要同步的键列表
 * @returns {Promise} 同步Promise
 */
async function syncToServer(keys = []) {
  try {
    const data = {}
    
    keys.forEach(key => {
      const value = getItem(key)
      if (value !== null) {
        data[key] = value
      }
    })
    
    // 这里应该调用实际的同步API
    console.log('数据同步到服务器：', Object.keys(data))
    
    // 模拟异步操作
    return new Promise(resolve => {
      setTimeout(() => {
        resolve({ success: true, syncedKeys: Object.keys(data) })
      }, 1000)
    })
  } catch (error) {
    console.error('同步数据失败：', error)
    throw error
  }
}

module.exports = {
  // 用户相关
  setUserInfo,
  getUserInfo,
  clearUserInfo,
  
  // 认证相关
  setToken,
  getToken,
  setLoginStatus,
  getLoginStatus,
  
  // 通用存储
  setItem,
  getItem,
  removeItem,
  clearAll,
  
  // 存储管理
  getStorageInfo,
  checkStorageSpace,
  
  // 应用配置
  setAppConfig,
  getAppConfig,
  
  // 缓存管理
  setCache,
  getCache,
  removeCache,
  clearCache,
  
  // 数据同步
  syncToServer
} 