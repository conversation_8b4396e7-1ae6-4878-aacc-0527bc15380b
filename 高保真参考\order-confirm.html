<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>确认订单 - 逍遥境</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body { height: 852px; overflow: hidden; }
        .status-bar {
            background: black;
            color: white;
            height: 47px;
            font-size: 14px;
            font-weight: 600;
        }
        .content-area {
            height: calc(852px - 47px - 80px);
            overflow-y: auto;
        }
        .time-slot {
            transition: all 0.3s ease;
        }
        .time-slot.selected {
            background: #1f2937;
            color: white;
            transform: scale(1.05);
        }
        .duration-btn {
            transition: all 0.3s ease;
        }
        .duration-btn.selected {
            background: #1f2937;
            color: white;
        }
        .confirm-btn {
            background: linear-gradient(135deg, #1f2937 0%, #374151 100%);
            transition: all 0.3s ease;
        }
        .confirm-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(31, 41, 55, 0.3);
        }
        .confirm-btn:disabled {
            background: #d1d5db;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }
    </style>
</head>
<body class="bg-gray-50">
    <!-- iOS 状态栏 -->
    <div class="status-bar flex justify-between items-center px-6">
        <div class="flex items-center space-x-1">
            <span>9:41</span>
        </div>
        <div class="flex items-center space-x-1">
            <i class="fas fa-signal text-xs"></i>
            <i class="fas fa-wifi text-xs"></i>
            <div class="w-6 h-3 border border-white rounded-sm">
                <div class="w-4 h-1 bg-white rounded-sm mt-0.5 ml-0.5"></div>
            </div>
        </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="content-area">
        <!-- 顶部导航 -->
        <div class="bg-white px-6 py-4 shadow-sm">
            <div class="flex items-center space-x-4">
                <button onclick="goBack()">
                    <i class="fas fa-chevron-left text-xl text-gray-600"></i>
                </button>
                <h1 class="text-xl font-semibold text-gray-800">确认订单</h1>
            </div>
        </div>

        <!-- 设备信息 -->
        <div class="bg-white m-4 rounded-2xl p-5 shadow-sm">
            <h3 class="text-lg font-semibold text-gray-800 mb-3">租赁设备</h3>
            <div class="flex items-center space-x-4">
                <img src="https://images.unsplash.com/photo-1473968512647-3e447244af8f?w=300&h=200&fit=crop" 
                     alt="DJI Air 3" class="w-20 h-16 object-cover rounded-xl">
                <div class="flex-1">
                    <h4 class="font-semibold text-gray-800">DJI Air 3</h4>
                    <p class="text-sm text-gray-500 mb-1">双主摄 · 4K/60fps HDR · 46分钟续航</p>
                    <div class="text-lg font-bold text-gray-800">¥80<span class="text-sm font-normal text-gray-500">/小时</span></div>
                </div>
            </div>
        </div>

        <!-- 拍摄地点 -->
        <div class="bg-white m-4 rounded-2xl p-5 shadow-sm">
            <h3 class="text-lg font-semibold text-gray-800 mb-3">拍摄地点</h3>
            <div class="flex items-center space-x-4">
                <img src="https://images.unsplash.com/photo-1508804185872-d7badad00f7d?w=300&h=200&fit=crop" 
                     alt="书圣故里" class="w-20 h-16 object-cover rounded-xl">
                <div class="flex-1">
                    <h4 class="font-semibold text-gray-800">书圣故里</h4>
                    <p class="text-sm text-gray-500 mb-1">古典园林风格，文化底蕴深厚</p>
                    <div class="flex items-center space-x-4 text-xs text-gray-500">
                        <span>距离您 2.3km</span>
                        <span>⭐ 4.9 (328)</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- 时间选择 -->
        <div class="bg-white m-4 rounded-2xl p-5 shadow-sm">
            <h3 class="text-lg font-semibold text-gray-800 mb-3">选择时间</h3>
            
            <!-- 日期选择 -->
            <div class="mb-4">
                <label class="text-sm font-medium text-gray-600 mb-2 block">拍摄日期</label>
                <input type="date" id="dateInput" class="w-full border border-gray-300 rounded-xl px-4 py-3 focus:outline-none focus:ring-2 focus:ring-gray-300">
            </div>

            <!-- 时间段选择 -->
            <div class="mb-4">
                <label class="text-sm font-medium text-gray-600 mb-2 block">时间段</label>
                <div class="grid grid-cols-3 gap-3">
                    <button class="time-slot bg-gray-50 border border-gray-200 rounded-xl py-3 text-center text-sm" onclick="selectTimeSlot(this, '09:00')">
                        <div class="font-medium">09:00</div>
                        <div class="text-xs text-gray-500">可用</div>
                    </button>
                    <button class="time-slot bg-gray-50 border border-gray-200 rounded-xl py-3 text-center text-sm" onclick="selectTimeSlot(this, '13:00')">
                        <div class="font-medium">13:00</div>
                        <div class="text-xs text-gray-500">可用</div>
                    </button>
                    <button class="time-slot bg-gray-50 border border-gray-200 rounded-xl py-3 text-center text-sm" onclick="selectTimeSlot(this, '16:00')">
                        <div class="font-medium">16:00</div>
                        <div class="text-xs text-gray-500">可用</div>
                    </button>
                    <button class="time-slot bg-gray-50 border border-gray-200 rounded-xl py-3 text-center text-sm" onclick="selectTimeSlot(this, '10:30')">
                        <div class="font-medium">10:30</div>
                        <div class="text-xs text-gray-500">可用</div>
                    </button>
                    <button class="time-slot bg-red-50 border border-red-200 rounded-xl py-3 text-center text-sm opacity-50 cursor-not-allowed">
                        <div class="font-medium text-red-600">14:00</div>
                        <div class="text-xs text-red-500">已预约</div>
                    </button>
                    <button class="time-slot bg-gray-50 border border-gray-200 rounded-xl py-3 text-center text-sm" onclick="selectTimeSlot(this, '18:00')">
                        <div class="font-medium">18:00</div>
                        <div class="text-xs text-gray-500">可用</div>
                    </button>
                </div>
            </div>

            <!-- 租赁时长 -->
            <div>
                <label class="text-sm font-medium text-gray-600 mb-2 block">租赁时长</label>
                <div class="grid grid-cols-4 gap-3">
                    <button class="duration-btn bg-gray-50 border border-gray-200 rounded-xl py-3 text-center text-sm font-medium" onclick="selectDuration(this, 1)">
                        1小时
                    </button>
                    <button class="duration-btn bg-gray-50 border border-gray-200 rounded-xl py-3 text-center text-sm font-medium" onclick="selectDuration(this, 2)">
                        2小时
                    </button>
                    <button class="duration-btn bg-gray-50 border border-gray-200 rounded-xl py-3 text-center text-sm font-medium" onclick="selectDuration(this, 3)">
                        3小时
                    </button>
                    <button class="duration-btn bg-gray-50 border border-gray-200 rounded-xl py-3 text-center text-sm font-medium" onclick="selectDuration(this, 4)">
                        4小时
                    </button>
                </div>
            </div>
        </div>

        <!-- 费用明细 -->
        <div class="bg-white m-4 rounded-2xl p-5 shadow-sm">
            <h3 class="text-lg font-semibold text-gray-800 mb-3">费用明细</h3>
            <div class="space-y-3">
                <div class="flex justify-between items-center">
                    <span class="text-gray-600">设备租赁费</span>
                    <span class="font-medium">¥<span id="equipmentFee">80</span> × <span id="durationDisplay">1</span>小时</span>
                </div>
                <div class="flex justify-between items-center">
                    <span class="text-gray-600">服务费</span>
                    <span class="font-medium">¥10</span>
                </div>
                <div class="flex justify-between items-center">
                    <span class="text-gray-600">保险费</span>
                    <span class="font-medium">¥5</span>
                </div>
                <div class="border-t border-gray-200 pt-3">
                    <div class="flex justify-between items-center">
                        <span class="text-lg font-semibold text-gray-800">总计</span>
                        <span class="text-2xl font-bold text-gray-800">¥<span id="totalFee">95</span></span>
                    </div>
                </div>
            </div>
        </div>

        <!-- 余额检查 -->
        <div class="bg-blue-50 border border-blue-200 m-4 rounded-2xl p-4">
            <div class="flex items-center space-x-3">
                <i class="fas fa-wallet text-blue-600"></i>
                <div class="flex-1">
                    <div class="text-sm font-medium text-blue-800">账户余额</div>
                    <div class="text-lg font-bold text-blue-900">¥<span id="balance">150</span></div>
                </div>
                <div id="balanceStatus" class="text-sm font-medium text-green-600">
                    <i class="fas fa-check-circle"></i> 余额充足
                </div>
            </div>
        </div>
    </div>

    <!-- 底部操作栏 -->
    <div class="fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 px-6 py-4">
        <div class="flex items-center justify-between mb-3">
            <span class="text-gray-600">总计</span>
            <span class="text-2xl font-bold text-gray-800">¥<span id="bottomTotalFee">95</span></span>
        </div>
        <button id="confirmBtn" onclick="confirmOrder()" class="confirm-btn w-full text-white py-3 rounded-xl font-medium" disabled>
            请选择时间和时长
        </button>
    </div>

    <script>
        let selectedTime = null;
        let selectedDuration = 1;
        let equipmentPrice = 80;
        let serviceFee = 10;
        let insuranceFee = 5;
        let userBalance = 150;

        // 设置今天为默认日期
        document.getElementById('dateInput').value = new Date().toISOString().split('T')[0];

        function goBack() {
            window.history.back();
        }

        function selectTimeSlot(button, time) {
            // 清除其他选中状态
            document.querySelectorAll('.time-slot').forEach(btn => {
                btn.classList.remove('selected');
            });
            
            // 选中当前时间段
            button.classList.add('selected');
            selectedTime = time;
            updateConfirmButton();
        }

        function selectDuration(button, hours) {
            // 清除其他选中状态
            document.querySelectorAll('.duration-btn').forEach(btn => {
                btn.classList.remove('selected');
            });
            
            // 选中当前时长
            button.classList.add('selected');
            selectedDuration = hours;
            updatePricing();
            updateConfirmButton();
        }

        function updatePricing() {
            const equipmentTotal = equipmentPrice * selectedDuration;
            const total = equipmentTotal + serviceFee + insuranceFee;
            
            document.getElementById('durationDisplay').textContent = selectedDuration;
            document.getElementById('totalFee').textContent = total;
            document.getElementById('bottomTotalFee').textContent = total;

            // 检查余额
            const balanceStatus = document.getElementById('balanceStatus');
            if (userBalance >= total) {
                balanceStatus.innerHTML = '<i class="fas fa-check-circle"></i> 余额充足';
                balanceStatus.className = 'text-sm font-medium text-green-600';
            } else {
                balanceStatus.innerHTML = '<i class="fas fa-exclamation-circle"></i> 余额不足';
                balanceStatus.className = 'text-sm font-medium text-red-600';
            }
        }

        function updateConfirmButton() {
            const confirmBtn = document.getElementById('confirmBtn');
            const total = (equipmentPrice * selectedDuration) + serviceFee + insuranceFee;
            
            if (selectedTime && selectedDuration && userBalance >= total) {
                confirmBtn.disabled = false;
                confirmBtn.textContent = '确认下单';
                confirmBtn.classList.remove('opacity-50');
            } else if (selectedTime && selectedDuration && userBalance < total) {
                confirmBtn.disabled = true;
                confirmBtn.textContent = '余额不足，请充值';
                confirmBtn.classList.add('opacity-50');
            } else {
                confirmBtn.disabled = true;
                confirmBtn.textContent = '请选择时间和时长';
                confirmBtn.classList.add('opacity-50');
            }
        }

        function confirmOrder() {
            const total = (equipmentPrice * selectedDuration) + serviceFee + insuranceFee;
            
            if (userBalance < total) {
                if (confirm('余额不足，是否前往充值页面？')) {
                    alert('正在跳转到充值页面...');
                    return;
                }
            }

            const date = document.getElementById('dateInput').value;
            const orderInfo = `
订单确认

设备：DJI Air 3
地点：书圣故里
日期：${date}
时间：${selectedTime}
时长：${selectedDuration}小时
总费用：¥${total}

确认下单？
            `;

            if (confirm(orderInfo.trim())) {
                alert('下单成功！正在跳转到无人机操控界面...');
                // 模拟跳转到无人机操控页面
            }
        }

        // 初始化
        updatePricing();
    </script>
</body>
</html> 