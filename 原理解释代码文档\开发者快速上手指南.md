# 逍遥境无人机租赁平台 - 开发者快速上手指南

## 🚀 快速开始

### 📋 环境要求
- **微信开发者工具**: 最新稳定版
- **Node.js**: 14.0+ 版本
- **操作系统**: Windows 10+, macOS 10.14+, Ubuntu 18.04+

### ⚡ 5分钟快速启动
```bash
# 1. 克隆项目
git clone [项目地址]
cd yicix-5

# 2. 安装依赖
npm install

# 3. 启动开发
# 打开微信开发者工具，导入项目目录
# 设置AppID（测试号或正式AppID）
# 点击"编译"开始开发
```

## 📁 项目结构详解

### 🏗️ 核心目录结构
```
yicix-5/
├── 📱 pages/                       # 页面目录
│   ├── login/                      # 登录页面
│   ├── home/                       # 首页
│   ├── equipment/                  # 设备页面
│   ├── equipment-detail/           # 设备详情
│   ├── location/                   # 地点选择
│   ├── order-confirm/              # 订单确认
│   ├── orders/                     # 订单管理
│   ├── drone-control/              # 无人机控制
│   ├── profile/                    # 用户中心
│   ├── gallery/                    # 作品集
│   └── recharge/                   # 充值页面
├── 🔧 utils/                       # 工具类
│   ├── auth.js                     # 认证管理
│   ├── orderManager.js             # 订单管理
│   ├── galleryManager.js           # 作品集管理
│   ├── mockData.js                 # 模拟数据
│   ├── request.js                  # 网络请求
│   ├── storage.js                  # 本地存储
│   └── util.js                     # 工具函数
├── 📚 前端所需替换接口文档/        # API接口文档
├── 📖 原理解释代码文档/            # 代码原理解释
├── 🎨 高保真参考/                  # 设计参考
└── 📋 项目配置文件
```

### 📄 页面文件结构
每个页面包含4个核心文件：
```
pages/[页面名]/
├── [页面名].js                     # 页面逻辑
├── [页面名].wxml                   # 页面结构
├── [页面名].wxss                   # 页面样式
└── [页面名].json                   # 页面配置
```

## 🎯 核心功能模块

### 🔐 1. 用户认证模块 (`utils/auth.js`)
```javascript
// 主要功能
- checkLoginStatus()                # 检查登录状态
- login(username, password)         # 用户登录
- logout()                          # 用户登出
- getCurrentUser()                  # 获取当前用户
- updateUserInfo(userInfo)          # 更新用户信息
- redirectToLogin(returnUrl)        # 重定向到登录页

// 使用示例
const auth = require('../../utils/auth.js')

// 检查登录状态
if (!auth.checkLoginStatus()) {
  auth.redirectToLogin('/pages/orders/orders')
  return
}

// 获取用户信息
const userInfo = auth.getCurrentUser()
console.log('当前用户:', userInfo)
```

### 📦 2. 订单管理模块 (`utils/orderManager.js`)
```javascript
// 主要功能
- createOrder(orderData)            # 创建订单
- getOrderList(status)              # 获取订单列表
- getOrderDetail(orderId)           # 获取订单详情
- updateOrderStatus(orderId, status) # 更新订单状态
- cancelOrder(orderId)              # 取消订单
- calculatePrice(equipment, duration) # 计算价格

// 使用示例
const orderManager = require('../../utils/orderManager.js')

// 创建订单
const orderData = {
  equipmentId: 'drone001',
  locationId: 'loc001',
  startTime: '2024-01-01 10:00',
  duration: 2
}
const order = orderManager.createOrder(orderData)
```

### 🎬 3. 作品集管理模块 (`utils/galleryManager.js`)
```javascript
// 主要功能
- getMediaList(filters)             # 获取媒体列表
- uploadMedia(mediaData)            # 上传媒体文件
- deleteMedia(mediaId)              # 删除媒体文件
- updateMediaInfo(mediaId, info)    # 更新媒体信息
- getStatistics()                   # 获取统计信息

// 使用示例
const galleryManager = require('../../utils/galleryManager.js')

// 获取作品列表
const mediaList = galleryManager.getMediaList({
  type: 'photo',
  date: '2024-01'
})
```

## 🔄 数据流转机制

### 📊 数据存储层次
```
数据存储架构:
├── 🏠 本地存储 (storage.js)
│   ├── 用户信息缓存
│   ├── 登录状态
│   └── 临时数据
├── 📱 页面状态 (Page.data)
│   ├── 界面显示数据
│   ├── 用户交互状态
│   └── 临时计算结果
└── 🌐 远程数据 (API接口)
    ├── 用户数据
    ├── 订单数据
    └── 设备数据
```

### 🔄 数据同步流程
```javascript
// 典型的数据加载流程
Page({
  onLoad() {
    // 1. 检查认证状态
    if (!auth.checkLoginStatus()) {
      auth.redirectToLogin()
      return
    }
    
    // 2. 加载本地缓存数据
    const cachedData = storage.get('pageData')
    if (cachedData) {
      this.setData(cachedData)
    }
    
    // 3. 加载远程数据
    this.loadRemoteData()
  },
  
  async loadRemoteData() {
    try {
      const data = await request.get('/api/data')
      this.setData(data)
      // 4. 更新本地缓存
      storage.set('pageData', data)
    } catch (error) {
      console.error('数据加载失败:', error)
      wx.showToast({
        title: '数据加载失败',
        icon: 'error'
      })
    }
  }
})
```

## 🎨 界面开发规范

### 🎯 样式规范
```css
/* 1. 颜色规范 */
:root {
  --primary-color: #1f2937;        /* 主色调 */
  --secondary-color: #f59e0b;      /* 辅助色 */
  --success-color: #10b981;        /* 成功色 */
  --error-color: #ef4444;          /* 错误色 */
  --text-primary: #111827;         /* 主文本 */
  --text-secondary: #6b7280;       /* 次要文本 */
  --background: #f9fafb;           /* 背景色 */
}

/* 2. 间距规范 */
.spacing-xs { margin: 10rpx; }      /* 小间距 */
.spacing-sm { margin: 20rpx; }      /* 中间距 */
.spacing-md { margin: 30rpx; }      /* 大间距 */
.spacing-lg { margin: 40rpx; }      /* 超大间距 */

/* 3. 字体规范 */
.text-xs { font-size: 24rpx; }      /* 小字体 */
.text-sm { font-size: 28rpx; }      /* 中字体 */
.text-md { font-size: 32rpx; }      /* 大字体 */
.text-lg { font-size: 36rpx; }      /* 超大字体 */
```

### 🧩 组件化开发
```xml
<!-- 可复用的卡片组件 -->
<template name="card">
  <view class="card">
    <view class="card-header">
      <text class="card-title">{{title}}</text>
    </view>
    <view class="card-content">
      <slot></slot>
    </view>
  </view>
</template>

<!-- 使用组件 -->
<template is="card" data="{{title: '设备信息'}}">
  <text>设备详细信息内容</text>
</template>
```

## 🔗 API集成指南

### 🌐 接口配置
```javascript
// utils/request.js 配置
const config = {
  // 开发环境
  dev: {
    baseURL: 'http://localhost:3000/api',
    timeout: 10000
  },
  // 生产环境
  prod: {
    baseURL: 'https://api.xiaoyaojing.com/v1',
    timeout: 5000
  }
}

// 根据环境选择配置
const currentConfig = config[process.env.NODE_ENV || 'dev']
```

### 🔄 接口替换步骤
```javascript
// 1. 替换模拟数据为真实API
// 原来的模拟数据调用
const mockData = require('../../utils/mockData.js')
const equipmentList = mockData.getEquipmentList()

// 替换为真实API调用
const request = require('../../utils/request.js')
const equipmentList = await request.get('/equipment/list')

// 2. 处理错误和加载状态
try {
  wx.showLoading({ title: '加载中...' })
  const data = await request.get('/api/data')
  this.setData({ data })
} catch (error) {
  wx.showToast({
    title: '加载失败',
    icon: 'error'
  })
} finally {
  wx.hideLoading()
}
```

## 🐛 调试和测试

### 🔍 调试技巧
```javascript
// 1. 控制台调试
console.log('调试信息:', data)
console.error('错误信息:', error)

// 2. 页面调试
Page({
  onLoad() {
    // 开发环境显示调试信息
    if (process.env.NODE_ENV === 'development') {
      this.setData({
        debugMode: true,
        debugInfo: '页面加载完成'
      })
    }
  }
})

// 3. 网络请求调试
// 在微信开发者工具中查看Network面板
// 检查请求参数、响应数据、状态码
```

### 🧪 测试建议
```javascript
// 1. 功能测试清单
const testCases = [
  '用户登录流程',
  '设备浏览和筛选',
  '订单创建和支付',
  '无人机控制操作',
  '作品上传和管理',
  '用户信息修改'
]

// 2. 边界条件测试
- 网络异常情况
- 数据为空的情况
- 用户权限不足
- 设备不可用状态
- 支付失败处理
```

## 📚 学习资源

### 📖 必读文档
1. **API接口文档**: `前端所需替换接口文档/` - 了解所有接口规范
2. **原理解释文档**: `原理解释代码文档/` - 深入理解业务逻辑
3. **设计参考**: `高保真参考/` - 了解界面设计要求

### 🎯 开发重点
1. **认证流程**: 理解用户登录和权限控制机制
2. **订单流程**: 掌握从选择到支付的完整订单流程
3. **状态管理**: 理解页面间数据传递和状态同步
4. **错误处理**: 学习异常情况的处理和用户提示

### 🔧 常见问题
```javascript
// Q1: 页面跳转参数传递
// A: 使用navigateTo和页面参数
wx.navigateTo({
  url: '/pages/detail/detail?id=123&type=equipment'
})

// Q2: 数据持久化
// A: 使用storage工具类
const storage = require('../../utils/storage.js')
storage.set('userInfo', userInfo)
const userInfo = storage.get('userInfo')

// Q3: 异步操作处理
// A: 使用async/await或Promise
async loadData() {
  try {
    const data = await request.get('/api/data')
    this.setData({ data })
  } catch (error) {
    this.handleError(error)
  }
}
```

## 🚀 部署上线

### 📱 小程序发布流程
1. **代码检查**: 确保代码质量和规范性
2. **功能测试**: 完整测试所有功能模块
3. **性能优化**: 检查加载速度和内存使用
4. **提交审核**: 通过微信开发者工具提交
5. **发布上线**: 审核通过后发布到线上

### 🔧 生产环境配置
```javascript
// 生产环境配置检查清单
const prodConfig = {
  apiBaseURL: 'https://api.xiaoyaojing.com',  // 生产API地址
  appId: 'wx1234567890abcdef',                // 正式AppID
  version: '1.0.0',                           // 版本号
  debug: false,                               // 关闭调试模式
  analytics: true                             // 开启数据统计
}
```

---

**开始开发**: 🚀 **立即开始**  
**遇到问题**: 📚 **查阅文档**  
**需要帮助**: 💬 **联系团队**
