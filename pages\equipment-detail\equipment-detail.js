// pages/equipment-detail/equipment-detail.js
const app = getApp()
const auth = require('../../utils/auth.js')
const { mockEquipment } = require('../../utils/mockData.js')

Page({

  /**
   * 页面的初始数据
   */
  data: {
    // 设备详情
    equipmentDetail: null,
    specsArray: [],
    rentalNotices: [
      '1. 租赁前需完成实名认证',
      '2. 设备使用需遵守当地法规',
      '3. 禁止在禁飞区域使用',
      '4. 损坏设备需照价赔偿',
      '5. 归还时需检查设备完整性'
    ],
    
    // 页面状态
    loading: true,
    equipmentId: null,
    isFavorite: false,
    accessories: [
      '无人机主体',
      '遥控器',
      '电池×3',
      '充电器',
      '螺旋桨×4',
      '携带箱'
    ]
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    console.log('设备详情页面加载，设备ID：', options.id)
    this.setData({ equipmentId: options.id })
    this.checkAuth()
    this.loadEquipmentDetail(options.id)
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {

  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {

  },

  /**
   * 检查认证状态
   */
  checkAuth() {
    if (!auth.checkLoginStatus()) {
      auth.redirectToLogin(`/pages/equipment-detail/equipment-detail?id=${this.data.equipmentId}`)
      return false
    }
    return true
  },

  /**
   * 加载设备详情
   */
  async loadEquipmentDetail(equipmentId) {
    if (!this.checkAuth()) return

    this.setData({ loading: true })

    try {
      // 从mock数据中查找设备
      const equipment = mockEquipment.find(item => item.id === equipmentId)
      
      if (!equipment) {
        throw new Error('设备不存在')
      }

      // 转换规格数据为数组格式
      const specsArray = Object.entries(equipment.specs || {}).map(([key, value]) => ({
        key,
        label: this.getSpecLabel(key),
        value
      }))

      this.setData({
        equipmentDetail: equipment,
        specsArray
      })

      console.log('设备详情加载完成：', equipment.name)
    } catch (error) {
      console.error('加载设备详情失败：', error)
      wx.showToast({
        title: '加载失败',
        icon: 'error'
      })
      
      // 返回上一页
      setTimeout(() => {
        wx.navigateBack()
      }, 1500)
    } finally {
      this.setData({ loading: false })
    }
  },

  /**
   * 获取规格标签
   */
  getSpecLabel(key) {
    const labelMap = {
      weight: '重量',
      maxFlightTime: '续航时间',
      maxSpeed: '最大速度',
      cameraResolution: '摄像头分辨率',
      transmission: '图传距离',
      obstacle: '避障功能'
    }
    return labelMap[key] || key
  },

  /**
   * 开始租赁
   */
  startRental() {
    if (!this.checkAuth()) return

    const equipment = this.data.equipmentDetail
    if (!equipment) {
      wx.showToast({
        title: '设备信息错误',
        icon: 'error'
      })
      return
    }

    if (!equipment.available) {
      wx.showToast({
        title: '设备不可用',
        icon: 'none'
      })
      return
    }

    // 跳转到地点选择页面
    wx.navigateTo({
      url: `/pages/location/location?equipmentId=${equipment.id}&equipmentName=${encodeURIComponent(equipment.name)}`
    })
  },

  /**
   * 添加到收藏
   */
  addToFavorites() {
    if (!this.checkAuth()) return

    wx.showToast({
      title: '已收藏',
      icon: 'success'
    })
    
    console.log('添加收藏：', this.data.equipmentDetail?.name)
  },

  /**
   * 联系客服
   */
  contactService() {
    wx.showModal({
      title: '联系客服',
      content: '如有疑问请联系客服\n客服电话：400-123-4567\n微信客服：xiaoyaojing001',
      showCancel: false
    })
  },

  /**
   * 返回上一页
   */
  goBack() {
    wx.navigateBack()
  },

  /**
   * 切换收藏状态
   */
  toggleFavorite() {
    this.setData({
      isFavorite: !this.data.isFavorite
    })

    wx.showToast({
      title: this.data.isFavorite ? '已收藏' : '已取消收藏',
      icon: 'success'
    })
  }
})