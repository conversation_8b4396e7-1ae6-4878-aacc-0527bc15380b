# 逍遥境无人机租赁微信小程序开发文档

## 项目概述

**项目名称**：逍遥境无人机租赁平台  
**平台类型**：微信小程序  
**目标用户**：航拍爱好者、摄影师、旅游用户  
**核心功能**：无人机设备租赁、实时操控、航拍作品管理

## 技术架构

### 开发框架
- **前端框架**：微信小程序原生开发
- **UI组件库**：WeUI + 自定义组件
- **状态管理**：全局数据管理
- **地图服务**：腾讯位置服务API
- **支付系统**：微信支付
- **实时通信**：WebSocket（无人机控制）

### 技术栈转换对照表

| HTML原型 | 微信小程序 | 说明 |
|---------|-----------|------|
| HTML + CSS | WXML + WXSS | 页面结构和样式 |
| JavaScript | JavaScript | 业务逻辑（保持一致） |
| Tailwind CSS | 自定义WXSS类 | 样式系统转换 |
| FontAwesome | 小程序图标库 | 图标系统 |
| iframe导航 | 页面路由系统 | 导航机制 |

## 页面架构设计

### 目录结构
```
miniprogram/
├── pages/                 # 页面目录
│   ├── login/            # 登录授权页
│   ├── home/             # 首页
│   ├── equipment/        # 设备相关页面
│   │   ├── list/         # 设备列表
│   │   └── detail/       # 设备详情
│   ├── location/         # 地点选择页
│   ├── order/            # 订单相关页面
│   │   ├── confirm/      # 下单确认
│   │   └── list/         # 订单列表
│   ├── control/          # 无人机操控页
│   ├── profile/          # 个人中心
│   ├── gallery/          # 作品集
│   └── recharge/         # 充值页面
├── components/           # 自定义组件
├── utils/               # 工具函数
├── services/            # API服务
└── app.js              # 小程序入口
```

## 详细页面说明

### 1. 登录授权页 (pages/login/)

**对应HTML文件**：`login.html`

**功能需求**：
- 微信一键登录
- 手机号授权登录
- 用户协议确认
- 登录状态持久化

**技术实现**：
```javascript
// login.js
Page({
  data: {
    canIUseGetUserProfile: wx.canIUse('getUserProfile')
  },
  
  // 微信登录
  wechatLogin() {
    wx.getUserProfile({
      desc: '用于完善会员资料',
      success: (res) => {
        // 调用登录接口
        this.doLogin(res.userInfo)
      }
    })
  },
  
  // 手机号登录
  getPhoneNumber(e) {
    if (e.detail.code) {
      // 获取手机号码
      this.requestPhoneNumber(e.detail.code)
    }
  }
})
```

**页面配置**：
```json
{
  "navigationStyle": "custom",
  "backgroundColor": "#f8fafc"
}
```

### 2. 首页 (pages/home/<USER>

**对应HTML文件**：`home.html`

**功能需求**：
- 热门设备推荐
- 热门拍摄地点展示
- 快速操作入口
- 搜索功能
- 底部Tab导航

**数据结构**：
```javascript
data: {
  userInfo: {},
  hotEquipments: [],
  hotLocations: [],
  currentTab: 0
}
```

**API接口**：
- `GET /api/equipments/hot` - 获取热门设备
- `GET /api/locations/nearby` - 获取附近地点
- `GET /api/user/info` - 获取用户信息

### 3. 设备列表页 (pages/equipment/list/)

**对应HTML文件**：`equipment.html`

**功能需求**：
- 设备分类筛选
- 价格排序
- 设备状态显示
- 分页加载
- 跳转详情页

**筛选逻辑**：
```javascript
filterEquipments(category, priceRange) {
  let filtered = this.data.allEquipments
  
  if (category !== 'all') {
    filtered = filtered.filter(eq => eq.category === category)
  }
  
  if (priceRange) {
    filtered = filtered.filter(eq => 
      eq.price >= priceRange.min && eq.price <= priceRange.max
    )
  }
  
  this.setData({ equipments: filtered })
}
```

### 4. 设备详情页 (pages/equipment/detail/)

**对应HTML文件**：`equipment-detail.html`

**功能需求**：
- 设备图片轮播
- 技术规格展示
- 用户评价列表
- 收藏功能
- 立即租用按钮

**页面传参**：
```javascript
// 跳转时传递设备ID
wx.navigateTo({
  url: `/pages/equipment/detail/detail?id=${equipmentId}`
})
```

### 5. 地点选择页 (pages/location/)

**对应HTML文件**：`location.html`

**功能需求**：
- 腾讯地图集成
- 附近拍摄点标记
- 地点详情展示
- GPS定位
- 地点确认选择

**地图配置**：
```javascript
// 使用腾讯位置服务
const QQMapWX = require('../../utils/qqmap-wx-jssdk.js')
const qqmapsdk = new QQMapWX({
  key: 'YOUR_KEY_HERE'
})

// 获取附近地点
qqmapsdk.search({
  keyword: '拍摄地点',
  location: {
    latitude: this.data.latitude,
    longitude: this.data.longitude
  },
  success: (res) => {
    this.setData({ locations: res.data })
  }
})
```

### 6. 下单确认页 (pages/order/confirm/)

**对应HTML文件**：`order-confirm.html`

**功能需求**：
- 订单信息确认
- 时间段选择
- 租赁时长设置
- 费用计算
- 余额检查
- 支付流程

**费用计算逻辑**：
```javascript
calculateTotal() {
  const { equipment, duration, serviceFee, insurance } = this.data
  const equipmentFee = equipment.price * duration
  const total = equipmentFee + serviceFee + insurance
  
  this.setData({ 
    equipmentFee,
    totalFee: total
  })
  
  // 检查余额
  this.checkBalance(total)
}
```

### 7. 无人机操控页 (pages/control/)

**对应HTML文件**：`drone-control.html`

**功能需求**：
- 实时视频流显示
- 虚拟摇杆控制
- 拍照录像功能
- 飞行参数显示
- HUD界面
- 返航功能

**WebSocket连接**：
```javascript
// 建立WebSocket连接
connectDrone() {
  this.socketTask = wx.connectSocket({
    url: 'wss://api.xiaoyaojing.com/drone/control',
    header: {
      'Authorization': wx.getStorageSync('token')
    }
  })
  
  this.socketTask.onMessage((res) => {
    const data = JSON.parse(res.data)
    this.updateFlightData(data)
  })
}

// 发送控制指令
sendControlCommand(command) {
  this.socketTask.send({
    data: JSON.stringify(command)
  })
}
```

### 8. 订单列表页 (pages/order/list/)

**对应HTML文件**：`orders.html`

**功能需求**：
- 订单状态筛选
- 订单详情展示
- 继续操控功能
- 订单评价
- 重新预订

**状态管理**：
```javascript
// 订单状态枚举
const ORDER_STATUS = {
  PENDING: 'pending',
  ACTIVE: 'active', 
  COMPLETED: 'completed',
  CANCELLED: 'cancelled'
}
```

### 9. 个人中心页 (pages/profile/)

**对应HTML文件**：`profile.html`

**功能需求**：
- 用户信息展示
- 余额管理
- 统计数据
- 功能菜单
- 设置选项

**用户数据结构**：
```javascript
userProfile: {
  id: '',
  nickname: '',
  avatar: '',
  level: '',
  balance: 0,
  flightTime: 0,
  totalOrders: 0,
  totalWorks: 0,
  rating: 0
}
```

### 10. 作品集页 (pages/gallery/)

**对应HTML文件**：`gallery.html`

**功能需求**：
- 照片视频网格展示
- 类型筛选
- 全屏预览
- 下载分享
- 编辑删除

**媒体处理**：
```javascript
// 预览图片
previewImage(current, urls) {
  wx.previewImage({
    current,
    urls
  })
}

// 保存到相册
saveToAlbum(url) {
  wx.downloadFile({
    url,
    success: (res) => {
      wx.saveImageToPhotosAlbum({
        filePath: res.tempFilePath
      })
    }
  })
}
```

### 11. 充值页面 (pages/recharge/)

**对应HTML文件**：`recharge.html`

**功能需求**：
- 充值金额选择
- 支付方式选择
- 优惠活动展示
- 微信支付集成
- 充值记录

**微信支付实现**：
```javascript
// 发起支付
requestPayment(orderInfo) {
  wx.requestPayment({
    timeStamp: orderInfo.timeStamp,
    nonceStr: orderInfo.nonceStr,
    package: orderInfo.package,
    signType: 'RSA',
    paySign: orderInfo.paySign,
    success: (res) => {
      this.handlePaymentSuccess()
    },
    fail: (res) => {
      this.handlePaymentFail()
    }
  })
}
```

## 业务逻辑设计

### 用户状态管理

```javascript
// app.js 全局状态
App({
  globalData: {
    userInfo: null,
    isLoggedIn: false,
    currentLocation: null,
    token: ''
  },
  
  // 检查登录状态
  checkLoginStatus() {
    const token = wx.getStorageSync('token')
    if (token) {
      this.globalData.isLoggedIn = true
      this.globalData.token = token
      return true
    }
    return false
  },
  
  // 用户登录
  login(userInfo) {
    this.globalData.userInfo = userInfo
    this.globalData.isLoggedIn = true
    wx.setStorageSync('userInfo', userInfo)
  },
  
  // 用户登出
  logout() {
    this.globalData.userInfo = null
    this.globalData.isLoggedIn = false
    wx.removeStorageSync('userInfo')
    wx.removeStorageSync('token')
  }
})
```

### 路由守卫机制

```javascript
// utils/auth.js
function checkAuth() {
  const app = getApp()
  if (!app.globalData.isLoggedIn) {
    wx.redirectTo({
      url: '/pages/login/login'
    })
    return false
  }
  return true
}

// 在需要登录的页面onLoad中调用
onLoad() {
  if (!checkAuth()) return
  // 页面逻辑
}
```

### 余额检查逻辑

```javascript
// 余额检查
function checkBalance(requiredAmount) {
  const userInfo = wx.getStorageSync('userInfo')
  if (userInfo.balance < requiredAmount) {
    wx.showModal({
      title: '余额不足',
      content: '当前余额不足，是否前往充值？',
      success: (res) => {
        if (res.confirm) {
          wx.navigateTo({
            url: '/pages/recharge/recharge'
          })
        }
      }
    })
    return false
  }
  return true
}
```

## API接口设计

### 基础配置

```javascript
// config.js
const config = {
  baseURL: 'https://api.xiaoyaojing.com',
  timeout: 10000,
  version: 'v1'
}

// request.js - 封装网络请求
function request(options) {
  return new Promise((resolve, reject) => {
    wx.request({
      url: `${config.baseURL}/${config.version}${options.url}`,
      method: options.method || 'GET',
      data: options.data || {},
      header: {
        'Authorization': `Bearer ${wx.getStorageSync('token')}`,
        'Content-Type': 'application/json',
        ...options.header
      },
      success: (res) => {
        if (res.statusCode === 200) {
          resolve(res.data)
        } else {
          reject(res)
        }
      },
      fail: reject
    })
  })
}
```

### 主要API接口

| 模块 | 接口 | 方法 | 描述 |
|-----|------|------|------|
| 用户 | `/auth/login` | POST | 用户登录 |
| 用户 | `/auth/phone` | POST | 手机号登录 |
| 用户 | `/user/profile` | GET | 获取用户信息 |
| 设备 | `/equipments` | GET | 获取设备列表 |
| 设备 | `/equipments/:id` | GET | 获取设备详情 |
| 地点 | `/locations/nearby` | GET | 获取附近地点 |
| 订单 | `/orders` | POST | 创建订单 |
| 订单 | `/orders/:id` | GET | 获取订单详情 |
| 订单 | `/orders/user` | GET | 获取用户订单 |
| 支付 | `/payment/create` | POST | 创建支付订单 |
| 操控 | `/drone/connect` | WebSocket | 无人机连接 |
| 媒体 | `/media/upload` | POST | 上传媒体文件 |
| 媒体 | `/media/user` | GET | 获取用户作品 |

## 数据存储设计

### 本地存储

```javascript
// 用户信息
wx.setStorageSync('userInfo', {
  id: '',
  nickname: '',
  avatar: '',
  phone: '',
  balance: 0
})

// 认证信息
wx.setStorageSync('token', 'jwt_token_here')

// 位置信息
wx.setStorageSync('location', {
  latitude: 0,
  longitude: 0,
  address: ''
})

// 应用设置
wx.setStorageSync('settings', {
  notifications: true,
  autoLocation: true,
  theme: 'light'
})
```

### 云数据库设计

```javascript
// 用户表 (users)
{
  _id: ObjectId,
  openid: String,
  unionid: String,
  nickname: String,
  avatar: String,
  phone: String,
  balance: Number,
  level: String,
  createdAt: Date,
  updatedAt: Date
}

// 设备表 (equipments)
{
  _id: ObjectId,
  name: String,
  category: String,
  price: Number,
  specs: Object,
  images: Array,
  status: String,
  location: Object,
  createdAt: Date
}

// 订单表 (orders)
{
  _id: ObjectId,
  userId: ObjectId,
  equipmentId: ObjectId,
  locationId: ObjectId,
  startTime: Date,
  duration: Number,
  totalAmount: Number,
  status: String,
  createdAt: Date
}
```

## 部署配置

### 小程序配置 (app.json)

```json
{
  "pages": [
    "pages/login/login",
    "pages/home/<USER>",
    "pages/equipment/list/list",
    "pages/equipment/detail/detail",
    "pages/location/location",
    "pages/order/confirm/confirm",
    "pages/control/control",
    "pages/order/list/list",
    "pages/profile/profile",
    "pages/gallery/gallery",
    "pages/recharge/recharge"
  ],
  "tabBar": {
    "color": "#9ca3af",
    "selectedColor": "#1f2937",
    "backgroundColor": "#ffffff",
    "borderStyle": "white",
    "list": [
      {
        "pagePath": "pages/home/<USER>",
        "text": "首页",
        "iconPath": "images/home.png",
        "selectedIconPath": "images/home-active.png"
      },
      {
        "pagePath": "pages/equipment/list/list",
        "text": "设备",
        "iconPath": "images/equipment.png",
        "selectedIconPath": "images/equipment-active.png"
      },
      {
        "pagePath": "pages/order/list/list",
        "text": "订单",
        "iconPath": "images/orders.png",
        "selectedIconPath": "images/orders-active.png"
      },
      {
        "pagePath": "pages/profile/profile",
        "text": "我的",
        "iconPath": "images/profile.png",
        "selectedIconPath": "images/profile-active.png"
      }
    ]
  },
  "permission": {
    "scope.userLocation": {
      "desc": "您的位置信息将用于小程序位置接口的效果展示"
    },
    "scope.camera": {
      "desc": "您的摄像头将用于扫描二维码"
    },
    "scope.album": {
      "desc": "您的相册权限将用于保存图片"
    }
  },
  "requiredBackgroundModes": ["location"],
  "style": "v2",
  "sitemapLocation": "sitemap.json"
}
```

### 权限配置

```javascript
// 获取位置权限
wx.getLocation({
  type: 'wgs84',
  success: (res) => {
    // 位置获取成功
  },
  fail: () => {
    wx.showModal({
      title: '定位权限',
      content: '需要获取您的地理位置，请确认授权',
      success: (res) => {
        if (res.confirm) {
          wx.openSetting()
        }
      }
    })
  }
})
```

## 性能优化

### 图片优化
```javascript
// 图片懒加载
Component({
  data: {
    imageSrc: '',
    loaded: false
  },
  
  lifetimes: {
    attached() {
      // 使用 Intersection Observer 实现懒加载
      this.createIntersectionObserver()
        .relativeToViewport()
        .observe('.lazy-image', (res) => {
          if (res.intersectionRatio > 0) {
            this.setData({
              imageSrc: this.data.realSrc,
              loaded: true
            })
          }
        })
    }
  }
})
```

### 数据缓存
```javascript
// 数据缓存策略
function getCachedData(key, apiFunction) {
  const cached = wx.getStorageSync(key)
  const now = Date.now()
  
  if (cached && now - cached.timestamp < 5 * 60 * 1000) {
    return Promise.resolve(cached.data)
  }
  
  return apiFunction().then(data => {
    wx.setStorageSync(key, {
      data,
      timestamp: now
    })
    return data
  })
}
```

## 测试策略

### 单元测试
```javascript
// 测试工具函数
const { formatPrice, validatePhoneNumber } = require('../../utils/util')

describe('工具函数测试', () => {
  test('价格格式化', () => {
    expect(formatPrice(100)).toBe('¥100.00')
    expect(formatPrice(99.9)).toBe('¥99.90')
  })
  
  test('手机号验证', () => {
    expect(validatePhoneNumber('13800138000')).toBe(true)
    expect(validatePhoneNumber('138001380')).toBe(false)
  })
})
```

### 集成测试
- 登录流程测试
- 下单流程测试
- 支付流程测试
- 设备控制测试

## 上线清单

### 开发阶段
- [ ] 完成所有页面开发
- [ ] API接口联调
- [ ] 数据库设计实现
- [ ] 单元测试覆盖
- [ ] 集成测试通过

### 提审阶段
- [ ] 小程序代码审核
- [ ] 微信支付接入审核
- [ ] 位置服务权限审核
- [ ] 相机权限审核
- [ ] 内容安全审核

### 上线阶段
- [ ] 生产环境部署
- [ ] CDN配置
- [ ] 监控系统接入
- [ ] 用户反馈收集
- [ ] 数据统计分析

## 运维监控

### 错误监控
```javascript
// 全局错误监控
App({
  onError(error) {
    // 上报错误信息
    wx.request({
      url: 'https://api.xiaoyaojing.com/v1/logs/error',
      method: 'POST',
      data: {
        error: error,
        userInfo: this.globalData.userInfo,
        timestamp: Date.now()
      }
    })
  }
})
```

### 性能监控
```javascript
// 页面性能监控
Page({
  onLoad() {
    this.startTime = Date.now()
  },
  
  onReady() {
    const loadTime = Date.now() - this.startTime
    // 上报页面加载时间
    this.reportPerformance({
      page: this.route,
      loadTime
    })
  }
})
```

## 后续优化方向

1. **AI功能集成**
   - 智能拍摄建议
   - 自动构图优化
   - 智能剪辑功能

2. **社交功能**
   - 作品分享社区
   - 用户互动评论
   - 航拍技巧交流

3. **增值服务**
   - 专业摄影师服务
   - 定制拍摄方案
   - 航拍教学课程

4. **技术升级**
   - 5G网络优化
   - AR实时预览
   - 云端智能处理

---

**文档版本**：v1.0  
**最后更新**：2024年12月  
**维护团队**：逍遥境技术团队 