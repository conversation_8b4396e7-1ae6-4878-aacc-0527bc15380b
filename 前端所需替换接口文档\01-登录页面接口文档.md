# 登录页面接口文档

## 页面概述
登录页面是用户进入小程序的入口，提供微信一键登录和手机号授权登录两种方式。

## 当前实现分析

### 页面文件位置
- `pages/login/login.wxml` - 页面结构
- `pages/login/login.js` - 页面逻辑
- `pages/login/login.wxss` - 页面样式
- `utils/auth.js` - 认证工具模块
- `utils/mockData.js` - 模拟用户数据

### 当前登录流程
1. 用户选择登录方式（微信登录/手机号授权）
2. 检查用户协议同意状态
3. 获取微信登录code
4. 调用模拟登录API
5. 处理登录成功，保存用户信息
6. 跳转到目标页面或首页

## 需要替换的接口

### 1. 微信登录接口

#### 接口信息
- **接口名称**: 微信登录
- **请求方法**: POST
- **接口路径**: `/api/auth/wechat-login`
- **当前模拟位置**: `pages/login/login.js` 第293-322行 `callLoginAPI` 方法

#### 请求参数
```json
{
  "type": "wechat",
  "code": "微信登录code",
  "userInfo": {
    "nickName": "用户昵称",
    "avatarUrl": "头像URL",
    "gender": 0,
    "country": "国家",
    "province": "省份", 
    "city": "城市",
    "language": "zh_CN"
  }
}
```

#### 响应数据
```json
{
  "code": 200,
  "message": "登录成功",
  "data": {
    "id": "用户ID",
    "nickname": "用户昵称",
    "avatar": "头像URL",
    "phone": "手机号",
    "balance": 299.50,
    "level": "VIP",
    "registerTime": "2024-01-15",
    "totalFlightTime": 120,
    "totalOrders": 25,
    "creditScore": 850,
    "token": "JWT令牌",
    "loginType": "wechat",
    "loginTime": "2024-01-27T10:00:00.000Z"
  }
}
```

### 2. 手机号授权登录接口

#### 接口信息
- **接口名称**: 手机号授权登录
- **请求方法**: POST
- **接口路径**: `/api/auth/phone-login`
- **当前模拟位置**: `pages/login/login.js` 第293-322行 `callLoginAPI` 方法

#### 请求参数
```json
{
  "type": "phone_auth",
  "code": "微信登录code",
  "encryptedData": "加密数据",
  "iv": "初始向量"
}
```

#### 响应数据
```json
{
  "code": 200,
  "message": "登录成功",
  "data": {
    "id": "用户ID",
    "nickname": "用户昵称",
    "avatar": "头像URL",
    "phone": "138****8888",
    "balance": 299.50,
    "level": "VIP",
    "registerTime": "2024-01-15",
    "totalFlightTime": 120,
    "totalOrders": 25,
    "creditScore": 850,
    "token": "JWT令牌",
    "loginType": "phone_auth",
    "loginTime": "2024-01-27T10:00:00.000Z"
  }
}
```

### 3. 登录状态验证接口

#### 接口信息
- **接口名称**: 验证登录状态
- **请求方法**: GET
- **接口路径**: `/api/auth/verify-token`
- **当前模拟位置**: `utils/auth.js` 第12-48行 `checkLoginStatus` 方法

#### 请求头
```
Authorization: Bearer {token}
```

#### 响应数据
```json
{
  "code": 200,
  "message": "token有效",
  "data": {
    "valid": true,
    "userInfo": {
      "id": "用户ID",
      "nickname": "用户昵称",
      "avatar": "头像URL",
      "phone": "手机号",
      "balance": 299.50,
      "level": "VIP"
    }
  }
}
```

## 替换指导

### 1. 修改登录API调用
**文件**: `pages/login/login.js`
**位置**: 第293-322行 `callLoginAPI` 方法

**当前代码**:
```javascript
async callLoginAPI(params) {
  console.log('登录API参数：', params)
  
  // 模拟API延迟
  await new Promise(resolve => setTimeout(resolve, 1000))
  
  // 返回模拟用户数据
  const userData = {
    ...mockUser,
    loginType: params.type,
    loginTime: new Date().toISOString(),
    token: 'mock_token_' + Date.now()
  }
  
  return userData
}
```

**替换为**:
```javascript
async callLoginAPI(params) {
  console.log('登录API参数：', params)
  
  try {
    const response = await wx.request({
      url: `${app.globalData.apiBaseUrl}/api/auth/${params.type === 'wechat' ? 'wechat-login' : 'phone-login'}`,
      method: 'POST',
      data: params,
      header: {
        'Content-Type': 'application/json'
      }
    })
    
    if (response.data.code === 200) {
      return response.data.data
    } else {
      throw new Error(response.data.message || '登录失败')
    }
  } catch (error) {
    console.error('登录API调用失败：', error)
    throw error
  }
}
```

### 2. 修改登录状态检查
**文件**: `utils/auth.js`
**位置**: 第12-48行 `checkLoginStatus` 方法

**需要添加的token验证逻辑**:
```javascript
// 在现有token过期检查后添加服务器验证
if (!tokenExpired) {
  try {
    const response = await wx.request({
      url: `${app.globalData.apiBaseUrl}/api/auth/verify-token`,
      method: 'GET',
      header: {
        'Authorization': `Bearer ${token}`
      }
    })
    
    if (response.data.code !== 200 || !response.data.data.valid) {
      console.log('Token验证失败，清除登录状态')
      clearUserInfo()
      return false
    }
  } catch (error) {
    console.error('Token验证请求失败：', error)
    clearUserInfo()
    return false
  }
}
```

### 3. 配置API基础URL
**文件**: `app.js`
**需要添加**:
```javascript
globalData: {
  apiBaseUrl: 'https://your-api-domain.com', // 替换为实际API域名
  // ... 其他全局数据
}
```

## 错误处理

### 常见错误码
- `400`: 请求参数错误
- `401`: 认证失败
- `403`: 权限不足
- `500`: 服务器内部错误

### 错误处理示例
```javascript
catch (error) {
  console.error('登录失败：', error)
  
  let errorMessage = '登录失败，请重试'
  
  if (error.code === 400) {
    errorMessage = '请求参数错误'
  } else if (error.code === 401) {
    errorMessage = '认证失败，请重新登录'
  } else if (error.code === 500) {
    errorMessage = '服务器繁忙，请稍后重试'
  }
  
  wx.showToast({
    title: errorMessage,
    icon: 'error'
  })
}
```

## 注意事项

1. **微信小程序限制**: 手机号授权需要企业认证小程序
2. **Token管理**: 建议设置7天有效期，支持刷新机制
3. **用户信息**: 微信新版本不再提供用户头像昵称，需要使用头像昵称填写组件
4. **安全性**: 手机号解密必须在后端进行，前端只传递加密数据
5. **错误处理**: 需要处理网络异常、服务器错误等各种情况

## 测试建议

1. 测试微信登录流程
2. 测试手机号授权登录流程  
3. 测试登录状态验证
4. 测试token过期处理
5. 测试网络异常情况
6. 测试各种错误码处理
