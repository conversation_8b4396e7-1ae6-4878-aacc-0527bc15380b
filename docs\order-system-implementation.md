# 订单系统实现说明

## 功能概述

已实现下单后扣除余额和添加订单的完整逻辑，为后端接口预留了调用结构。

## 实现的功能

### 1. 订单创建流程
- ✅ 用户选择设备和地点
- ✅ 选择租赁时间和时长
- ✅ 计算总费用（设备费用 + 服务费 + 保险费）
- ✅ 检查用户余额是否足够
- ✅ 扣除用户余额
- ✅ 创建订单记录
- ✅ 本地存储订单信息
- ✅ 跳转到控制页面

### 2. 余额管理
- ✅ 实时检查用户余额
- ✅ 余额不足时提示充值
- ✅ 下单成功后扣除相应金额
- ✅ 更新用户信息显示
- ✅ 避免浮点数精度问题

### 3. 订单管理
- ✅ 本地存储用户订单
- ✅ 订单列表显示（合并用户订单和模拟数据）
- ✅ 订单状态管理
- ✅ 订单详情查询

## 文件结构

### 核心文件
- `utils/orderManager.js` - 订单管理工具模块
- `pages/order-confirm/order-confirm.js` - 订单确认页面
- `pages/orders/orders.js` - 订单列表页面
- `utils/auth.js` - 用户认证和信息管理

### 数据流程
1. **订单确认页面** → 收集订单信息
2. **订单管理器** → 处理订单创建和余额扣除
3. **本地存储** → 保存订单和用户信息
4. **订单列表** → 显示用户订单

## 后端接口预留

### 1. 创建订单接口
```javascript
// 位置：utils/orderManager.js - callCreateOrderAPI()
const response = await wx.request({
  url: 'https://api.yourdomain.com/orders',
  method: 'POST',
  data: orderData,
  header: {
    'Content-Type': 'application/json',
    'Authorization': `Bearer ${wx.getStorageSync('token')}`
  }
})
```

### 2. 余额扣除接口
```javascript
// 位置：utils/orderManager.js - deductBalance()
const response = await wx.request({
  url: 'https://api.yourdomain.com/users/balance/deduct',
  method: 'POST',
  data: { amount },
  header: {
    'Content-Type': 'application/json',
    'Authorization': `Bearer ${wx.getStorageSync('token')}`
  }
})
```

### 3. 订单查询接口
```javascript
// 可在 pages/orders/orders.js 中添加
const response = await wx.request({
  url: 'https://api.yourdomain.com/orders',
  method: 'GET',
  header: {
    'Authorization': `Bearer ${wx.getStorageSync('token')}`
  }
})
```

## 数据结构

### 订单对象结构
```javascript
{
  id: 'order_1234567890',
  equipmentId: 'dji_air3',
  equipmentName: 'DJI Air 3',
  locationId: 'location_001',
  locationName: '书圣故里',
  userId: 'user_001',
  status: 'ongoing',
  statusText: '进行中',
  orderTime: '2024-01-27 10:30:00',
  startTime: '2024-01-27 14:00:00',
  endTime: '2024-01-27 16:00:00',
  duration: 2,
  price: 80,
  totalAmount: 95,
  payment: {
    method: 'balance',
    methodText: '余额支付',
    transactionId: 'tx_1234567890'
  },
  equipment: {
    icon: 'icon-drone',
    battery: 100,
    specs: ['4K双摄', '46分钟续航']
  },
  location: {
    address: '绍兴市越城区蕺山街道',
    distance: 2.3
  },
  canControl: true,
  canCancel: true,
  canReview: false,
  remainingTime: '2小时',
  progress: 0
}
```

### 用户余额更新
```javascript
{
  id: 'user_001',
  nickname: '用户昵称',
  balance: 204.50, // 扣除后的新余额
  // ... 其他用户信息
}
```

## 测试流程

1. **选择设备** - 从设备页面选择无人机
2. **选择地点** - 从地点页面选择拍摄位置
3. **确认订单** - 选择时间、时长，确认费用
4. **提交订单** - 检查余额，扣除费用，创建订单
5. **查看订单** - 在订单列表中查看新创建的订单（状态：进行中）
6. **余额更新** - 在个人中心查看更新后的余额

### 订单操作流程

#### 进行中订单操作
1. **继续操控** - 点击后跳转到对应型号的无人机控制页面
   - 传递参数：`orderId` 和 `equipmentId`
   - 页面路径：`/pages/drone-control/drone-control`

2. **提前结束** - 点击后弹出确认框
   - 确认框标题：`提前结束`
   - 确认框内容：`确认提前结束租赁？剩余时间将按比例退款`
   - 点击确认：订单状态变更为"已完成"，显示退款提示
   - 点击取消：保持原状态

#### 订单状态变更
- **进行中** → **已完成**：点击"提前结束"并确认
- **进行中** → **已取消**：点击"取消订单"并确认
- 状态变更会同步更新本地存储和页面显示

## 注意事项

1. **事务处理** - 实际项目中需要确保订单创建和余额扣除的原子性
2. **错误回滚** - 如果余额扣除失败，需要回滚已创建的订单
3. **网络异常** - 需要处理网络请求失败的情况
4. **数据同步** - 本地数据和服务器数据的同步机制
5. **安全验证** - 服务器端需要验证订单金额和用户权限

## 后续集成步骤

1. 替换模拟API调用为真实的后端接口
2. 添加网络错误处理和重试机制
3. 实现订单状态同步
4. 添加支付方式选择（余额、微信支付等）
5. 完善订单取消和退款逻辑
