<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>首页 - 逍遥境</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body { height: 852px; overflow: hidden; }
        .status-bar {
            background: black;
            color: white;
            height: 47px;
            font-size: 14px;
            font-weight: 600;
        }
        .content-area {
            height: calc(852px - 47px - 70px);
            overflow-y: auto;
        }
        .tab-bar {
            height: 70px;
            border-top: 1px solid #e5e7eb;
        }
        .tab-item.active {
            color: #1f2937;
        }
        .tab-item {
            color: #9ca3af;
        }
        .drone-card {
            transition: all 0.3s ease;
        }
        .drone-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.1);
        }
    </style>
</head>
<body class="bg-gray-50">
    <!-- iOS 状态栏 -->
    <div class="status-bar flex justify-between items-center px-6">
        <div class="flex items-center space-x-1">
            <span>9:41</span>
        </div>
        <div class="flex items-center space-x-1">
            <i class="fas fa-signal text-xs"></i>
            <i class="fas fa-wifi text-xs"></i>
            <div class="w-6 h-3 border border-white rounded-sm">
                <div class="w-4 h-1 bg-white rounded-sm mt-0.5 ml-0.5"></div>
            </div>
        </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="content-area">
        <!-- 顶部导航 -->
        <div class="bg-white px-6 py-4 shadow-sm">
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-2xl font-bold text-gray-800">逍遥境</h1>
                    <p class="text-sm text-gray-500">发现美好瞬间</p>
                </div>
                <div class="flex items-center space-x-4">
                    <i class="fas fa-search text-gray-400 text-xl"></i>
                    <i class="fas fa-bell text-gray-400 text-xl"></i>
                </div>
            </div>
        </div>

        <!-- 搜索栏 -->
        <div class="px-6 py-4">
            <div class="relative">
                <input type="text" placeholder="搜索设备或拍摄地点..." 
                       class="w-full bg-white rounded-2xl px-12 py-3 text-gray-600 shadow-sm border border-gray-100 focus:outline-none focus:ring-2 focus:ring-gray-300">
                <i class="fas fa-search absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400"></i>
            </div>
        </div>

        <!-- 热门推荐 -->
        <div class="px-6 mb-6">
            <h2 class="text-lg font-semibold text-gray-800 mb-4">热门推荐</h2>
            <div class="grid grid-cols-2 gap-4">
                <div class="drone-card bg-white rounded-2xl p-4 shadow-sm">
                    <img src="https://images.unsplash.com/photo-1473968512647-3e447244af8f?w=300&h=200&fit=crop" 
                         alt="DJI Air 3" class="w-full h-24 object-cover rounded-xl mb-3">
                    <h3 class="font-semibold text-gray-800 text-sm">DJI Air 3</h3>
                    <p class="text-xs text-gray-500 mb-2">4K双摄 · 46分钟续航</p>
                    <div class="flex items-center justify-between">
                        <span class="text-lg font-bold text-gray-800">¥80<span class="text-sm font-normal">/时</span></span>
                        <button onclick="viewEquipment('air3')" class="bg-gray-800 text-white px-3 py-1 rounded-lg text-xs">租用</button>
                    </div>
                </div>
                
                <div class="drone-card bg-white rounded-2xl p-4 shadow-sm">
                    <img src="https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=300&h=200&fit=crop" 
                         alt="DJI Mini 4 Pro" class="w-full h-24 object-cover rounded-xl mb-3">
                    <h3 class="font-semibold text-gray-800 text-sm">DJI Mini 4 Pro</h3>
                    <p class="text-xs text-gray-500 mb-2">4K HDR · 34分钟续航</p>
                    <div class="flex items-center justify-between">
                        <span class="text-lg font-bold text-gray-800">¥60<span class="text-sm font-normal">/时</span></span>
                        <button onclick="viewEquipment('mini4')" class="bg-gray-800 text-white px-3 py-1 rounded-lg text-xs">租用</button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 热门拍摄地点 -->
        <div class="px-6 mb-6">
            <h2 class="text-lg font-semibold text-gray-800 mb-4">热门拍摄地点</h2>
            <div class="space-y-3">
                <div class="bg-white rounded-2xl p-4 shadow-sm flex items-center space-x-4">
                    <img src="https://images.unsplash.com/photo-1508804185872-d7badad00f7d?w=300&h=200&fit=crop" 
                         alt="书圣故里" class="w-16 h-16 object-cover rounded-xl">
                    <div class="flex-1">
                        <h3 class="font-semibold text-gray-800">书圣故里</h3>
                        <p class="text-sm text-gray-500">距离您 2.3km · 古典园林</p>
                        <div class="flex items-center space-x-2 mt-1">
                            <div class="flex text-yellow-400 text-xs">
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                            </div>
                            <span class="text-xs text-gray-500">4.9 (328)</span>
                        </div>
                    </div>
                    <button onclick="selectLocation('shusheng')" class="text-gray-400">
                        <i class="fas fa-chevron-right"></i>
                    </button>
                </div>

                <div class="bg-white rounded-2xl p-4 shadow-sm flex items-center space-x-4">
                    <img src="https://images.unsplash.com/photo-1558618047-3c8c76ca7d13?w=300&h=200&fit=crop" 
                         alt="鲁迅故里" class="w-16 h-16 object-cover rounded-xl">
                    <div class="flex-1">
                        <h3 class="font-semibold text-gray-800">鲁迅故里</h3>
                        <p class="text-sm text-gray-500">距离您 3.1km · 历史古镇</p>
                        <div class="flex items-center space-x-2 mt-1">
                            <div class="flex text-yellow-400 text-xs">
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                            </div>
                            <span class="text-xs text-gray-500">4.8 (156)</span>
                        </div>
                    </div>
                    <button onclick="selectLocation('luxun')" class="text-gray-400">
                        <i class="fas fa-chevron-right"></i>
                    </button>
                </div>
            </div>
        </div>

        <!-- 快速操作 -->
        <div class="px-6 mb-6">
            <h2 class="text-lg font-semibold text-gray-800 mb-4">快速操作</h2>
            <div class="grid grid-cols-4 gap-4">
                <div class="text-center">
                    <div class="w-12 h-12 bg-gray-100 rounded-2xl flex items-center justify-center mx-auto mb-2">
                        <i class="fas fa-helicopter text-gray-600"></i>
                    </div>
                    <span class="text-xs text-gray-600">浏览设备</span>
                </div>
                <div class="text-center">
                    <div class="w-12 h-12 bg-gray-100 rounded-2xl flex items-center justify-center mx-auto mb-2">
                        <i class="fas fa-map-marker-alt text-gray-600"></i>
                    </div>
                    <span class="text-xs text-gray-600">选择地点</span>
                </div>
                <div class="text-center">
                    <div class="w-12 h-12 bg-gray-100 rounded-2xl flex items-center justify-center mx-auto mb-2">
                        <i class="fas fa-camera text-gray-600"></i>
                    </div>
                    <span class="text-xs text-gray-600">我的作品</span>
                </div>
                <div class="text-center">
                    <div class="w-12 h-12 bg-gray-100 rounded-2xl flex items-center justify-center mx-auto mb-2">
                        <i class="fas fa-headset text-gray-600"></i>
                    </div>
                    <span class="text-xs text-gray-600">客服支持</span>
                </div>
            </div>
        </div>
    </div>

    <!-- 底部导航栏 -->
    <div class="tab-bar bg-white flex items-center justify-around">
        <div class="tab-item active text-center">
            <i class="fas fa-home text-xl mb-1"></i>
            <div class="text-xs">首页</div>
        </div>
        <div class="tab-item text-center" onclick="navigateTo('equipment')">
            <i class="fas fa-helicopter text-xl mb-1"></i>
            <div class="text-xs">设备</div>
        </div>
        <div class="tab-item text-center" onclick="navigateTo('orders')">
            <i class="fas fa-file-alt text-xl mb-1"></i>
            <div class="text-xs">订单</div>
        </div>
        <div class="tab-item text-center" onclick="navigateTo('profile')">
            <i class="fas fa-user text-xl mb-1"></i>
            <div class="text-xs">我的</div>
        </div>
    </div>

    <script>
        function viewEquipment(id) {
            alert(`查看设备详情: ${id}`);
            // 模拟跳转到设备详情页
        }

        function selectLocation(id) {
            alert(`选择拍摄地点: ${id}`);
            // 模拟跳转到地点选择页
        }

        function navigateTo(page) {
            const tabs = document.querySelectorAll('.tab-item');
            tabs.forEach(tab => tab.classList.remove('active'));
            event.currentTarget.classList.add('active');
            
            // 模拟页面跳转
            setTimeout(() => {
                window.parent.postMessage({type: 'navigate', page: page}, '*');
            }, 200);
        }
    </script>
</body>
</html> 