<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>设备 - 逍遥境</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body { height: 852px; overflow: hidden; }
        .status-bar {
            background: black;
            color: white;
            height: 47px;
            font-size: 14px;
            font-weight: 600;
        }
        .content-area {
            height: calc(852px - 47px - 70px);
            overflow-y: auto;
        }
        .tab-bar {
            height: 70px;
            border-top: 1px solid #e5e7eb;
        }
        .tab-item.active {
            color: #1f2937;
        }
        .tab-item {
            color: #9ca3af;
        }
        .equipment-card {
            transition: all 0.3s ease;
        }
        .equipment-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 15px 35px rgba(0,0,0,0.1);
        }
        .filter-btn.active {
            background: #1f2937;
            color: white;
        }
    </style>
</head>
<body class="bg-gray-50">
    <!-- iOS 状态栏 -->
    <div class="status-bar flex justify-between items-center px-6">
        <div class="flex items-center space-x-1">
            <span>9:41</span>
        </div>
        <div class="flex items-center space-x-1">
            <i class="fas fa-signal text-xs"></i>
            <i class="fas fa-wifi text-xs"></i>
            <div class="w-6 h-3 border border-white rounded-sm">
                <div class="w-4 h-1 bg-white rounded-sm mt-0.5 ml-0.5"></div>
            </div>
        </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="content-area">
        <!-- 顶部导航 -->
        <div class="bg-white px-6 py-4 shadow-sm">
            <div class="flex items-center justify-between">
                <h1 class="text-2xl font-bold text-gray-800">设备租赁</h1>
                <div class="flex items-center space-x-4">
                    <i class="fas fa-filter text-gray-400 text-xl"></i>
                    <i class="fas fa-sort text-gray-400 text-xl"></i>
                </div>
            </div>
        </div>

        <!-- 筛选栏 -->
        <div class="px-6 py-4">
            <div class="flex space-x-3 overflow-x-auto">
                <button class="filter-btn active px-4 py-2 rounded-full text-sm whitespace-nowrap border border-gray-300">全部</button>
                <button class="filter-btn px-4 py-2 rounded-full text-sm whitespace-nowrap border border-gray-300 text-gray-600">专业级</button>
                <button class="filter-btn px-4 py-2 rounded-full text-sm whitespace-nowrap border border-gray-300 text-gray-600">入门级</button>
                <button class="filter-btn px-4 py-2 rounded-full text-sm whitespace-nowrap border border-gray-300 text-gray-600">便携式</button>
                <button class="filter-btn px-4 py-2 rounded-full text-sm whitespace-nowrap border border-gray-300 text-gray-600">¥50-80</button>
            </div>
        </div>

        <!-- 设备列表 -->
        <div class="px-6 space-y-4">
            <!-- DJI Air 3 -->
            <div class="equipment-card bg-white rounded-2xl p-5 shadow-sm">
                <div class="flex space-x-4">
                    <img src="https://images.unsplash.com/photo-1473968512647-3e447244af8f?w=300&h=200&fit=crop" 
                         alt="DJI Air 3" class="w-24 h-20 object-cover rounded-xl">
                    <div class="flex-1">
                        <div class="flex items-start justify-between mb-2">
                            <h3 class="text-lg font-semibold text-gray-800">DJI Air 3</h3>
                            <span class="bg-green-100 text-green-800 px-2 py-1 rounded-lg text-xs font-medium">可租用</span>
                        </div>
                        <p class="text-sm text-gray-500 mb-3">双主摄 · 4K/60fps HDR · 46分钟续航 · 障碍物感知</p>
                        <div class="flex items-center justify-between">
                            <div>
                                <span class="text-2xl font-bold text-gray-800">¥80</span>
                                <span class="text-sm text-gray-500">/小时</span>
                            </div>
                            <button onclick="viewDetail('air3')" class="bg-gray-800 hover:bg-gray-700 text-white px-6 py-2 rounded-xl font-medium transition-colors">
                                查看详情
                            </button>
                        </div>
                        <div class="flex items-center mt-3 space-x-4 text-xs text-gray-500">
                            <div class="flex items-center space-x-1">
                                <i class="fas fa-star text-yellow-400"></i>
                                <span>4.9 (127)</span>
                            </div>
                            <div class="flex items-center space-x-1">
                                <i class="fas fa-clock"></i>
                                <span>今日可租</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- DJI Mini 4 Pro -->
            <div class="equipment-card bg-white rounded-2xl p-5 shadow-sm">
                <div class="flex space-x-4">
                    <img src="https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=300&h=200&fit=crop" 
                         alt="DJI Mini 4 Pro" class="w-24 h-20 object-cover rounded-xl">
                    <div class="flex-1">
                        <div class="flex items-start justify-between mb-2">
                            <h3 class="text-lg font-semibold text-gray-800">DJI Mini 4 Pro</h3>
                            <span class="bg-green-100 text-green-800 px-2 py-1 rounded-lg text-xs font-medium">可租用</span>
                        </div>
                        <p class="text-sm text-gray-500 mb-3">4K HDR · 249g轻量 · 34分钟续航 · 智能跟随</p>
                        <div class="flex items-center justify-between">
                            <div>
                                <span class="text-2xl font-bold text-gray-800">¥60</span>
                                <span class="text-sm text-gray-500">/小时</span>
                            </div>
                            <button onclick="viewDetail('mini4')" class="bg-gray-800 hover:bg-gray-700 text-white px-6 py-2 rounded-xl font-medium transition-colors">
                                查看详情
                            </button>
                        </div>
                        <div class="flex items-center mt-3 space-x-4 text-xs text-gray-500">
                            <div class="flex items-center space-x-1">
                                <i class="fas fa-star text-yellow-400"></i>
                                <span>4.8 (89)</span>
                            </div>
                            <div class="flex items-center space-x-1">
                                <i class="fas fa-clock"></i>
                                <span>今日可租</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- DJI Mavic 3 -->
            <div class="equipment-card bg-white rounded-2xl p-5 shadow-sm">
                <div class="flex space-x-4">
                    <img src="https://images.unsplash.com/photo-1508614999368-9260051292e5?w=300&h=200&fit=crop" 
                         alt="DJI Mavic 3" class="w-24 h-20 object-cover rounded-xl">
                    <div class="flex-1">
                        <div class="flex items-start justify-between mb-2">
                            <h3 class="text-lg font-semibold text-gray-800">DJI Mavic 3</h3>
                            <span class="bg-yellow-100 text-yellow-800 px-2 py-1 rounded-lg text-xs font-medium">使用中</span>
                        </div>
                        <p class="text-sm text-gray-500 mb-3">哈苏相机 · 5.1K视频 · 46分钟续航 · 专业航拍</p>
                        <div class="flex items-center justify-between">
                            <div>
                                <span class="text-2xl font-bold text-gray-800">¥120</span>
                                <span class="text-sm text-gray-500">/小时</span>
                            </div>
                            <button disabled class="bg-gray-300 text-gray-500 px-6 py-2 rounded-xl font-medium cursor-not-allowed">
                                暂不可用
                            </button>
                        </div>
                        <div class="flex items-center mt-3 space-x-4 text-xs text-gray-500">
                            <div class="flex items-center space-x-1">
                                <i class="fas fa-star text-yellow-400"></i>
                                <span>4.9 (203)</span>
                            </div>
                            <div class="flex items-center space-x-1">
                                <i class="fas fa-clock"></i>
                                <span>预计17:30可用</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- DJI Phantom 4 Pro -->
            <div class="equipment-card bg-white rounded-2xl p-5 shadow-sm">
                <div class="flex space-x-4">
                    <img src="https://images.unsplash.com/photo-1520150480169-4b71bde47f98?w=300&h=200&fit=crop" 
                         alt="DJI Phantom 4 Pro" class="w-24 h-20 object-cover rounded-xl">
                    <div class="flex-1">
                        <div class="flex items-start justify-between mb-2">
                            <h3 class="text-lg font-semibold text-gray-800">DJI Phantom 4 Pro</h3>
                            <span class="bg-green-100 text-green-800 px-2 py-1 rounded-lg text-xs font-medium">可租用</span>
                        </div>
                        <p class="text-sm text-gray-500 mb-3">1英寸CMOS · 4K视频 · 30分钟续航 · 机械云台</p>
                        <div class="flex items-center justify-between">
                            <div>
                                <span class="text-2xl font-bold text-gray-800">¥100</span>
                                <span class="text-sm text-gray-500">/小时</span>
                            </div>
                            <button onclick="viewDetail('phantom4')" class="bg-gray-800 hover:bg-gray-700 text-white px-6 py-2 rounded-xl font-medium transition-colors">
                                查看详情
                            </button>
                        </div>
                        <div class="flex items-center mt-3 space-x-4 text-xs text-gray-500">
                            <div class="flex items-center space-x-1">
                                <i class="fas fa-star text-yellow-400"></i>
                                <span>4.7 (156)</span>
                            </div>
                            <div class="flex items-center space-x-1">
                                <i class="fas fa-clock"></i>
                                <span>今日可租</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 底部导航栏 -->
    <div class="tab-bar bg-white flex items-center justify-around">
        <div class="tab-item text-center" onclick="navigateTo('home')">
            <i class="fas fa-home text-xl mb-1"></i>
            <div class="text-xs">首页</div>
        </div>
        <div class="tab-item active text-center">
            <i class="fas fa-helicopter text-xl mb-1"></i>
            <div class="text-xs">设备</div>
        </div>
        <div class="tab-item text-center" onclick="navigateTo('orders')">
            <i class="fas fa-file-alt text-xl mb-1"></i>
            <div class="text-xs">订单</div>
        </div>
        <div class="tab-item text-center" onclick="navigateTo('profile')">
            <i class="fas fa-user text-xl mb-1"></i>
            <div class="text-xs">我的</div>
        </div>
    </div>

    <script>
        function viewDetail(equipmentId) {
            alert(`查看设备详情: ${equipmentId}`);
            // 模拟跳转到设备详情页
        }

        function navigateTo(page) {
            const tabs = document.querySelectorAll('.tab-item');
            tabs.forEach(tab => tab.classList.remove('active'));
            
            // 模拟页面跳转
            setTimeout(() => {
                window.parent.postMessage({type: 'navigate', page: page}, '*');
            }, 200);
        }

        // 筛选按钮交互
        document.querySelectorAll('.filter-btn').forEach(btn => {
            btn.addEventListener('click', function() {
                document.querySelectorAll('.filter-btn').forEach(b => b.classList.remove('active'));
                this.classList.add('active');
            });
        });
    </script>
</body>
</html> 