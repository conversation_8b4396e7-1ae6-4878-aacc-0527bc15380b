/**
 * 作品集管理工具
 * 处理拍摄作品的保存、管理和展示
 */

/**
 * 保存拍摄作品到作品集
 * @param {Object} workData - 作品数据
 * @returns {Promise<Object>} 保存结果
 */
async function saveWorkToGallery(workData) {
  try {
    // 生成作品ID
    const workId = 'work_' + Date.now()
    const now = new Date()
    
    // 构造作品对象
    const work = {
      id: workId,
      orderId: workData.orderId,
      equipmentId: workData.equipmentId,
      equipmentName: workData.equipmentName,
      locationId: workData.locationId,
      locationName: workData.locationName,
      type: workData.type, // 'photo' 或 'video'
      title: generateWorkTitle(workData),
      url: workData.url || generatePlaceholderUrl(workData.type),
      thumbnail: workData.thumbnail || generateThumbnailUrl(workData.type),
      createTime: now.toISOString().replace('T', ' ').substring(0, 19),
      date: formatDateShort(now),
      device: workData.equipmentName,
      resolution: workData.resolution || (workData.type === 'photo' ? '4000×3000' : '1920×1080'),
      fileSize: workData.fileSize || (workData.type === 'photo' ? '2.5MB' : '15.2MB'),
      duration: workData.duration || (workData.type === 'video' ? '00:30' : undefined),
      tags: generateTags(workData),
      likes: 0,
      shares: 0,
      views: 0,
      description: generateDescription(workData),
      selected: false
    }

    // TODO: 上传到后端
    /*
    const response = await wx.request({
      url: 'https://api.yourdomain.com/gallery',
      method: 'POST',
      data: work,
      header: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${wx.getStorageSync('token')}`
      }
    })

    if (response.statusCode !== 200) {
      throw new Error('作品保存失败')
    }
    */

    // 保存到本地存储
    const userGallery = getUserGallery()
    userGallery.unshift(work) // 新作品放在最前面
    wx.setStorageSync('userGallery', userGallery)

    console.log('作品保存成功：', work.title)
    return {
      success: true,
      work: work,
      message: '作品保存成功'
    }
  } catch (error) {
    console.error('保存作品失败：', error)
    return {
      success: false,
      message: error.message || '保存作品失败'
    }
  }
}

/**
 * 批量保存拍摄作品
 * @param {Object} batchData - 批量作品数据
 * @returns {Promise<Object>} 保存结果
 */
async function saveBatchWorks(batchData) {
  try {
    const { orderId, equipmentId, equipmentName, locationId, locationName, photoCount, videoCount } = batchData
    const works = []

    // 生成照片作品
    for (let i = 1; i <= photoCount; i++) {
      const photoWork = await saveWorkToGallery({
        orderId,
        equipmentId,
        equipmentName,
        locationId,
        locationName,
        type: 'photo',
        sequence: i
      })
      if (photoWork.success) {
        works.push(photoWork.work)
      }
    }

    // 生成视频作品
    for (let i = 1; i <= videoCount; i++) {
      const videoWork = await saveWorkToGallery({
        orderId,
        equipmentId,
        equipmentName,
        locationId,
        locationName,
        type: 'video',
        sequence: i
      })
      if (videoWork.success) {
        works.push(videoWork.work)
      }
    }

    console.log(`批量保存完成：${photoCount}张照片，${videoCount}个视频`)
    return {
      success: true,
      works: works,
      totalCount: works.length,
      message: `成功保存${works.length}个作品`
    }
  } catch (error) {
    console.error('批量保存作品失败：', error)
    return {
      success: false,
      message: error.message || '批量保存失败'
    }
  }
}

/**
 * 获取用户作品集
 * @returns {Array} 用户作品列表
 */
function getUserGallery() {
  try {
    return wx.getStorageSync('userGallery') || []
  } catch (error) {
    console.error('获取用户作品集失败：', error)
    return []
  }
}

/**
 * 根据订单ID获取作品
 * @param {string} orderId - 订单ID
 * @returns {Array} 订单相关作品列表
 */
function getWorksByOrderId(orderId) {
  try {
    const userGallery = getUserGallery()
    return userGallery.filter(work => work.orderId === orderId)
  } catch (error) {
    console.error('获取订单作品失败：', error)
    return []
  }
}

/**
 * 删除作品
 * @param {string} workId - 作品ID
 * @returns {Promise<Object>} 删除结果
 */
async function deleteWork(workId) {
  try {
    // TODO: 从后端删除
    /*
    const response = await wx.request({
      url: `https://api.yourdomain.com/gallery/${workId}`,
      method: 'DELETE',
      header: {
        'Authorization': `Bearer ${wx.getStorageSync('token')}`
      }
    })

    if (response.statusCode !== 200) {
      throw new Error('删除失败')
    }
    */

    // 从本地存储删除
    const userGallery = getUserGallery()
    const updatedGallery = userGallery.filter(work => work.id !== workId)
    wx.setStorageSync('userGallery', updatedGallery)

    return {
      success: true,
      message: '作品删除成功'
    }
  } catch (error) {
    console.error('删除作品失败：', error)
    return {
      success: false,
      message: error.message || '删除失败'
    }
  }
}

/**
 * 生成作品标题
 */
function generateWorkTitle(workData) {
  const typeText = workData.type === 'photo' ? '航拍摄影' : '航拍视频'
  const sequence = workData.sequence ? ` ${workData.sequence}` : ''
  return `${workData.locationName} - ${typeText}${sequence}`
}

/**
 * 生成占位符URL
 */
function generatePlaceholderUrl(type) {
  const size = type === 'photo' ? '400x400' : '400x300'
  return `http://iph.href.lu/${size}`
}

/**
 * 生成缩略图URL
 */
function generateThumbnailUrl(type) {
  const size = type === 'photo' ? '200x200' : '200x150'
  return `http://iph.href.lu/${size}`
}

/**
 * 生成标签
 */
function generateTags(workData) {
  const tags = ['航拍']
  if (workData.type === 'photo') {
    tags.push('摄影')
  } else {
    tags.push('视频')
  }
  if (workData.locationName) {
    tags.push(workData.locationName)
  }
  return tags
}

/**
 * 生成描述
 */
function generateDescription(workData) {
  const typeText = workData.type === 'photo' ? '航拍摄影作品' : '航拍视频作品'
  return `使用${workData.equipmentName}在${workData.locationName}拍摄的${typeText}`
}

/**
 * 格式化短日期
 */
function formatDateShort(date) {
  const month = String(date.getMonth() + 1).padStart(2, '0')
  const day = String(date.getDate()).padStart(2, '0')
  return `${month}-${day}`
}

/**
 * 计算作品集统计
 * @param {Array} works - 作品列表
 * @returns {Object} 统计数据
 */
function calculateGalleryStats(works) {
  const photoCount = works.filter(w => w.type === 'photo').length
  const videoCount = works.filter(w => w.type === 'video').length
  
  // 计算总文件大小（模拟）
  const totalSizeMB = works.reduce((total, work) => {
    const sizeStr = work.fileSize || '0MB'
    const size = parseFloat(sizeStr.replace('MB', '')) || 0
    return total + size
  }, 0)
  
  const storageSize = totalSizeMB > 1024 
    ? `${(totalSizeMB / 1024).toFixed(1)}GB` 
    : `${totalSizeMB.toFixed(1)}MB`

  // 计算拍摄天数
  const uniqueDates = [...new Set(works.map(w => w.createTime.substring(0, 10)))]
  
  return {
    totalWorks: works.length,
    photoCount: photoCount,
    videoCount: videoCount,
    shootingDays: uniqueDates.length,
    storageSize: storageSize,
    shareCount: works.reduce((total, work) => total + (work.shares || 0), 0)
  }
}

module.exports = {
  saveWorkToGallery,
  saveBatchWorks,
  getUserGallery,
  getWorksByOrderId,
  deleteWork,
  calculateGalleryStats
}
