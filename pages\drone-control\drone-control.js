// pages/drone-control/drone-control.js
const app = getApp()
const auth = require('../../utils/auth.js')
const { mockEquipment } = require('../../utils/mockData.js')
const galleryManager = require('../../utils/galleryManager.js')
const orderManager = require('../../utils/orderManager.js')

Page({

  /**
   * 页面的初始数据
   */
  data: {
    // 订单和设备信息
    orderId: null,
    equipmentId: null,
    equipment: null,

    // 飞行状态
    isConnected: false,
    isFlying: false,
    flightMode: 'manual', // manual, auto, return

    // 飞行数据
    flightData: {
      altitude: 125.0,
      speed: 15.0,
      battery: 83,
      signal: 95,
      distance: 2.3,
      windSpeed: 3.0,
      flightTime: 0
    },

    // 相机设置
    cameraSettings: {
      iso: '200',
      shutter: '1/250',
      aperture: 'f/2.8',
      focal: '24mm'
    },

    // 控制状态
    recording: false,
    photoCount: 0,
    videoCount: 0,

    // 页面状态
    showSettingsModal: false,
    loading: false,

    // 页面状态
    loading: true,
    showSettings: false,

    // 格式化的飞行时间
    formattedFlightTime: '0:00'
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    console.log('无人机控制页面加载', options)

    this.setData({
      orderId: options.orderId,
      equipmentId: options.equipmentId
    })

    this.checkAuth()
    this.loadEquipmentInfo()
    this.initDroneConnection()
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {
    // 保持屏幕常亮
    wx.setKeepScreenOn({
      keepScreenOn: true
    })
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    // 恢复连接
    if (this.data.isConnected) {
      this.resumeConnection()
    }
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {
    // 暂停连接但不断开
    this.pauseConnection()
  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {
    // 保存拍摄统计和作品
    this.saveShootingResults()

    // 断开连接
    this.disconnectDrone()

    // 取消屏幕常亮
    wx.setKeepScreenOn({
      keepScreenOn: false
    })
  },

  /**
   * 处理用户返回操作
   */
  onBackPress() {
    // 拦截系统返回，使用自定义退出逻辑
    this.exitControl()
    return true // 阻止默认返回行为
  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {
    this.refreshFlightData()
    wx.stopPullDownRefresh()
  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {
    return {
      title: '我正在用逍遥境控制无人机！',
      path: '/pages/home/<USER>'
    }
  },

  /**
   * 检查认证状态
   */
  checkAuth() {
    if (!auth.checkLoginStatus()) {
      auth.redirectToLogin('/pages/drone-control/drone-control')
      return false
    }
    return true
  },

  /**
   * 加载设备信息
   */
  async loadEquipmentInfo() {
    try {
      const equipment = mockEquipment.find(item => item.id === this.data.equipmentId)

      if (!equipment) {
        throw new Error('设备不存在')
      }

      // 获取订单信息以获取位置数据和拍摄统计
      let locationName = '未知地点'
      let photoCount = 0
      let videoCount = 0

      if (this.data.orderId) {
        const order = orderManager.getOrderById(this.data.orderId)
        if (order) {
          locationName = order.locationName || '未知地点'
          // 恢复之前的拍摄统计
          photoCount = order.photoCount || 0
          videoCount = order.videoCount || 0
        }
      }

      this.setData({
        equipment,
        equipmentName: equipment.name,
        locationName: locationName,
        photoCount: photoCount,
        videoCount: videoCount,
        'flightData.battery': equipment.battery || 100
      })

      console.log('设备信息加载完成：', equipment.name, '位置：', locationName, `拍摄统计：${photoCount}张照片，${videoCount}个视频`)
    } catch (error) {
      console.error('加载设备信息失败：', error)
      wx.showToast({
        title: '设备信息错误',
        icon: 'error'
      })
    }
  },

  /**
   * 初始化无人机连接
   */
  async initDroneConnection() {
    this.setData({ loading: true })

    try {
      // 模拟连接过程
      wx.showLoading({ title: '连接设备中...' })

      await new Promise(resolve => setTimeout(resolve, 3000))

      this.setData({
        isConnected: true,
        controlEnabled: true,
        loading: false,
        formattedFlightTime: '0:00'
      })

      // 开始飞行数据更新
      this.startFlightDataUpdate()

      wx.hideLoading()
      wx.showToast({
        title: '设备连接成功',
        icon: 'success'
      })

      console.log('无人机连接成功')
    } catch (error) {
      console.error('连接失败：', error)
      wx.hideLoading()
      wx.showToast({
        title: '连接失败',
        icon: 'error'
      })

      this.setData({ loading: false })
    }
  },

  /**
   * 开始飞行数据更新
   */
  startFlightDataUpdate() {
    this.flightDataTimer = setInterval(() => {
      if (this.data.isConnected) {
        this.updateFlightData()
      }
    }, 1000)
  },

  /**
   * 更新飞行数据
   */
  updateFlightData() {
    const flightData = { ...this.data.flightData }

    // 模拟飞行数据变化
    if (this.data.isFlying) {
      flightData.flightTime += 1
      flightData.battery = Math.max(0, flightData.battery - 0.1)
      flightData.altitude += (Math.random() - 0.5) * 2
      flightData.speed = Math.random() * 15
      flightData.distance += flightData.speed / 3600
    }

    flightData.signal = 90 + Math.random() * 10
    flightData.gps = 10 + Math.random() * 5

    // 格式化飞行时间
    const minutes = Math.floor(flightData.flightTime / 60)
    const seconds = Math.floor(flightData.flightTime % 60)
    const formattedFlightTime = `${minutes}:${seconds.toString().padStart(2, '0')}`

    this.setData({
      flightData,
      formattedFlightTime
    })
  },

  /**
   * 起飞
   */
  takeOff() {
    if (!this.data.isConnected || this.data.isFlying) return

    wx.showModal({
      title: '确认起飞',
      content: '请确保周围环境安全，确认起飞？',
      success: (res) => {
        if (res.confirm) {
          this.setData({
            isFlying: true,
            'flightData.altitude': 5
          })

          wx.showToast({
            title: '起飞成功',
            icon: 'success'
          })

          console.log('无人机起飞')
        }
      }
    })
  },

  /**
   * 降落
   */
  land() {
    if (!this.data.isFlying) return

    this.setData({
      isFlying: false,
      'flightData.altitude': 0,
      'flightData.speed': 0
    })

    wx.showToast({
      title: '降落成功',
      icon: 'success'
    })

    console.log('无人机降落')
  },

  /**
   * 返航
   */
  returnHome() {
    if (!this.data.isFlying) return

    wx.showModal({
      title: '确认返航',
      content: '无人机将自动返回起飞点，确认返航？',
      success: (res) => {
        if (res.confirm) {
          this.setData({ flightMode: 'return' })

          wx.showToast({
            title: '开始返航',
            icon: 'success'
          })

          // 模拟返航过程
          setTimeout(() => {
            this.land()
            this.setData({ flightMode: 'manual' })
          }, 5000)

          console.log('无人机返航')
        }
      }
    })
  },

  /**
   * 拍照
   */
  takePhoto() {
    if (!this.data.isFlying) {
      wx.showToast({
        title: '请先起飞',
        icon: 'none'
      })
      return
    }

    wx.showToast({
      title: '拍照成功',
      icon: 'success'
    })

    console.log('拍照')
  },

  /**
   * 录像
   */
  toggleRecording() {
    if (!this.data.isFlying) {
      wx.showToast({
        title: '请先起飞',
        icon: 'none'
      })
      return
    }

    const recording = !this.data.recording
    this.setData({ recording })

    wx.showToast({
      title: recording ? '开始录像' : '停止录像',
      icon: 'success'
    })

    console.log(recording ? '开始录像' : '停止录像')
  },

  /**
   * 切换拍照模式
   */
  togglePhotoMode() {
    this.setData({ photoMode: !this.data.photoMode })
  },

  /**
   * 显示设置
   */
  showSettings() {
    this.setData({ showSettings: true })
  },

  /**
   * 隐藏设置
   */
  hideSettings() {
    this.setData({ showSettings: false })
  },

  /**
   * 断开连接
   */
  disconnectDrone() {
    if (this.flightDataTimer) {
      clearInterval(this.flightDataTimer)
      this.flightDataTimer = null
    }

    this.setData({
      isConnected: false,
      isFlying: false,
      controlEnabled: false
    })

    console.log('无人机连接断开')
  },

  /**
   * 暂停连接
   */
  pauseConnection() {
    if (this.flightDataTimer) {
      clearInterval(this.flightDataTimer)
      this.flightDataTimer = null
    }
  },

  /**
   * 恢复连接
   */
  resumeConnection() {
    this.startFlightDataUpdate()
  },

  /**
   * 刷新飞行数据
   */
  refreshFlightData() {
    if (this.data.isConnected) {
      this.updateFlightData()
      wx.showToast({
        title: '数据已刷新',
        icon: 'success'
      })
    }
  },

  /**
   * 紧急停止
   */
  emergencyStop() {
    wx.showModal({
      title: '紧急停止',
      content: '这将立即停止所有操作，确认执行？',
      confirmColor: '#ef4444',
      success: (res) => {
        if (res.confirm) {
          this.setData({
            isFlying: false,
            'flightData.altitude': 0,
            'flightData.speed': 0,
            flightMode: 'manual'
          })

          wx.showToast({
            title: '紧急停止执行',
            icon: 'success'
          })

          console.log('紧急停止')
        }
      }
    })
  },

  /**
   * 退出控制
   */
  exitControl() {
    wx.showModal({
      title: '退出操控',
      content: '确认退出无人机操控？\n无人机将自动返航。',
      success: (res) => {
        if (res.confirm) {
          wx.showToast({
            title: '无人机正在返航',
            icon: 'success'
          })

          setTimeout(() => {
            // 返回到订单页面（tabBar页面需要使用switchTab）
            wx.switchTab({
              url: '/pages/orders/orders'
            })
          }, 2000)
        }
      }
    })
  },

  /**
   * 拍照
   */
  takePhoto() {
    const photoCount = this.data.photoCount + 1
    this.setData({ photoCount })

    // 闪光效果
    wx.showToast({
      title: `拍照成功！第${photoCount}张`,
      icon: 'success'
    })

    console.log('拍照', photoCount)
  },

  /**
   * 切换录制
   */
  toggleRecording() {
    const recording = !this.data.recording
    this.setData({ recording })

    if (recording) {
      wx.showToast({
        title: '开始录制',
        icon: 'success'
      })
    } else {
      const videoCount = this.data.videoCount + 1
      this.setData({ videoCount })
      wx.showToast({
        title: `录制结束！第${videoCount}个视频`,
        icon: 'success'
      })
    }

    console.log('录制状态', recording)
  },

  /**
   * 返航
   */
  returnHome() {
    wx.showModal({
      title: '返航确认',
      content: '确认让无人机返航？',
      success: (res) => {
        if (res.confirm) {
          wx.showToast({
            title: '无人机正在返航',
            icon: 'success'
          })
          console.log('返航')
        }
      }
    })
  },

  /**
   * 切换云台
   */
  toggleGimbal() {
    wx.showToast({
      title: '云台模式已切换',
      icon: 'success'
    })
    console.log('云台控制')
  },

  /**
   * 显示设置
   */
  showSettings() {
    this.setData({ showSettingsModal: true })
  },

  /**
   * 隐藏设置
   */
  hideSettings() {
    this.setData({ showSettingsModal: false })
  },

  /**
   * 放大
   */
  zoomIn() {
    wx.showToast({
      title: '放大镜头',
      icon: 'success'
    })
    console.log('放大')
  },

  /**
   * 缩小
   */
  zoomOut() {
    wx.showToast({
      title: '缩小镜头',
      icon: 'success'
    })
    console.log('缩小')
  },

  /**
   * 切换模式
   */
  changeMode() {
    wx.showActionSheet({
      itemList: ['自动模式', '手动模式', '运动模式', '电影模式'],
      success: (res) => {
        const modes = ['自动模式', '手动模式', '运动模式', '电影模式']
        wx.showToast({
          title: `切换到${modes[res.tapIndex]}`,
          icon: 'success'
        })
      }
    })
  },

  /**
   * 保存拍摄结果
   */
  async saveShootingResults() {
    try {
      const { photoCount, videoCount, orderId, equipmentId, equipmentName, locationName } = this.data

      // 总是保存拍摄统计，即使为0（用户可能删除了之前的拍摄）

      console.log(`保存拍摄结果：${photoCount}张照片，${videoCount}个视频`)

      // 更新订单的拍摄统计
      if (orderId) {
        const updateResult = await orderManager.updateOrderShootingStats(orderId, {
          photoCount,
          videoCount,
          totalWorks: photoCount + videoCount
        })

        if (updateResult.success) {
          console.log('订单拍摄统计更新成功')
        }
      }

      // 只有在有拍摄内容时才保存作品到作品集
      if (photoCount > 0 || videoCount > 0) {
        const saveResult = await galleryManager.saveBatchWorks({
          orderId: orderId,
          equipmentId: equipmentId,
          equipmentName: equipmentName,
          locationId: 'location_001', // TODO: 从订单获取实际位置ID
          locationName: locationName || '未知地点',
          photoCount: photoCount,
          videoCount: videoCount
        })

        if (saveResult.success) {
          console.log(`作品保存成功：共${saveResult.totalCount}个作品`)
        }
      }

    } catch (error) {
      console.error('保存拍摄结果失败：', error)
    }
  }
})