<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>无人机操控 - 逍遥境</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body { height: 852px; overflow: hidden; background: black; }
        .status-bar {
            background: rgba(0,0,0,0.8);
            color: white;
            height: 47px;
            font-size: 14px;
            font-weight: 600;
        }
        .video-stream {
            background: linear-gradient(45deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
            position: relative;
            overflow: hidden;
        }
        .floating-clouds {
            position: absolute;
            width: 100%;
            height: 100%;
            background: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 20"><circle cx="20" cy="10" r="3" fill="rgba(255,255,255,0.1)"/><circle cx="40" cy="15" r="2" fill="rgba(255,255,255,0.08)"/><circle cx="60" cy="8" r="4" fill="rgba(255,255,255,0.06)"/><circle cx="80" cy="12" r="2.5" fill="rgba(255,255,255,0.09)"/></svg>') repeat-x;
            animation: float 20s linear infinite;
        }
        @keyframes float {
            0% { transform: translateX(0); }
            100% { transform: translateX(-100px); }
        }
        .hud-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            pointer-events: none;
        }
        .crosshair {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 40px;
            height: 40px;
            border: 2px solid rgba(255,255,255,0.8);
            border-radius: 50%;
        }
        .crosshair::before, .crosshair::after {
            content: '';
            position: absolute;
            background: rgba(255,255,255,0.8);
        }
        .crosshair::before {
            top: 50%;
            left: -10px;
            right: -10px;
            height: 1px;
            transform: translateY(-50%);
        }
        .crosshair::after {
            left: 50%;
            top: -10px;
            bottom: -10px;
            width: 1px;
            transform: translateX(-50%);
        }
        .control-joystick {
            width: 100px;
            height: 100px;
            border: 2px solid rgba(255,255,255,0.3);
            border-radius: 50%;
            position: relative;
            background: rgba(0,0,0,0.2);
        }
        .joystick-handle {
            width: 40px;
            height: 40px;
            background: rgba(255,255,255,0.8);
            border-radius: 50%;
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            cursor: grab;
            transition: all 0.1s ease;
        }
        .joystick-handle:active {
            cursor: grabbing;
            background: rgba(255,255,255,1);
        }
        .recording {
            animation: pulse 1s infinite;
        }
        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }
        .battery-indicator {
            background: linear-gradient(90deg, #10b981 0%, #10b981 80%, #f59e0b 80%, #f59e0b 90%, #ef4444 90%);
        }
    </style>
</head>
<body>
    <!-- iOS 状态栏 -->
    <div class="status-bar flex justify-between items-center px-6">
        <div class="flex items-center space-x-1">
            <span>9:41</span>
        </div>
        <div class="flex items-center space-x-1">
            <i class="fas fa-signal text-xs"></i>
            <i class="fas fa-wifi text-xs"></i>
            <div class="w-6 h-3 border border-white rounded-sm">
                <div class="w-4 h-1 bg-white rounded-sm mt-0.5 ml-0.5"></div>
            </div>
        </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="relative" style="height: calc(852px - 47px);">
        <!-- 视频流区域 -->
        <div class="video-stream h-full relative">
            <!-- 模拟航拍视频背景 -->
            <img src="https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=800&h=600&fit=crop" 
                 alt="航拍视角" class="w-full h-full object-cover opacity-80">
            
            <!-- 浮动云朵效果 -->
            <div class="floating-clouds"></div>

            <!-- HUD 覆盖层 -->
            <div class="hud-overlay">
                <!-- 十字准心 -->
                <div class="crosshair"></div>

                <!-- 顶部状态栏 -->
                <div class="absolute top-4 left-4 right-4 flex justify-between items-center text-white text-sm">
                    <div class="flex items-center space-x-4">
                        <button onclick="exitControl()" class="bg-black bg-opacity-50 px-3 py-1 rounded-full">
                            <i class="fas fa-times mr-1"></i>退出
                        </button>
                        <div class="bg-black bg-opacity-50 px-3 py-1 rounded-full">
                            <i class="fas fa-satellite-dish mr-1"></i>
                            <span id="signalStrength">信号强度: 95%</span>
                        </div>
                    </div>
                    <div class="flex items-center space-x-4">
                        <div class="bg-black bg-opacity-50 px-3 py-1 rounded-full">
                            <i class="fas fa-clock mr-1"></i>
                            <span id="flightTime">00:15:32</span>
                        </div>
                        <div class="bg-black bg-opacity-50 px-3 py-1 rounded-full flex items-center">
                            <i class="fas fa-battery-three-quarters mr-1"></i>
                            <div class="w-8 h-2 bg-gray-600 rounded-full overflow-hidden">
                                <div class="battery-indicator h-full w-4/5 rounded-full"></div>
                            </div>
                            <span class="ml-1">83%</span>
                        </div>
                    </div>
                </div>

                <!-- 飞行参数显示 -->
                <div class="absolute top-20 left-4 bg-black bg-opacity-50 rounded-lg p-3 text-white text-xs">
                    <div class="grid grid-cols-2 gap-3">
                        <div>高度: <span id="altitude" class="font-mono">125m</span></div>
                        <div>速度: <span id="speed" class="font-mono">15km/h</span></div>
                        <div>距离: <span id="distance" class="font-mono">2.3km</span></div>
                        <div>风速: <span id="windSpeed" class="font-mono">3m/s</span></div>
                    </div>
                </div>

                <!-- 相机设置 -->
                <div class="absolute top-20 right-4 bg-black bg-opacity-50 rounded-lg p-3 text-white text-xs">
                    <div class="space-y-2">
                        <div>ISO: <span class="font-mono">200</span></div>
                        <div>快门: <span class="font-mono">1/250</span></div>
                        <div>光圈: <span class="font-mono">f/2.8</span></div>
                        <div>焦距: <span class="font-mono">24mm</span></div>
                    </div>
                </div>

                <!-- 录制状态指示 -->
                <div id="recordingIndicator" class="absolute top-4 left-1/2 transform -translate-x-1/2 bg-red-600 text-white px-4 py-2 rounded-full hidden recording">
                    <i class="fas fa-circle text-xs mr-2"></i>正在录制
                </div>
            </div>
        </div>

        <!-- 底部控制面板 -->
        <div class="absolute bottom-0 left-0 right-0 bg-black bg-opacity-80 p-4">
            <div class="flex items-center justify-between">
                <!-- 左侧摇杆（方向控制） -->
                <div class="flex flex-col items-center">
                    <div class="control-joystick mb-2">
                        <div class="joystick-handle" id="leftJoystick"></div>
                    </div>
                    <span class="text-white text-xs">方向</span>
                </div>

                <!-- 中央控制按钮 -->
                <div class="flex flex-col items-center space-y-3">
                    <!-- 拍照/录像按钮 -->
                    <div class="flex items-center space-x-4">
                        <button onclick="takePhoto()" class="w-16 h-16 bg-white rounded-full flex items-center justify-center text-gray-800 text-xl hover:bg-gray-200 transition-colors">
                            <i class="fas fa-camera"></i>
                        </button>
                        <button onclick="toggleRecording()" id="recordBtn" class="w-16 h-16 bg-red-600 rounded-full flex items-center justify-center text-white text-xl hover:bg-red-700 transition-colors">
                            <i class="fas fa-video"></i>
                        </button>
                    </div>
                    
                    <!-- 功能按钮 -->
                    <div class="flex items-center space-x-6">
                        <button onclick="returnHome()" class="bg-orange-600 text-white px-4 py-2 rounded-lg text-sm hover:bg-orange-700 transition-colors">
                            <i class="fas fa-home mr-1"></i>返航
                        </button>
                        <button onclick="toggleGimbal()" class="bg-blue-600 text-white px-4 py-2 rounded-lg text-sm hover:bg-blue-700 transition-colors">
                            <i class="fas fa-expand-arrows-alt mr-1"></i>云台
                        </button>
                        <button onclick="showSettings()" class="bg-gray-600 text-white px-4 py-2 rounded-lg text-sm hover:bg-gray-700 transition-colors">
                            <i class="fas fa-cog mr-1"></i>设置
                        </button>
                    </div>
                </div>

                <!-- 右侧摇杆（高度/旋转控制） -->
                <div class="flex flex-col items-center">
                    <div class="control-joystick mb-2">
                        <div class="joystick-handle" id="rightJoystick"></div>
                    </div>
                    <span class="text-white text-xs">高度/旋转</span>
                </div>
            </div>
        </div>

        <!-- 侧边功能栏 -->
        <div class="absolute right-4 top-1/2 transform -translate-y-1/2 space-y-3">
            <button onclick="zoomIn()" class="w-12 h-12 bg-black bg-opacity-50 text-white rounded-full flex items-center justify-center">
                <i class="fas fa-plus"></i>
            </button>
            <button onclick="zoomOut()" class="w-12 h-12 bg-black bg-opacity-50 text-white rounded-full flex items-center justify-center">
                <i class="fas fa-minus"></i>
            </button>
            <button onclick="changeMode()" class="w-12 h-12 bg-black bg-opacity-50 text-white rounded-full flex items-center justify-center">
                <i class="fas fa-eye"></i>
            </button>
        </div>
    </div>

    <script>
        let isRecording = false;
        let flightStartTime = Date.now();
        let photoCount = 0;
        let videoCount = 0;

        // 更新飞行时间
        function updateFlightTime() {
            const elapsed = Date.now() - flightStartTime;
            const hours = Math.floor(elapsed / 3600000);
            const minutes = Math.floor((elapsed % 3600000) / 60000);
            const seconds = Math.floor((elapsed % 60000) / 1000);
            
            document.getElementById('flightTime').textContent = 
                `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
        }

        // 模拟飞行参数变化
        function updateFlightParams() {
            document.getElementById('altitude').textContent = `${(120 + Math.random() * 10).toFixed(1)}m`;
            document.getElementById('speed').textContent = `${(10 + Math.random() * 10).toFixed(1)}km/h`;
            document.getElementById('distance').textContent = `${(2.0 + Math.random() * 0.5).toFixed(1)}km`;
            document.getElementById('signalStrength').textContent = `信号强度: ${(90 + Math.random() * 10).toFixed(0)}%`;
        }

        function exitControl() {
            if (confirm('确认退出无人机操控？\n无人机将自动返航。')) {
                alert('无人机正在返航，操控已结束。\n正在跳转到订单页面...');
                // 模拟跳转到订单页面
            }
        }

        function takePhoto() {
            photoCount++;
            // 闪光效果
            document.body.style.background = 'white';
            setTimeout(() => {
                document.body.style.background = 'black';
            }, 100);
            
            alert(`拍照成功！\n已保存第 ${photoCount} 张照片`);
        }

        function toggleRecording() {
            const recordBtn = document.getElementById('recordBtn');
            const recordingIndicator = document.getElementById('recordingIndicator');
            
            if (isRecording) {
                // 停止录制
                isRecording = false;
                recordBtn.classList.remove('recording');
                recordBtn.innerHTML = '<i class="fas fa-video"></i>';
                recordingIndicator.classList.add('hidden');
                videoCount++;
                alert(`录制结束！\n已保存第 ${videoCount} 个视频`);
            } else {
                // 开始录制
                isRecording = true;
                recordBtn.classList.add('recording');
                recordBtn.innerHTML = '<i class="fas fa-stop"></i>';
                recordingIndicator.classList.remove('hidden');
                alert('开始录制...');
            }
        }

        function returnHome() {
            if (confirm('确认让无人机返航？')) {
                alert('无人机正在返航...\n预计3分钟后到达起飞点');
            }
        }

        function toggleGimbal() {
            alert('云台模式已切换\n现在可以独立控制相机角度');
        }

        function showSettings() {
            alert('相机设置\n• ISO: 自动/手动\n• 白平衡: 自动\n• 曝光: +0.3EV\n• 格式: JPG+RAW');
        }

        function zoomIn() {
            alert('放大镜头');
        }

        function zoomOut() {
            alert('缩小镜头');
        }

        function changeMode() {
            alert('切换拍摄模式\n• 自动模式\n• 手动模式\n• 运动模式\n• 电影模式');
        }

        // 摇杆控制逻辑
        function initJoystickControl(joystickId) {
            const joystick = document.getElementById(joystickId);
            let isDragging = false;
            let startX, startY, centerX, centerY;

            joystick.addEventListener('mousedown', startDrag);
            joystick.addEventListener('touchstart', startDrag);

            function startDrag(e) {
                isDragging = true;
                const rect = joystick.parentElement.getBoundingClientRect();
                centerX = rect.left + rect.width / 2;
                centerY = rect.top + rect.height / 2;
                
                document.addEventListener('mousemove', drag);
                document.addEventListener('mouseup', stopDrag);
                document.addEventListener('touchmove', drag);
                document.addEventListener('touchend', stopDrag);
            }

            function drag(e) {
                if (!isDragging) return;
                
                const clientX = e.clientX || e.touches[0].clientX;
                const clientY = e.clientY || e.touches[0].clientY;
                
                const deltaX = clientX - centerX;
                const deltaY = clientY - centerY;
                const distance = Math.sqrt(deltaX * deltaX + deltaY * deltaY);
                const maxDistance = 30;
                
                if (distance <= maxDistance) {
                    joystick.style.transform = `translate(calc(-50% + ${deltaX}px), calc(-50% + ${deltaY}px))`;
                } else {
                    const angle = Math.atan2(deltaY, deltaX);
                    const limitedX = Math.cos(angle) * maxDistance;
                    const limitedY = Math.sin(angle) * maxDistance;
                    joystick.style.transform = `translate(calc(-50% + ${limitedX}px), calc(-50% + ${limitedY}px))`;
                }
            }

            function stopDrag() {
                isDragging = false;
                joystick.style.transform = 'translate(-50%, -50%)';
                document.removeEventListener('mousemove', drag);
                document.removeEventListener('mouseup', stopDrag);
                document.removeEventListener('touchmove', drag);
                document.removeEventListener('touchend', stopDrag);
            }
        }

        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 初始化摇杆控制
            initJoystickControl('leftJoystick');
            initJoystickControl('rightJoystick');
            
            // 开始更新飞行参数
            setInterval(updateFlightTime, 1000);
            setInterval(updateFlightParams, 2000);
        });
    </script>
</body>
</html> 