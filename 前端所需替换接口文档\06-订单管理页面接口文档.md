# 订单管理页面接口文档

## 页面概述
订单管理页面，用户可以查看所有订单、筛选订单状态、进行订单操作（取消、继续控制、提前结束、评价等）。

## 当前实现分析

### 页面文件位置
- `pages/orders/orders.js` - 订单列表页逻辑
- `utils/orderManager.js` - 订单管理工具
- `utils/mockData.js` - 模拟订单数据

### 当前功能流程
1. **订单列表加载**：获取用户所有订单
2. **状态筛选**：按全部/进行中/已完成/已取消筛选
3. **订单操作**：
   - 取消订单
   - 继续控制（跳转到无人机控制页面）
   - 提前结束
   - 查看作品集
   - 评价订单
   - 重新预订

## 需要替换的接口

### 1. 获取用户订单列表接口

#### 接口信息
- **接口名称**: 获取用户订单列表
- **请求方法**: GET
- **接口路径**: `/api/orders/list`
- **当前模拟位置**: `pages/orders/orders.js` 第103-219行 `loadOrders` 方法

#### 请求头
```
Authorization: Bearer {token}
```

#### 请求参数
```
page=1              // 页码
limit=20            // 每页数量
status=all          // 订单状态：all/ongoing/completed/cancelled
```

#### 响应数据
```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "list": [
      {
        "id": "order_001",
        "equipmentId": "dji_air3",
        "equipmentName": "DJI Air 3",
        "equipmentImage": "http://iph.href.lu/300x200",
        "locationId": "shusheng_01",
        "locationName": "书圣故里",
        "status": "ongoing",
        "statusText": "进行中",
        "orderTime": "2024-01-27T09:30:00.000Z",
        "startTime": "2024-01-27T10:00:00.000Z",
        "endTime": "2024-01-27T12:00:00.000Z",
        "duration": 2,
        "totalAmount": 175,
        "canControl": true,
        "canCancel": true,
        "canReview": false,
        "remainingTime": "1小时15分",
        "progress": 35,
        "photoCount": 0,
        "videoCount": 0,
        "totalWorks": 0
      }
    ],
    "pagination": {
      "page": 1,
      "limit": 20,
      "total": 25,
      "totalPages": 2,
      "hasNext": true,
      "hasPrev": false
    }
  }
}
```

### 2. 取消订单接口

#### 接口信息
- **接口名称**: 取消订单
- **请求方法**: POST
- **接口路径**: `/api/orders/cancel`
- **当前模拟位置**: `pages/orders/orders.js` 第267-296行 `cancelOrder` 方法

#### 请求头
```
Authorization: Bearer {token}
```

#### 请求参数
```json
{
  "orderId": "order_001",
  "reason": "用户主动取消"
}
```

#### 响应数据
```json
{
  "code": 200,
  "message": "订单取消成功",
  "data": {
    "orderId": "order_001",
    "status": "cancelled",
    "statusText": "已取消",
    "refundAmount": 175,
    "refundTime": "2024-01-27T11:00:00.000Z",
    "cancelTime": "2024-01-27T11:00:00.000Z"
  }
}
```

### 3. 提前结束订单接口

#### 接口信息
- **接口名称**: 提前结束订单
- **请求方法**: POST
- **接口路径**: `/api/orders/end-early`
- **当前模拟位置**: `pages/orders/orders.js` 第330-360行 `endEarly` 方法

#### 请求头
```
Authorization: Bearer {token}
```

#### 请求参数
```json
{
  "orderId": "order_001",
  "endTime": "2024-01-27T11:30:00.000Z"
}
```

#### 响应数据
```json
{
  "code": 200,
  "message": "订单已提前结束",
  "data": {
    "orderId": "order_001",
    "status": "completed",
    "statusText": "已完成",
    "actualEndTime": "2024-01-27T11:30:00.000Z",
    "refundAmount": 40,
    "refundReason": "提前结束按比例退款",
    "photoCount": 12,
    "videoCount": 3,
    "totalWorks": 15
  }
}
```

### 4. 订单评价接口

#### 接口信息
- **接口名称**: 提交订单评价
- **请求方法**: POST
- **接口路径**: `/api/orders/review`
- **当前模拟位置**: `pages/orders/orders.js` 第377-403行 `rateOrder` 方法

#### 请求头
```
Authorization: Bearer {token}
```

#### 请求参数
```json
{
  "orderId": "order_001",
  "rating": 5,
  "comment": "设备状态很好，拍摄效果超棒！",
  "tags": ["设备优秀", "服务满意", "体验良好"],
  "images": ["评价图片URL1", "评价图片URL2"]
}
```

#### 响应数据
```json
{
  "code": 200,
  "message": "评价提交成功",
  "data": {
    "reviewId": "review_001",
    "orderId": "order_001",
    "rating": 5,
    "comment": "设备状态很好，拍摄效果超棒！",
    "createTime": "2024-01-27T15:00:00.000Z",
    "rewardPoints": 10
  }
}
```

### 5. 获取订单详情接口

#### 接口信息
- **接口名称**: 获取订单详细信息
- **请求方法**: GET
- **接口路径**: `/api/orders/detail/{orderId}`
- **当前模拟位置**: `pages/orders/orders.js` 第257-262行 `viewOrderDetail` 方法

#### 请求头
```
Authorization: Bearer {token}
```

#### 请求参数
```
orderId: 订单ID（路径参数）
```

#### 响应数据
```json
{
  "code": 200,
  "message": "获取成功",
  "data": {
    "id": "order_001",
    "equipmentId": "dji_air3",
    "equipmentName": "DJI Air 3",
    "equipmentImage": "http://iph.href.lu/300x200",
    "locationId": "shusheng_01",
    "locationName": "书圣故里",
    "locationAddress": "绍兴市越城区蕺山街道",
    "status": "ongoing",
    "statusText": "进行中",
    "orderTime": "2024-01-27T09:30:00.000Z",
    "startTime": "2024-01-27T10:00:00.000Z",
    "endTime": "2024-01-27T12:00:00.000Z",
    "duration": 2,
    "totalAmount": 175,
    "payment": {
      "method": "balance",
      "methodText": "余额支付",
      "transactionId": "tx_001"
    },
    "equipment": {
      "specs": ["4K双摄", "46分钟续航", "全向避障"],
      "battery": 85,
      "icon": "icon-drone"
    },
    "timeline": [
      {
        "time": "2024-01-27T09:30:00.000Z",
        "event": "订单创建",
        "description": "订单创建成功，等待开始"
      },
      {
        "time": "2024-01-27T10:00:00.000Z",
        "event": "开始租赁",
        "description": "设备已激活，开始计时"
      }
    ],
    "canControl": true,
    "canCancel": true,
    "canReview": false,
    "photoCount": 8,
    "videoCount": 2,
    "totalWorks": 10
  }
}
```

## 替换指导

### 1. 修改订单列表加载
**文件**: `pages/orders/orders.js`
**位置**: 第103-219行 `loadOrders` 方法

**当前代码**:
```javascript
async loadOrders() {
  this.setData({ loading: true })

  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000))

    // 获取用户实际创建的订单
    const userOrders = orderManager.getUserOrders()

    // 合并模拟订单数据（用于演示）
    const mockOrders = [...]
    
    // 转换用户订单格式以匹配页面显示需求
    const formattedUserOrders = userOrders.map(order => ({...}))

    // 合并用户订单和模拟订单，用户订单在前
    const allOrders = [...formattedUserOrders, ...mockOrders]

    this.setData({ orders: allOrders })
    this.filterOrders()

  } catch (error) {
    console.error('加载订单失败：', error)
    wx.showToast({
      title: '加载失败',
      icon: 'error'
    })
  } finally {
    this.setData({ loading: false })
  }
}
```

**替换为**:
```javascript
async loadOrders() {
  this.setData({ loading: true })

  try {
    const params = {
      page: 1,
      limit: 50,
      status: 'all'
    }
    
    const response = await request.get('/api/orders/list', params)
    
    if (response && response.data) {
      // 格式化订单数据以匹配页面显示
      const formattedOrders = response.data.list.map(order => ({
        ...order,
        equipmentName: `${order.equipmentName} 租赁`,
        createTime: order.orderTime.substring(0, 16),
        startTime: order.startTime.substring(0, 16)
      }))
      
      this.setData({ orders: formattedOrders })
      this.filterOrders()
      
      console.log('订单数据加载完成：', formattedOrders.length, '个')
    }
  } catch (error) {
    console.error('加载订单失败：', error)
    wx.showToast({
      title: '加载失败',
      icon: 'error'
    })
    // 使用本地数据作为降级方案
    this.loadOrdersFallback()
  } finally {
    this.setData({ loading: false })
  }
}
```

### 2. 修改取消订单
**文件**: `pages/orders/orders.js`
**位置**: 第267-296行 `cancelOrder` 方法

**替换为**:
```javascript
cancelOrder(e) {
  const orderId = e.currentTarget.dataset.orderId

  wx.showModal({
    title: '取消订单',
    content: '确定要取消这个订单吗？取消后将按规则退款',
    success: async (res) => {
      if (res.confirm) {
        try {
          const response = await request.post('/api/orders/cancel', {
            orderId: orderId,
            reason: '用户主动取消'
          })

          if (response && response.data) {
            // 更新本地订单状态
            const orders = this.data.orders.map(order => {
              if (order.id === orderId) {
                return { 
                  ...order, 
                  status: 'cancelled', 
                  statusText: '已取消',
                  canCancel: false,
                  canControl: false
                }
              }
              return order
            })

            this.setData({ orders })
            this.filterOrders()

            wx.showToast({
              title: `订单已取消，退款${response.data.refundAmount}元`,
              icon: 'success',
              duration: 3000
            })
          }
        } catch (error) {
          console.error('取消订单失败：', error)
          wx.showToast({
            title: '取消失败，请重试',
            icon: 'error'
          })
        }
      }
    }
  })
}
```

### 3. 修改提前结束
**文件**: `pages/orders/orders.js`
**位置**: 第330-360行 `endEarly` 方法

**替换为**:
```javascript
endEarly(e) {
  const orderId = e.currentTarget.dataset.orderId

  wx.showModal({
    title: '提前结束',
    content: '确认提前结束租赁？剩余时间将按比例退款',
    success: async (res) => {
      if (res.confirm) {
        try {
          const response = await request.post('/api/orders/end-early', {
            orderId: orderId,
            endTime: new Date().toISOString()
          })

          if (response && response.data) {
            // 更新本地订单状态
            const orders = this.data.orders.map(order => {
              if (order.id === orderId) {
                return { 
                  ...order, 
                  status: 'completed', 
                  statusText: '已完成',
                  canControl: false,
                  canCancel: false,
                  canReview: true,
                  photoCount: response.data.photoCount,
                  videoCount: response.data.videoCount,
                  totalWorks: response.data.totalWorks
                }
              }
              return order
            })

            this.setData({ orders })
            this.filterOrders()

            wx.showToast({
              title: `租赁已结束，退款${response.data.refundAmount}元`,
              icon: 'success',
              duration: 3000
            })
          }
        } catch (error) {
          console.error('提前结束失败：', error)
          wx.showToast({
            title: '操作失败，请重试',
            icon: 'error'
          })
        }
      }
    }
  })
}
```

### 4. 修改订单评价
**文件**: `pages/orders/orders.js`
**位置**: 第377-403行 `rateOrder` 方法

**替换为**:
```javascript
rateOrder(e) {
  const orderId = e.currentTarget.dataset.orderId

  // 跳转到评价页面，或者显示评价弹窗
  wx.navigateTo({
    url: `/pages/order-review/order-review?orderId=${orderId}`
  })
  
  // 或者简化版本的评价处理
  /*
  wx.showModal({
    title: '订单评价',
    content: '请为本次租赁体验评分（1-5星）',
    editable: true,
    placeholderText: '请输入评价内容',
    success: async (res) => {
      if (res.confirm) {
        try {
          const response = await request.post('/api/orders/review', {
            orderId: orderId,
            rating: 5, // 默认5星，实际应该让用户选择
            comment: res.content || '满意',
            tags: ['体验良好']
          })

          if (response && response.data) {
            // 更新订单评价状态
            const orders = this.data.orders.map(order => {
              if (order.id === orderId) {
                return { ...order, rated: true, canReview: false }
              }
              return order
            })

            this.setData({ orders })
            this.filterOrders()

            wx.showToast({
              title: `感谢评价！获得${response.data.rewardPoints}积分`,
              icon: 'success'
            })
          }
        } catch (error) {
          console.error('提交评价失败：', error)
          wx.showToast({
            title: '评价失败，请重试',
            icon: 'error'
          })
        }
      }
    }
  })
  */
}
```

## 错误处理和降级方案

### 降级方案实现
```javascript
// 订单列表降级方案
loadOrdersFallback() {
  const userOrders = orderManager.getUserOrders()
  const mockOrders = [
    // 模拟订单数据...
  ]
  
  const allOrders = [...userOrders, ...mockOrders]
  this.setData({ orders: allOrders })
  this.filterOrders()
}

// 订单操作降级方案
updateOrderStatusLocal(orderId, status, statusText) {
  const orders = this.data.orders.map(order => {
    if (order.id === orderId) {
      return { ...order, status, statusText }
    }
    return order
  })
  
  this.setData({ orders })
  this.filterOrders()
}
```

## 注意事项

1. **状态同步**: 订单状态变更需要实时同步到列表显示
2. **权限控制**: 不同状态的订单有不同的操作权限
3. **退款处理**: 取消和提前结束需要处理退款逻辑
4. **实时更新**: 订单进度和剩余时间需要实时更新
5. **分页加载**: 支持订单列表的分页加载
6. **缓存策略**: 合理缓存订单数据，减少重复请求

## 测试建议

1. 测试订单列表的加载和筛选
2. 测试各种订单操作的功能
3. 测试订单状态变更的同步
4. 测试网络异常时的降级方案
5. 测试订单详情页面的跳转
6. 测试重新预订的流程
7. 测试作品集查看功能
