<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>我的作品集 - 逍遥境</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body { height: 852px; overflow: hidden; }
        .status-bar {
            background: black;
            color: white;
            height: 47px;
            font-size: 14px;
            font-weight: 600;
        }
        .content-area {
            height: calc(852px - 47px);
            overflow-y: auto;
        }
        .media-item {
            position: relative;
            aspect-ratio: 1;
            overflow: hidden;
            border-radius: 12px;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        .media-item:hover {
            transform: scale(1.05);
            box-shadow: 0 10px 25px rgba(0,0,0,0.2);
        }
        .video-overlay {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: rgba(0,0,0,0.7);
            color: white;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .media-info {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            background: linear-gradient(transparent, rgba(0,0,0,0.8));
            color: white;
            padding: 16px 12px 8px;
            font-size: 12px;
        }
        .filter-btn.active {
            background: #1f2937;
            color: white;
        }
        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.9);
            z-index: 1000;
        }
        .modal.show {
            display: flex;
            align-items: center;
            justify-content: center;
        }
    </style>
</head>
<body class="bg-gray-50">
    <!-- iOS 状态栏 -->
    <div class="status-bar flex justify-between items-center px-6">
        <div class="flex items-center space-x-1">
            <span>9:41</span>
        </div>
        <div class="flex items-center space-x-1">
            <i class="fas fa-signal text-xs"></i>
            <i class="fas fa-wifi text-xs"></i>
            <div class="w-6 h-3 border border-white rounded-sm">
                <div class="w-4 h-1 bg-white rounded-sm mt-0.5 ml-0.5"></div>
            </div>
        </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="content-area">
        <!-- 顶部导航 -->
        <div class="bg-white px-6 py-4 shadow-sm">
            <div class="flex items-center space-x-4">
                <button onclick="goBack()">
                    <i class="fas fa-chevron-left text-xl text-gray-600"></i>
                </button>
                <div class="flex-1">
                    <h1 class="text-xl font-semibold text-gray-800">我的作品集</h1>
                    <p class="text-sm text-gray-500">共156张作品</p>
                </div>
                <button onclick="selectMultiple()">
                    <i class="fas fa-check-circle text-xl text-gray-400"></i>
                </button>
            </div>
        </div>

        <!-- 筛选栏 -->
        <div class="bg-white px-6 py-4 border-b border-gray-100">
            <div class="flex space-x-3 overflow-x-auto">
                <button class="filter-btn active px-4 py-2 rounded-full text-sm whitespace-nowrap border border-gray-300" onclick="filterMedia('all')">
                    全部 (156)
                </button>
                <button class="filter-btn px-4 py-2 rounded-full text-sm whitespace-nowrap border border-gray-300" onclick="filterMedia('photo')">
                    照片 (132)
                </button>
                <button class="filter-btn px-4 py-2 rounded-full text-sm whitespace-nowrap border border-gray-300" onclick="filterMedia('video')">
                    视频 (24)
                </button>
                <button class="filter-btn px-4 py-2 rounded-full text-sm whitespace-nowrap border border-gray-300" onclick="filterMedia('recent')">
                    最近7天
                </button>
            </div>
        </div>

        <!-- 统计信息 -->
        <div class="px-6 py-4 bg-gradient-to-r from-blue-50 to-indigo-50">
            <div class="grid grid-cols-3 gap-4">
                <div class="text-center">
                    <div class="text-lg font-bold text-gray-800">23</div>
                    <div class="text-xs text-gray-500">拍摄天数</div>
                </div>
                <div class="text-center">
                    <div class="text-lg font-bold text-gray-800">1.2GB</div>
                    <div class="text-xs text-gray-500">存储空间</div>
                </div>
                <div class="text-center">
                    <div class="text-lg font-bold text-gray-800">48</div>
                    <div class="text-xs text-gray-500">分享次数</div>
                </div>
            </div>
        </div>

        <!-- 作品网格 -->
        <div class="p-4">
            <div class="grid grid-cols-3 gap-2">
                <!-- 照片作品 -->
                <div class="media-item" data-type="photo" onclick="viewMedia('photo1')">
                    <img src="https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=400&h=400&fit=crop" 
                         alt="航拍作品" class="w-full h-full object-cover">
                    <div class="media-info">
                        <div class="flex items-center justify-between">
                            <span><i class="fas fa-camera mr-1"></i>书圣故里</span>
                            <span>12-01</span>
                        </div>
                    </div>
                </div>

                <div class="media-item" data-type="video" onclick="viewMedia('video1')">
                    <img src="https://images.unsplash.com/photo-1558618047-3c8c76ca7d13?w=400&h=400&fit=crop" 
                         alt="航拍视频" class="w-full h-full object-cover">
                    <div class="video-overlay">
                        <i class="fas fa-play"></i>
                    </div>
                    <div class="media-info">
                        <div class="flex items-center justify-between">
                            <span><i class="fas fa-video mr-1"></i>鲁迅故里</span>
                            <span>11-30</span>
                        </div>
                    </div>
                </div>

                <div class="media-item" data-type="photo" onclick="viewMedia('photo2')">
                    <img src="https://images.unsplash.com/photo-1574958269340-fa927503f3dd?w=400&h=400&fit=crop" 
                         alt="航拍作品" class="w-full h-full object-cover">
                    <div class="media-info">
                        <div class="flex items-center justify-between">
                            <span><i class="fas fa-camera mr-1"></i>兰亭景区</span>
                            <span>11-28</span>
                        </div>
                    </div>
                </div>

                <div class="media-item" data-type="photo" onclick="viewMedia('photo3')">
                    <img src="https://images.unsplash.com/photo-1580993446442-c5b396fa4157?w=400&h=400&fit=crop" 
                         alt="航拍作品" class="w-full h-full object-cover">
                    <div class="media-info">
                        <div class="flex items-center justify-between">
                            <span><i class="fas fa-camera mr-1"></i>沈园</span>
                            <span>11-25</span>
                        </div>
                    </div>
                </div>

                <div class="media-item" data-type="video" onclick="viewMedia('video2')">
                    <img src="https://images.unsplash.com/photo-1473968512647-3e447244af8f?w=400&h=400&fit=crop" 
                         alt="航拍视频" class="w-full h-full object-cover">
                    <div class="video-overlay">
                        <i class="fas fa-play"></i>
                    </div>
                    <div class="media-info">
                        <div class="flex items-center justify-between">
                            <span><i class="fas fa-video mr-1"></i>东湖景区</span>
                            <span>11-22</span>
                        </div>
                    </div>
                </div>

                <div class="media-item" data-type="photo" onclick="viewMedia('photo4')">
                    <img src="https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=400&h=400&fit=crop" 
                         alt="航拍作品" class="w-full h-full object-cover">
                    <div class="media-info">
                        <div class="flex items-center justify-between">
                            <span><i class="fas fa-camera mr-1"></i>柯岩景区</span>
                            <span>11-20</span>
                        </div>
                    </div>
                </div>

                <div class="media-item" data-type="photo" onclick="viewMedia('photo5')">
                    <img src="https://images.unsplash.com/photo-1520150480169-4b71bde47f98?w=400&h=400&fit=crop" 
                         alt="航拍作品" class="w-full h-full object-cover">
                    <div class="media-info">
                        <div class="flex items-center justify-between">
                            <span><i class="fas fa-camera mr-1"></i>安昌古镇</span>
                            <span>11-18</span>
                        </div>
                    </div>
                </div>

                <div class="media-item" data-type="video" onclick="viewMedia('video3')">
                    <img src="https://images.unsplash.com/photo-1508614999368-9260051292e5?w=400&h=400&fit=crop" 
                         alt="航拍视频" class="w-full h-full object-cover">
                    <div class="video-overlay">
                        <i class="fas fa-play"></i>
                    </div>
                    <div class="media-info">
                        <div class="flex items-center justify-between">
                            <span><i class="fas fa-video mr-1"></i>会稽山</span>
                            <span>11-15</span>
                        </div>
                    </div>
                </div>

                <div class="media-item" data-type="photo" onclick="viewMedia('photo6')">
                    <img src="https://images.unsplash.com/photo-1469474968028-56623f02e42e?w=400&h=400&fit=crop" 
                         alt="航拍作品" class="w-full h-full object-cover">
                    <div class="media-info">
                        <div class="flex items-center justify-between">
                            <span><i class="fas fa-camera mr-1"></i>西湖景区</span>
                            <span>11-12</span>
                        </div>
                    </div>
                </div>

                <!-- 加载更多占位符 -->
                <div class="aspect-square bg-gray-200 rounded-xl flex items-center justify-center cursor-pointer" onclick="loadMore()">
                    <div class="text-center text-gray-500">
                        <i class="fas fa-plus text-2xl mb-2"></i>
                        <div class="text-xs">加载更多</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 媒体查看模态框 -->
    <div id="mediaModal" class="modal">
        <div class="relative w-full h-full flex items-center justify-center p-4">
            <button onclick="closeModal()" class="absolute top-8 right-8 z-10 text-white text-2xl">
                <i class="fas fa-times"></i>
            </button>
            
            <div class="max-w-full max-h-full">
                <img id="modalImage" src="" alt="" class="max-w-full max-h-full object-contain rounded-lg">
            </div>

            <!-- 底部操作栏 -->
            <div class="absolute bottom-8 left-1/2 transform -translate-x-1/2 bg-black bg-opacity-70 rounded-full px-6 py-3 flex items-center space-x-6 text-white">
                <button onclick="downloadMedia()">
                    <i class="fas fa-download text-xl"></i>
                </button>
                <button onclick="shareMedia()">
                    <i class="fas fa-share-alt text-xl"></i>
                </button>
                <button onclick="editMedia()">
                    <i class="fas fa-edit text-xl"></i>
                </button>
                <button onclick="deleteMedia()">
                    <i class="fas fa-trash text-xl"></i>
                </button>
            </div>

            <!-- 媒体信息 -->
            <div class="absolute bottom-24 left-8 text-white">
                <h3 id="modalTitle" class="text-lg font-semibold mb-2"></h3>
                <div id="modalInfo" class="text-sm space-y-1 opacity-80"></div>
            </div>
        </div>
    </div>

    <script>
        function goBack() {
            window.history.back();
        }

        function selectMultiple() {
            alert('多选模式\n• 点击作品进行选择\n• 支持批量下载、分享、删除');
        }

        function filterMedia(type) {
            // 更新按钮状态
            document.querySelectorAll('.filter-btn').forEach(btn => btn.classList.remove('active'));
            event.target.classList.add('active');

            // 筛选媒体项目
            document.querySelectorAll('.media-item').forEach(item => {
                if (type === 'all' || item.dataset.type === type || type === 'recent') {
                    item.style.display = 'block';
                } else {
                    item.style.display = 'none';
                }
            });
        }

        function viewMedia(mediaId) {
            const modal = document.getElementById('mediaModal');
            const modalImage = document.getElementById('modalImage');
            const modalTitle = document.getElementById('modalTitle');
            const modalInfo = document.getElementById('modalInfo');

            // 根据媒体ID设置不同的内容
            const mediaData = {
                'photo1': {
                    src: 'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=800&h=600&fit=crop',
                    title: '书圣故里 - 古典园林',
                    info: `
                        <div>拍摄时间: 2024-12-01 14:30</div>
                        <div>设备: DJI Air 3</div>
                        <div>分辨率: 4000×3000</div>
                        <div>文件大小: 2.5MB</div>
                    `
                },
                'video1': {
                    src: 'https://images.unsplash.com/photo-1558618047-3c8c76ca7d13?w=800&h=600&fit=crop',
                    title: '鲁迅故里 - 历史古镇',
                    info: `
                        <div>拍摄时间: 2024-11-30 09:15</div>
                        <div>设备: DJI Mini 4 Pro</div>
                        <div>分辨率: 4K/60fps</div>
                        <div>时长: 02:35</div>
                    `
                }
            };

            const data = mediaData[mediaId] || mediaData['photo1'];
            modalImage.src = data.src;
            modalTitle.textContent = data.title;
            modalInfo.innerHTML = data.info;
            
            modal.classList.add('show');
        }

        function closeModal() {
            document.getElementById('mediaModal').classList.remove('show');
        }

        function downloadMedia() {
            alert('下载成功！\n作品已保存到手机相册');
        }

        function shareMedia() {
            alert('分享选项\n• 微信好友\n• 朋友圈\n• QQ空间\n• 新浪微博\n• 复制链接');
        }

        function editMedia() {
            alert('编辑功能\n• 裁剪\n• 滤镜\n• 调色\n• 添加水印');
        }

        function deleteMedia() {
            if (confirm('确认删除这个作品？\n删除后不可恢复')) {
                alert('作品已删除');
                closeModal();
            }
        }

        function loadMore() {
            alert('加载更多作品...');
        }

        // 点击模态框外部关闭
        document.getElementById('mediaModal').addEventListener('click', function(e) {
            if (e.target === this) {
                closeModal();
            }
        });
    </script>
</body>
</html> 