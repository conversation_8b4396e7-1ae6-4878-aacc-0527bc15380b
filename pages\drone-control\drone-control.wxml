<!--pages/drone-control/drone-control.wxml-->
<view class="drone-control-container">
  <!-- 视频流区域 -->
  <view class="video-stream">
    <!-- 模拟航拍视频背景 -->
    <image
      class="video-background"
      src="http://iph.href.lu/1920x1080"
      mode="aspectFill"
    />

    <!-- 浮动云朵效果 -->
    <view class="floating-clouds"></view>

    <!-- HUD 覆盖层 -->
    <view class="hud-overlay">
      <!-- 十字准心 -->
      <view class="crosshair"></view>

      <!-- 顶部状态栏 -->
      <view class="top-status">
        <view class="status-left">
          <view class="exit-btn" bindtap="exitControl">
            <text class="exit-icon">✕</text>
            <text class="exit-text">退出</text>
          </view>
          <view class="signal-status">
            <text class="signal-icon">📶</text>
            <text class="signal-text">信号强度: {{flightData.signal.toFixed(0)}}%</text>
          </view>
        </view>
        <view class="status-right">
          <view class="battery-display">
            <text class="battery-icon">🔋</text>
            <view class="battery-bar">
              <view class="battery-fill" style="width: {{flightData.battery}}"></view>
            </view>
            <text class="battery-text">{{flightData.battery.toFixed(0)}}%</text>
          </view>
          <view class="flight-time-display">
            <text class="time-icon">🕐</text>
            <text class="time-text">{{formattedFlightTime}}</text>
          </view>
        </view>
      </view>

      <!-- 飞行参数显示 -->
      <view class="flight-params">
        <view class="param-item">
          <text class="param-label">高度:</text>
          <text class="param-value">{{flightData.altitude.toFixed(1)}}m</text>
        </view>
        <view class="param-item">
          <text class="param-label">速度:</text>
          <text class="param-value">{{flightData.speed.toFixed(1)}}km/h</text>
        </view>
        <view class="param-item">
          <text class="param-label">距离:</text>
          <text class="param-value">{{flightData.distance.toFixed(1)}}km</text>
        </view>
        <view class="param-item">
          <text class="param-label">风速:</text>
          <text class="param-value">{{flightData.windSpeed.toFixed(1)}}m/s</text>
        </view>
      </view>

      <!-- 相机设置 -->
      <!-- <view class="camera-settings">
        <view class="setting-item">
          <text class="setting-text">ISO: {{cameraSettings.iso}}</text>
        </view>
        <view class="setting-item">
          <text class="setting-text">快门: {{cameraSettings.shutter}}</text>
        </view>
        <view class="setting-item">
          <text class="setting-text">光圈: {{cameraSettings.aperture}}</text>
        </view>
        <view class="setting-item">
          <text class="setting-text">焦距: {{cameraSettings.focal}}</text>
        </view>
      </view> -->

      <!-- 录制状态指示 -->
      <view class="recording-indicator {{recording ? 'active' : ''}}" wx:if="{{recording}}">
        <text class="recording-dot">●</text>
        <text class="recording-text">正在录制</text>
      </view>
    </view>
  </view>

  <!-- 左侧操作杆 -->
  <view class="left-joystick-container">
    <view class="control-joystick" id="leftJoystick">
      <view class="joystick-handle"></view>
    </view>
  </view>

  <!-- 右侧操作杆 -->
  <view class="right-joystick-container">
    <view class="control-joystick" id="rightJoystick">
      <view class="joystick-handle"></view>
    </view>
  </view>

  <!-- 底部控制面板 -->
  <view class="bottom-control-panel">
    <!-- 统一的四个控制按钮 -->
    <view class="control-btn photo-btn" bindtap="takePhoto">
      <text class="control-icon">📷</text>
    </view>
    <view class="control-btn record-btn {{recording ? 'recording' : ''}}" bindtap="toggleRecording">
      <text class="control-icon">{{recording ? '⏹' : '🎥'}}</text>
    </view>
    <view class="control-btn return-btn" bindtap="returnHome">
      <text class="control-icon">🏠</text>
    </view>
    <view class="control-btn settings-btn" bindtap="showSettings">
      <text class="control-icon">⚙️</text>
    </view>
  </view>

  <!-- 侧边功能栏 -->
  <view class="side-controls">
    <view class="side-btn zoom-in-btn" bindtap="zoomIn">
      <text class="side-icon">+</text>
    </view>
    <view class="side-btn zoom-out-btn" bindtap="zoomOut">
      <text class="side-icon">-</text>
    </view>
    <view class="side-btn mode-btn" bindtap="changeMode">
      <text class="side-icon">👁</text>
    </view>
  </view>
</view>

<!-- 设置弹窗 -->
<view class="settings-modal" wx:if="{{showSettingsModal}}" bindtap="hideSettings">
  <view class="settings-content" catchtap="">
    <view class="settings-header">
      <text class="settings-title">相机设置</text>
      <text class="settings-close" bindtap="hideSettings">✕</text>
    </view>

    <view class="settings-list">
      <view class="setting-item">
        <text class="setting-label">ISO</text>
        <text class="setting-value">自动/手动</text>
      </view>

      <view class="setting-item">
        <text class="setting-label">白平衡</text>
        <text class="setting-value">自动</text>
      </view>

      <view class="setting-item">
        <text class="setting-label">曝光</text>
        <text class="setting-value">+0.3EV</text>
      </view>

      <view class="setting-item">
        <text class="setting-label">格式</text>
        <text class="setting-value">JPG+RAW</text>
      </view>

      <view class="setting-item">
        <text class="setting-label">分辨率</text>
        <text class="setting-value">4K/60fps</text>
      </view>
    </view>

    <view class="settings-actions">
      <button class="settings-confirm-btn" bindtap="hideSettings">确定</button>
    </view>
  </view>
</view>

