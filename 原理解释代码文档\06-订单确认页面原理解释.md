# 订单确认页面原理解释文档

## 页面概述
订单确认页面是整个租用流程的核心环节，用户在此页面确认设备信息、拍摄地点、选择租用时间、查看费用明细并最终提交订单。此页面承担着订单创建、费用计算、时间管理、用户余额验证等关键业务逻辑，是从浏览到交易转化的最重要页面。

## 文件结构
```
pages/order-confirm/
├── order-confirm.js      # 页面逻辑文件（412行）
├── order-confirm.wxml    # 页面结构文件（153行）
├── order-confirm.wxss    # 页面样式文件
└── order-confirm.json    # 页面配置文件
```

## 核心依赖模块
- `utils/auth.js` - 认证工具模块，提供登录状态检查和用户权限验证
- `utils/mockData.js` - 模拟数据模块，提供设备数据（mockEquipment）和地点数据（mockLocations）
- `utils/orderManager.js` - 订单管理模块，提供订单创建、状态管理、费用计算等核心业务逻辑
- `app.js` - 全局应用实例，提供用户信息和全局状态管理

## 页面数据结构详解

### data 对象分析（第12-44行）
```javascript
data: {
  // 订单信息
  equipment: null,
  location: null,

  // 时间选择
  startDate: '',
  selectedTimeSlot: '',
  selectedDuration: 1,
  timeSlots: [
    { label: '09:00', value: '09:00', available: true },
    { label: '10:30', value: '10:30', available: true },
    { label: '13:00', value: '13:00', available: true },
    { label: '14:00', value: '14:00', available: false },
    { label: '16:00', value: '16:00', available: true },
    { label: '18:00', value: '18:00', available: true }
  ],
  durationOptions: [1, 2, 3, 4],

  // 费用计算
  serviceFee: 10,
  insuranceFee: 5,
  totalFee: 0,

  // 用户信息
  userInfo: { balance: 150 },

  // 页面状态
  loading: false,
  submitting: false,
  canSubmit: false,
  submitButtonText: '请选择时间和时长'
}
```

**数据字段详细说明**：

### 1. 订单信息相关

#### equipment（设备信息）
- **作用**：存储用户选择的设备完整信息，用于订单创建和页面显示
- **默认值**：null（页面初始化时为空）
- **数据来源**：如果从地点选择页面跳转，那么根据URL参数中的equipmentId从mockEquipment中查找对应设备
- **数据结构**：如果有设备数据，那么包含id、name、price、description、image、specs等完整字段
- **重要性**：如果设备信息缺失，那么无法进行订单确认和费用计算
- **使用场景**：
  - **如果**：需要显示设备信息，那么在设备信息卡片中展示
  - **如果**：需要计算费用，那么使用equipment.price作为基础价格
  - **如果**：需要创建订单，那么使用equipment.id作为设备标识

#### location（地点信息）
- **作用**：存储用户选择的拍摄地点完整信息，用于订单创建和页面显示
- **默认值**：null（页面初始化时为空）
- **数据来源**：如果从地点选择页面跳转，那么根据URL参数中的locationId从mockLocations中查找对应地点
- **数据结构**：如果有地点数据，那么包含id、name、address、distance、rating、image、description等完整字段
- **重要性**：如果地点信息缺失，那么无法确定拍摄位置和相关费用
- **使用场景**：
  - **如果**：需要显示地点信息，那么在地点信息卡片中展示
  - **如果**：需要计算距离相关费用，那么使用location.distance
  - **如果**：需要创建订单，那么使用location.id作为地点标识

### 2. 时间选择相关

#### startDate（开始日期）
- **作用**：存储用户选择的租用开始日期
- **默认值**：空字符串''（页面初始化时为空）
- **数据来源**：如果用户点击日期选择器，那么通过onDateChange方法更新此字段
- **格式要求**：使用YYYY-MM-DD格式，符合微信小程序picker组件要求
- **验证逻辑**：如果选择的日期早于当前日期，那么需要提示用户重新选择
- **业务规则**：
  - **如果**：用户未选择日期，那么不能提交订单
  - **如果**：选择的是今天，那么需要检查时间段是否已过
  - **如果**：选择的是未来日期，那么所有时间段都可用

#### selectedTimeSlot（选择的时间段）
- **作用**：存储用户选择的具体时间段
- **默认值**：空字符串''（页面初始化时为空）
- **数据来源**：如果用户点击时间段选项，那么通过selectTimeSlot方法更新此字段
- **格式说明**：使用HH:MM格式，如'09:00'、'14:00'等
- **可用性检查**：如果时间段不可用，那么用户无法选择该时间段
- **业务逻辑**：
  - **如果**：用户未选择时间段，那么不能提交订单
  - **如果**：选择的时间段已被预约，那么需要提示用户选择其他时间
  - **如果**：时间段可用，那么更新UI状态并重新计算费用

#### selectedDuration（选择的时长）
- **作用**：存储用户选择的租用时长（小时数）
- **默认值**：1（默认租用1小时）
- **数据来源**：如果用户选择时长选项，那么通过selectDuration方法更新此字段
- **取值范围**：1-4小时，对应durationOptions数组
- **费用影响**：如果时长变化，那么总费用需要重新计算
- **业务规则**：
  - **如果**：时长为1小时，那么按基础价格计算
  - **如果**：时长超过1小时，那么按小时数乘以基础价格计算
  - **如果**：时长过长，那么可能需要额外的保险费用

#### timeSlots（时间段选项）
- **作用**：存储所有可选的时间段及其可用状态
- **默认值**：预设的6个时间段，包含上午、下午、傍晚时段
- **数据结构**：每个时间段包含label（显示文字）、value（实际值）、available（是否可用）
- **动态更新**：如果日期变化，那么需要重新检查各时间段的可用性
- **可用性逻辑**：
  - **如果**：选择今天且当前时间已过该时间段，那么设为不可用
  - **如果**：该时间段已有其他订单，那么设为不可用
  - **如果**：设备在该时间段维护，那么设为不可用
- **时间段说明**：
  - **09:00**：早晨时段，适合拍摄日出和清晨景色
  - **10:30**：上午时段，光线充足，适合大部分拍摄需求
  - **13:00**：中午时段，阳光强烈，适合特定拍摄效果
  - **14:00**：下午时段，通常为热门时段，容易被预约
  - **16:00**：傍晚前时段，光线柔和，适合人像拍摄
  - **18:00**：傍晚时段，适合拍摄日落和黄昏景色

#### durationOptions（时长选项）
- **作用**：存储所有可选的租用时长选项
- **默认值**：[1, 2, 3, 4]，表示1-4小时的选项
- **选择逻辑**：如果用户需要更长时间，那么可以选择更大的数值
- **费用计算**：如果选择不同时长，那么费用按小时数线性增长
- **业务限制**：
  - **如果**：选择1小时，那么适合简单的拍摄任务
  - **如果**：选择2-3小时，那么适合中等复杂度的拍摄项目
  - **如果**：选择4小时，那么适合复杂的专业拍摄项目
  - **如果**：需要超过4小时，那么建议分多次预订

### 3. 费用计算相关

#### serviceFee（服务费）
- **作用**：存储固定的服务费用
- **默认值**：10元（固定费用）
- **费用说明**：包含设备检查、技术支持、客服服务等
- **计算逻辑**：如果有订单，那么固定收取10元服务费
- **业务价值**：
  - **如果**：用户需要技术指导，那么服务费包含专业指导
  - **如果**：设备出现问题，那么服务费包含技术支持
  - **如果**：需要紧急联系，那么服务费包含24小时客服

#### insuranceFee（保险费）
- **作用**：存储设备保险费用
- **默认值**：5元（固定费用）
- **费用说明**：包含设备损坏保险、意外事故保险等
- **计算逻辑**：如果有订单，那么固定收取5元保险费
- **保险范围**：
  - **如果**：设备意外损坏，那么保险承担维修费用
  - **如果**：设备丢失，那么保险承担部分赔偿
  - **如果**：第三方责任，那么保险提供基础保障

#### totalFee（总费用）
- **作用**：存储计算后的订单总费用
- **默认值**：0（页面初始化时为0）
- **计算公式**：设备费用 × 时长 + 服务费 + 保险费
- **更新时机**：如果设备、时长、时间段任一项变化，那么重新计算总费用
- **计算逻辑详解**：
  - **基础费用**：如果选择设备，那么使用equipment.price作为每小时费用
  - **时长费用**：如果选择时长，那么基础费用 × selectedDuration
  - **附加费用**：如果有订单，那么加上serviceFee + insuranceFee
  - **最终费用**：totalFee = (equipment.price × selectedDuration) + serviceFee + insuranceFee

### 4. 用户信息相关

#### userInfo（用户信息）
- **作用**：存储当前用户的基本信息和账户状态
- **默认值**：{ balance: 150 }（模拟用户余额150元）
- **数据来源**：如果页面加载，那么从全局应用状态或本地存储获取用户信息
- **关键字段**：
  - **balance**：用户账户余额，用于支付验证
  - **其他字段**：可能包含用户名、头像、会员等级等信息
- **余额验证逻辑**：
  - **如果**：用户余额充足（balance >= totalFee），那么可以提交订单
  - **如果**：用户余额不足（balance < totalFee），那么需要提示充值
  - **如果**：用户未登录，那么需要先进行身份验证

### 5. 页面状态相关

#### loading（页面加载状态）
- **作用**：控制页面数据加载时的loading状态显示
- **默认值**：false（页面初始化时不显示loading）
- **设为true的时机**：如果开始加载设备或地点数据，那么显示loading状态
- **设为false的时机**：如果数据加载完成（成功或失败），那么隐藏loading状态
- **用户体验**：如果数据正在加载，那么显示loading动画，避免用户看到不完整的信息

#### submitting（提交状态）
- **作用**：控制订单提交过程中的状态显示
- **默认值**：false（页面初始化时不在提交状态）
- **设为true的时机**：如果用户点击提交订单按钮，那么设为true防止重复提交
- **设为false的时机**：如果订单提交完成（成功或失败），那么恢复为false
- **防重复提交**：如果正在提交，那么禁用提交按钮，避免重复订单

#### canSubmit（可提交状态）
- **作用**：控制是否允许用户提交订单
- **默认值**：false（页面初始化时不允许提交）
- **设为true的条件**：
  - **如果**：设备信息完整，那么检查通过
  - **如果**：地点信息完整，那么检查通过
  - **如果**：选择了有效日期，那么检查通过
  - **如果**：选择了可用时间段，那么检查通过
  - **如果**：选择了有效时长，那么检查通过
  - **如果**：用户余额充足，那么检查通过
- **设为false的条件**：如果以上任一条件不满足，那么不允许提交

#### submitButtonText（提交按钮文字）
- **作用**：动态显示提交按钮的文字内容
- **默认值**：'请选择时间和时长'（提示用户需要完成的操作）
- **动态更新逻辑**：
  - **如果**：未选择时间和时长，那么显示'请选择时间和时长'
  - **如果**：余额不足，那么显示'余额不足，请充值'
  - **如果**：正在提交，那么显示'提交中...'
  - **如果**：可以提交，那么显示'确认订单 ¥XX'（包含总金额）
- **用户引导**：如果按钮文字明确，那么用户知道下一步需要做什么

## 页面生命周期详解

### 1. onLoad 生命周期（第49-55行）
```javascript
onLoad(options) {
  console.log('订单确认页面加载', options)

  this.checkAuth()
  this.loadOrderInfo(options)
  this.initDateTime()
}
```

**详细执行逻辑**：

1. **参数获取和日志记录**：
   - **参数来源**：如果用户从地点选择页面选择地点，那么通过URL参数传递设备和地点信息
   - **参数内容**：equipmentId、locationId、equipmentName、locationName等关键信息
   - **日志记录**：记录页面加载事件和传递的参数，便于开发调试和问题排查
   - **如果**：在开发环境，那么可以通过控制台查看完整的参数传递情况

2. **认证状态检查**：
   - **调用**：this.checkAuth()方法
   - **如果**：用户未登录，那么跳转到登录页面
   - **如果**：用户已登录，那么继续执行后续步骤
   - **重要性**：订单确认页面涉及支付和个人信息，必须确保用户身份验证
   - **安全考虑**：如果用户身份验证失败，那么不能访问订单相关功能

3. **订单信息加载**：
   - **调用**：this.loadOrderInfo(options)方法
   - **如果**：认证通过，那么开始加载设备和地点的详细信息
   - **数据处理**：根据传递的ID获取完整的设备和地点数据
   - **错误处理**：如果数据加载失败，那么需要提示用户并提供解决方案

4. **日期时间初始化**：
   - **调用**：this.initDateTime()方法
   - **如果**：基础数据加载完成，那么初始化日期选择器的默认值
   - **默认设置**：通常设置为当前日期或下一个工作日
   - **时间段检查**：如果是当天，那么需要检查哪些时间段已过期

## 核心功能详解

### 1. 认证状态检查（第107-113行）
```javascript
checkAuth() {
  if (!auth.checkLoginStatus()) {
    auth.redirectToLogin('/pages/order-confirm/order-confirm')
    return false
  }
  return true
}
```

**详细执行逻辑**：

1. **登录状态检查**：
   - **调用**：auth.checkLoginStatus()方法
   - **检查内容**：本地存储中的登录状态、token有效性、用户信息完整性、会话过期时间
   - **如果**：用户未登录或token过期，那么返回false
   - **如果**：用户已登录且token有效，那么返回true
   - **安全验证**：如果token格式错误或被篡改，那么也会返回false

2. **未登录处理**：
   - **如果**：检查结果为false，那么执行跳转到登录页
   - **重定向参数**：传递当前页面路径，但不包含订单参数（因为可能包含敏感信息）
   - **目的**：用户登录成功后能够回到订单确认页面，但需要重新选择设备和地点
   - **返回值**：返回false，告知调用方认证失败，停止后续操作

3. **已登录处理**：
   - **如果**：检查结果为true，那么直接返回true
   - **后续操作**：调用方可以继续执行需要登录的操作，如加载用户信息、创建订单等
   - **权限验证**：如果用户有特殊权限限制，那么在此处进行额外检查

### 2. 订单信息加载（第118-158行）
```javascript
async loadOrderInfo(options) {
  if (!this.checkAuth()) return

  this.setData({ loading: true })

  try {
    const { equipmentId, locationId, equipmentName, locationName } = options

    // 验证必要参数
    if (!equipmentId || !locationId) {
      throw new Error('缺少必要的订单参数')
    }

    // 查找设备信息
    const equipment = mockEquipment.find(item => item.id === parseInt(equipmentId))
    if (!equipment) {
      throw new Error('设备信息不存在')
    }

    // 查找地点信息
    const location = mockLocations.find(item => item.id === parseInt(locationId))
    if (!location) {
      throw new Error('地点信息不存在')
    }

    // 更新页面数据
    this.setData({
      equipment,
      location
    })

    // 初始化费用计算
    this.calculateTotalFee()

    console.log('订单信息加载完成', { equipment: equipment.name, location: location.name })

  } catch (error) {
    console.error('加载订单信息失败：', error)
    wx.showModal({
      title: '加载失败',
      content: error.message || '订单信息加载失败，请重新选择',
      confirmText: '重新选择',
      success: (res) => {
        if (res.confirm) {
          wx.switchTab({ url: '/pages/equipment/equipment' })
        }
      }
    })
  } finally {
    this.setData({ loading: false })
  }
}
```

**详细执行逻辑**：

1. **认证检查**：
   - **如果**：用户未通过认证，那么直接返回，不执行数据加载
   - **目的**：避免未登录用户加载订单信息造成安全问题

2. **loading状态设置**：
   - **如果**：开始加载数据，那么设置loading为true
   - **用户体验**：显示loading动画，告知用户正在加载订单信息

3. **参数验证**：
   - **参数提取**：从options中提取equipmentId、locationId、equipmentName、locationName
   - **必要参数检查**：如果equipmentId或locationId缺失，那么抛出错误
   - **参数完整性**：如果参数不完整，那么无法创建有效订单

4. **设备信息查找**：
   - **数据查找**：从mockEquipment数组中查找对应的设备信息
   - **ID匹配**：使用parseInt()确保ID类型匹配（URL参数为字符串，数据中为数字）
   - **如果**：找不到对应设备，那么抛出"设备信息不存在"错误
   - **数据完整性**：确保获取到的设备信息包含所有必要字段

5. **地点信息查找**：
   - **数据查找**：从mockLocations数组中查找对应的地点信息
   - **ID匹配**：同样使用parseInt()确保ID类型匹配
   - **如果**：找不到对应地点，那么抛出"地点信息不存在"错误
   - **数据完整性**：确保获取到的地点信息包含所有必要字段

6. **页面数据更新**：
   - **如果**：设备和地点信息都获取成功，那么更新页面data
   - **数据绑定**：更新后的数据会自动反映到页面UI中
   - **状态同步**：确保页面显示的信息与实际数据一致

7. **费用计算初始化**：
   - **调用**：this.calculateTotalFee()方法
   - **如果**：设备信息已加载，那么可以进行初始费用计算
   - **计算基础**：使用设备价格和默认时长计算初始总费用

8. **成功处理**：
   - **如果**：数据加载成功，那么记录设备和地点名称到控制台
   - **日志信息**：便于开发调试，确认数据加载情况

9. **错误处理**：
   - **如果**：任何步骤出现错误，那么显示错误提示模态框
   - **错误信息**：显示具体的错误原因，帮助用户理解问题
   - **用户引导**：提供"重新选择"选项，引导用户回到设备选择页面
   - **降级处理**：如果用户取消，那么停留在当前页面，但功能受限

10. **状态清理**：
    - **loading状态**：无论成功失败都设为false
    - **用户体验**：确保loading状态能够正确结束

### 3. 日期时间初始化（第163-175行）
```javascript
initDateTime() {
  const today = new Date()
  const tomorrow = new Date(today.getTime() + 24 * 60 * 60 * 1000)

  // 设置默认日期为明天
  const defaultDate = tomorrow.toISOString().split('T')[0]

  this.setData({
    startDate: defaultDate
  })

  // 更新时间段可用性
  this.updateTimeSlotAvailability()

  console.log('日期时间初始化完成，默认日期：', defaultDate)
}
```

**详细执行逻辑**：

1. **当前时间获取**：
   - **如果**：需要设置默认日期，那么首先获取当前时间
   - **时间对象**：创建Date对象表示当前时间

2. **明天日期计算**：
   - **时间计算**：当前时间 + 24小时（24 * 60 * 60 * 1000毫秒）
   - **如果**：当前时间较晚，那么默认选择明天，避免时间段冲突
   - **业务考虑**：给用户充足的准备时间，避免当天匆忙预订

3. **日期格式转换**：
   - **ISO格式**：使用toISOString()获取标准时间格式
   - **日期提取**：使用split('T')[0]只保留日期部分（YYYY-MM-DD）
   - **格式兼容**：确保格式符合微信小程序picker组件要求

4. **默认日期设置**：
   - **如果**：计算出默认日期，那么更新startDate字段
   - **页面响应**：日期选择器会显示默认选中的日期

5. **时间段可用性更新**：
   - **调用**：this.updateTimeSlotAvailability()方法
   - **如果**：日期已设置，那么检查该日期下各时间段的可用性
   - **动态更新**：根据选择的日期更新时间段的可用状态

6. **初始化完成日志**：
   - **如果**：初始化成功，那么记录默认日期到控制台
   - **调试信息**：便于开发时确认初始化是否正确

### 4. 时间段可用性更新（第180-205行）
```javascript
updateTimeSlotAvailability() {
  const selectedDate = this.data.startDate
  if (!selectedDate) return

  const today = new Date().toISOString().split('T')[0]
  const currentHour = new Date().getHours()

  const updatedTimeSlots = this.data.timeSlots.map(slot => {
    let available = true

    // 如果是今天，检查时间是否已过
    if (selectedDate === today) {
      const slotHour = parseInt(slot.value.split(':')[0])
      if (slotHour <= currentHour) {
        available = false
      }
    }

    // 这里可以添加更多的可用性检查逻辑
    // 比如检查该时间段是否已被预订

    return { ...slot, available }
  })

  this.setData({ timeSlots: updatedTimeSlots })

  console.log('时间段可用性更新完成', selectedDate)
}
```

**详细执行逻辑**：

1. **日期验证**：
   - **如果**：未选择日期（startDate为空），那么直接返回，不进行更新
   - **前置条件**：必须先选择日期才能检查时间段可用性

2. **当前时间信息获取**：
   - **今天日期**：获取当前日期的YYYY-MM-DD格式
   - **当前小时**：获取当前时间的小时数，用于判断时间段是否已过

3. **时间段遍历更新**：
   - **遍历处理**：对每个时间段进行可用性检查
   - **默认状态**：初始设置available为true

4. **当天时间检查**：
   - **如果**：选择的是今天，那么需要检查时间是否已过
   - **时间提取**：从slot.value（如'09:00'）中提取小时数
   - **时间比较**：如果时间段的小时数小于等于当前小时，那么设为不可用
   - **业务逻辑**：避免用户选择已经过去的时间段

5. **扩展检查逻辑**：
   - **预订冲突**：如果该时间段已被其他用户预订，那么设为不可用
   - **设备维护**：如果设备在该时间段需要维护，那么设为不可用
   - **天气因素**：如果天气不适合拍摄，那么可以设为不可用
   - **节假日调整**：如果是特殊节假日，那么可能有不同的可用性规则

6. **数据更新**：
   - **对象复制**：使用扩展运算符{...slot, available}创建新对象
   - **状态更新**：将更新后的时间段数组设置到页面data中
   - **UI响应**：页面会根据新的可用性状态更新时间段显示

7. **更新完成日志**：
   - **如果**：更新完成，那么记录选择的日期到控制台
   - **调试信息**：便于开发时确认更新是否正确执行

### 5. 日期选择处理（第210-220行）
```javascript
onDateChange(e) {
  const selectedDate = e.detail.value
  console.log('选择日期：', selectedDate)

  this.setData({
    startDate: selectedDate,
    selectedTimeSlot: '' // 重置时间段选择
  })

  // 更新时间段可用性
  this.updateTimeSlotAvailability()

  // 重新计算费用和检查提交状态
  this.calculateTotalFee()
  this.checkSubmitStatus()
}
```

**详细执行逻辑**：

1. **日期获取**：
   - **如果**：用户在日期选择器中选择日期，那么从事件对象获取选择的日期值
   - **数据格式**：e.detail.value包含YYYY-MM-DD格式的日期字符串

2. **日期记录**：
   - **日志输出**：记录用户选择的日期，便于调试和用户行为分析
   - **如果**：在开发环境，那么可以通过控制台查看用户的选择行为

3. **状态更新**：
   - **日期更新**：将选择的日期设置到startDate字段
   - **时间段重置**：将selectedTimeSlot重置为空字符串
   - **重置原因**：如果日期变化，那么之前选择的时间段可能不再可用，需要用户重新选择

4. **时间段可用性更新**：
   - **调用**：this.updateTimeSlotAvailability()方法
   - **如果**：日期已更新，那么重新检查各时间段在新日期下的可用性
   - **动态调整**：确保用户看到的时间段状态是准确的

5. **费用重新计算**：
   - **调用**：this.calculateTotalFee()方法
   - **如果**：日期变化可能影响费用（如节假日加价），那么重新计算总费用
   - **费用更新**：确保显示的费用信息是最新的

6. **提交状态检查**：
   - **调用**：this.checkSubmitStatus()方法
   - **如果**：日期选择后时间段被重置，那么可能影响提交按钮的可用状态
   - **状态同步**：确保提交按钮状态与当前选择状态一致

### 6. 时间段选择处理（第225-250行）
```javascript
selectTimeSlot(e) {
  const { time, available } = e.currentTarget.dataset

  if (!available) {
    wx.showToast({
      title: '该时间段不可用',
      icon: 'none'
    })
    return
  }

  console.log('选择时间段：', time)

  this.setData({
    selectedTimeSlot: time
  })

  // 重新计算费用和检查提交状态
  this.calculateTotalFee()
  this.checkSubmitStatus()
}
```

**详细执行逻辑**：

1. **时间段信息获取**：
   - **数据提取**：从事件对象的dataset中获取time（时间值）和available（可用状态）
   - **如果**：用户点击时间段选项，那么获取该时间段的完整信息

2. **可用性检查**：
   - **如果**：时间段不可用（available为false），那么显示提示信息并返回
   - **用户反馈**：显示"该时间段不可用"的toast提示
   - **操作阻止**：不允许选择不可用的时间段，保护业务逻辑

3. **选择确认**：
   - **如果**：时间段可用，那么记录用户选择的时间段
   - **日志输出**：便于调试和用户行为分析

4. **状态更新**：
   - **时间段设置**：将选择的时间段设置到selectedTimeSlot字段
   - **UI响应**：页面会高亮显示选中的时间段

5. **费用重新计算**：
   - **调用**：this.calculateTotalFee()方法
   - **如果**：时间段选择完成，那么可以进行完整的费用计算
   - **费用确定**：基于设备、时长、时间段计算最终费用

6. **提交状态检查**：
   - **调用**：this.checkSubmitStatus()方法
   - **如果**：时间段已选择，那么检查是否满足提交订单的所有条件
   - **按钮状态**：更新提交按钮的可用状态和显示文字

### 7. 时长选择处理（第255-270行）
```javascript
selectDuration(e) {
  const duration = parseInt(e.currentTarget.dataset.duration)
  console.log('选择时长：', duration, '小时')

  this.setData({
    selectedDuration: duration
  })

  // 重新计算费用和检查提交状态
  this.calculateTotalFee()
  this.checkSubmitStatus()
}
```

**详细执行逻辑**：

1. **时长信息获取**：
   - **数据提取**：从事件对象的dataset中获取duration值
   - **类型转换**：使用parseInt()将字符串转换为数字类型
   - **如果**：用户点击时长选项，那么获取对应的小时数

2. **选择确认**：
   - **日志输出**：记录用户选择的时长，便于调试和分析
   - **格式化显示**：在日志中显示"X小时"的友好格式

3. **状态更新**：
   - **时长设置**：将选择的时长设置到selectedDuration字段
   - **UI响应**：页面会高亮显示选中的时长选项

4. **费用重新计算**：
   - **调用**：this.calculateTotalFee()方法
   - **如果**：时长变化，那么总费用需要重新计算
   - **计算影响**：时长直接影响设备租用费用的计算

5. **提交状态检查**：
   - **调用**：this.checkSubmitStatus()方法
   - **如果**：时长已选择，那么检查是否满足提交订单的所有条件
   - **状态更新**：更新提交按钮的可用状态

### 8. 费用计算功能（第275-295行）
```javascript
calculateTotalFee() {
  const { equipment, selectedDuration, serviceFee, insuranceFee } = this.data

  if (!equipment || !selectedDuration) {
    this.setData({ totalFee: 0 })
    return
  }

  // 计算设备费用
  const equipmentFee = equipment.price * selectedDuration

  // 计算总费用
  const totalFee = equipmentFee + serviceFee + insuranceFee

  this.setData({ totalFee })

  console.log('费用计算：', {
    设备费用: equipmentFee,
    服务费: serviceFee,
    保险费: insuranceFee,
    总费用: totalFee
  })
}
```

**详细执行逻辑**：

1. **数据获取**：
   - **解构赋值**：从页面data中获取计算费用所需的所有数据
   - **必要字段**：equipment（设备信息）、selectedDuration（时长）、serviceFee（服务费）、insuranceFee（保险费）

2. **前置条件检查**：
   - **如果**：设备信息缺失或未选择时长，那么将总费用设为0并返回
   - **逻辑保护**：避免在数据不完整时进行错误计算

3. **设备费用计算**：
   - **计算公式**：设备费用 = 设备单价 × 租用时长
   - **如果**：设备单价为100元/小时，时长为2小时，那么设备费用为200元
   - **价格来源**：使用equipment.price作为每小时的租用价格

4. **总费用计算**：
   - **计算公式**：总费用 = 设备费用 + 服务费 + 保险费
   - **固定费用**：服务费和保险费为固定金额，不随时长变化
   - **如果**：设备费用200元，服务费10元，保险费5元，那么总费用为215元

5. **费用更新**：
   - **状态设置**：将计算出的总费用设置到页面data中
   - **UI响应**：页面会自动更新显示的费用信息

6. **费用明细日志**：
   - **详细记录**：记录各项费用的具体金额和总费用
   - **调试信息**：便于开发时验证费用计算是否正确
   - **格式化显示**：使用中文字段名，便于理解

### 9. 提交状态检查（第300-330行）
```javascript
checkSubmitStatus() {
  const { equipment, location, startDate, selectedTimeSlot, selectedDuration, totalFee, userInfo, submitting } = this.data

  let canSubmit = false
  let submitButtonText = '请选择时间和时长'

  // 检查基本信息
  if (!equipment || !location) {
    submitButtonText = '订单信息不完整'
  }
  // 检查时间选择
  else if (!startDate || !selectedTimeSlot || !selectedDuration) {
    submitButtonText = '请选择时间和时长'
  }
  // 检查余额
  else if (userInfo.balance < totalFee) {
    submitButtonText = '余额不足，请充值'
  }
  // 检查提交状态
  else if (submitting) {
    submitButtonText = '提交中...'
  }
  // 可以提交
  else {
    canSubmit = true
    submitButtonText = `确认订单 ¥${totalFee}`
  }

  this.setData({
    canSubmit,
    submitButtonText
  })

  console.log('提交状态检查：', { canSubmit, submitButtonText })
}
```

**详细执行逻辑**：

1. **数据获取**：
   - **解构赋值**：获取检查提交状态所需的所有关键数据
   - **检查字段**：设备、地点、日期、时间段、时长、总费用、用户信息、提交状态

2. **状态初始化**：
   - **默认状态**：canSubmit设为false，不允许提交
   - **默认文字**：submitButtonText设为提示用户选择时间和时长

3. **基本信息检查**：
   - **如果**：设备或地点信息缺失，那么显示"订单信息不完整"
   - **业务逻辑**：没有设备和地点就无法创建有效订单
   - **用户引导**：明确告知用户问题所在

4. **时间选择检查**：
   - **如果**：日期、时间段或时长任一项未选择，那么显示"请选择时间和时长"
   - **完整性要求**：必须选择完整的时间信息才能确定租用安排
   - **用户提示**：引导用户完成必要的选择

5. **余额检查**：
   - **如果**：用户余额小于总费用，那么显示"余额不足，请充值"
   - **支付验证**：确保用户有足够余额支付订单
   - **解决方案**：提示用户需要充值，提供明确的解决方向

6. **提交状态检查**：
   - **如果**：正在提交订单，那么显示"提交中..."
   - **防重复提交**：避免用户多次点击造成重复订单
   - **状态反馈**：明确告知用户订单正在处理中

7. **可提交状态**：
   - **如果**：所有条件都满足，那么设置canSubmit为true
   - **按钮文字**：显示"确认订单 ¥XX"，包含具体金额
   - **用户确认**：让用户清楚知道即将支付的金额

8. **状态更新**：
   - **数据设置**：将检查结果更新到页面data中
   - **UI响应**：提交按钮会根据新状态更新样式和文字

9. **检查结果日志**：
   - **状态记录**：记录检查结果，便于调试
   - **如果**：在开发环境，那么可以通过控制台查看状态变化

### 10. 订单提交功能（第335-380行）
```javascript
async submitOrder() {
  if (!this.data.canSubmit || this.data.submitting) {
    return
  }

  this.setData({ submitting: true })
  this.checkSubmitStatus() // 更新按钮状态

  try {
    const { equipment, location, startDate, selectedTimeSlot, selectedDuration, totalFee, userInfo } = this.data

    // 构建订单数据
    const orderData = {
      equipmentId: equipment.id,
      equipmentName: equipment.name,
      locationId: location.id,
      locationName: location.name,
      startDate,
      timeSlot: selectedTimeSlot,
      duration: selectedDuration,
      totalFee,
      status: 'confirmed',
      createTime: new Date().toISOString()
    }

    // 创建订单
    const orderId = await orderManager.createOrder(orderData)

    // 扣除用户余额
    const newBalance = userInfo.balance - totalFee
    this.setData({
      'userInfo.balance': newBalance
    })

    console.log('订单创建成功：', orderId, orderData)

    // 显示成功提示
    wx.showToast({
      title: '订单创建成功',
      icon: 'success',
      duration: 2000
    })

    // 延迟跳转到订单详情页
    setTimeout(() => {
      wx.redirectTo({
        url: `/pages/order-detail/order-detail?orderId=${orderId}`
      })
    }, 2000)

  } catch (error) {
    console.error('订单提交失败：', error)
    wx.showModal({
      title: '提交失败',
      content: error.message || '订单提交失败，请重试',
      confirmText: '重试',
      cancelText: '取消'
    })
  } finally {
    this.setData({ submitting: false })
    this.checkSubmitStatus() // 恢复按钮状态
  }
}
```

**详细执行逻辑**：

1. **提交前检查**：
   - **如果**：不满足提交条件或正在提交，那么直接返回
   - **双重保护**：防止在不合适的状态下执行提交操作

2. **提交状态设置**：
   - **状态更新**：设置submitting为true，表示开始提交
   - **按钮更新**：调用checkSubmitStatus()更新按钮显示为"提交中..."

3. **订单数据构建**：
   - **数据收集**：从页面data中收集所有订单相关信息
   - **订单对象**：构建包含完整订单信息的数据对象
   - **时间戳**：添加createTime字段记录订单创建时间
   - **状态设置**：设置订单状态为'confirmed'（已确认）

4. **订单创建**：
   - **调用**：orderManager.createOrder(orderData)方法
   - **如果**：订单数据有效，那么在系统中创建新订单
   - **返回值**：获取创建成功的订单ID

5. **余额扣除**：
   - **余额计算**：新余额 = 当前余额 - 订单总费用
   - **状态更新**：更新用户信息中的余额字段
   - **数据同步**：确保页面显示的余额是最新的

6. **成功处理**：
   - **日志记录**：记录订单创建成功的信息，包含订单ID和完整数据
   - **用户反馈**：显示"订单创建成功"的toast提示
   - **持续时间**：toast显示2秒，给用户足够的反馈时间

7. **页面跳转**：
   - **延迟跳转**：等待2秒后跳转到订单详情页
   - **跳转方式**：使用wx.redirectTo()替换当前页面，避免用户返回到订单确认页
   - **参数传递**：将订单ID作为参数传递给订单详情页

8. **错误处理**：
   - **如果**：订单提交过程中出现错误，那么显示错误提示
   - **错误信息**：显示具体的错误原因或通用的重试提示
   - **用户选择**：提供"重试"和"取消"选项，让用户决定下一步操作

9. **状态恢复**：
   - **提交状态**：无论成功失败都将submitting设为false
   - **按钮恢复**：调用checkSubmitStatus()恢复按钮的正常状态
   - **用户体验**：确保用户可以继续操作或重新提交

## WXML结构详解

### 1. 页面整体结构（第2行）
```xml
<view class="confirm-container">
  <!-- 页面内容 -->
</view>
```

**结构说明**：
- **confirm-container**：整个订单确认页面的容器
- **如果**：页面需要统一的背景和布局，那么使用此容器

### 2. 重新预订提示区域（第3-10行）
```xml
<view class="reorder-tip" wx:if="{{isReorder}}">
  <view class="tip-icon">🔄</view>
  <view class="tip-content">
    <text class="tip-title">重新预订</text>
    <text class="tip-desc">设备和地点已为您选好，请重新选择时间</text>
  </view>
</view>
```

**详细说明**：

#### 重新预订功能设计
1. **显示条件**：
   - **如果**：isReorder为true，那么显示重新预订提示
   - **使用场景**：用户从订单管理页面点击"重新预订"进入此页面

2. **提示图标**：
   - **如果**：需要视觉识别，那么使用🔄图标表示重新预订功能
   - **视觉作用**：明确告知用户这是重新预订操作

3. **提示内容**：
   - **标题**：明确显示"重新预订"
   - **描述**：说明设备和地点已预选，用户只需重新选择时间
   - **用户引导**：减少用户的操作步骤，提升体验

### 3. 设备信息卡片（第12-27行）
```xml
<view class="info-card">
  <text class="card-title">租赁设备</text>
  <view class="equipment-info">
    <image
      class="equipment-image"
      src="{{equipment.image || 'http://iph.href.lu/320x256'}}"
      mode="aspectFill"
    />
    <view class="equipment-details">
      <text class="equipment-name">{{equipment.name}}</text>
      <text class="equipment-desc">{{equipment.description}}</text>
      <text class="equipment-price">¥{{equipment.price}}<text class="price-unit">/小时</text></text>
    </view>
  </view>
</view>
```

**详细说明**：

#### 设备信息展示
1. **卡片标题**：
   - **如果**：需要明确功能区域，那么显示"租赁设备"标题
   - **信息分类**：帮助用户理解当前查看的信息类型

2. **设备图片**：
   - **图片来源**：如果设备有图片，那么显示设备图片；如果没有，那么使用占位图片
   - **占位图片**：使用http://iph.href.lu/320x256生成320x256尺寸的占位图
   - **显示模式**：使用aspectFill模式保持图片比例并填充容器

3. **设备详情**：
   - **设备名称**：显示设备的完整名称
   - **设备描述**：显示设备的详细描述信息
   - **设备价格**：显示每小时的租用价格，格式为"¥XX/小时"

#### 数据绑定逻辑
1. **动态内容**：
   - **如果**：equipment数据已加载，那么显示真实的设备信息
   - **如果**：equipment数据未加载，那么显示空白或loading状态

2. **价格显示**：
   - **主价格**：使用equipment.price显示数字部分
   - **单位说明**：使用独立的text元素显示"/小时"，便于样式控制

### 4. 拍摄地点卡片（第29-47行）
```xml
<view class="info-card">
  <text class="card-title">拍摄地点</text>
  <view class="location-info">
    <image
      class="location-image"
      src="{{location.image || 'http://iph.href.lu/320x256'}}"
      mode="aspectFill"
    />
    <view class="location-details">
      <text class="location-name">{{location.name}}</text>
      <text class="location-desc">{{location.description}}</text>
      <view class="location-meta">
        <text class="location-distance">距离您 {{location.distance}}km</text>
        <text class="location-rating">⭐ {{location.rating}} ({{location.reviewCount}})</text>
      </view>
    </view>
  </view>
</view>
```

**详细说明**：

#### 地点信息展示
1. **卡片标题**：
   - **如果**：需要区分信息类型，那么显示"拍摄地点"标题
   - **功能识别**：帮助用户理解当前查看的是地点相关信息

2. **地点图片**：
   - **图片处理**：与设备图片类似，支持真实图片和占位图片
   - **尺寸一致**：使用相同的占位图片尺寸，保持页面视觉统一

3. **地点详情**：
   - **地点名称**：显示地点的完整名称
   - **地点描述**：显示地点的详细描述信息

4. **地点元数据**：
   - **距离信息**：显示"距离您 X.Xkm"，帮助用户了解地理位置
   - **评分信息**：显示"⭐ X.X (评论数)"，帮助用户了解地点质量

#### 元数据设计
1. **距离显示**：
   - **如果**：有距离数据，那么显示具体距离
   - **用户价值**：帮助用户评估交通成本和时间

2. **评分显示**：
   - **星级图标**：使用⭐图标直观显示评分
   - **评论数量**：显示评论数量，增加可信度
   - **如果**：评分较高，那么用户更容易选择该地点

### 5. 时间选择卡片（第49-82行）
```xml
<view class="info-card">
  <text class="card-title">选择时间</text>

  <!-- 日期选择 -->
  <view class="date-section">
    <text class="form-label">拍摄日期</text>
    <picker
      mode="date"
      value="{{startDate}}"
      bindchange="onDateChange"
      start="{{startDate}}"
    >
      <view class="date-picker">{{startDate || '请选择日期'}}</view>
    </picker>
  </view>

  <!-- 时间段选择 -->
  <view class="time-section">
    <text class="form-label">时间段</text>
    <view class="time-slots">
      <view
        class="time-slot {{selectedTimeSlot === item.value ? 'selected' : ''}} {{!item.available ? 'disabled' : ''}}"
        wx:for="{{timeSlots}}"
        wx:key="value"
        bindtap="selectTimeSlot"
        data-time="{{item.value}}"
        data-available="{{item.available}}"
      >
        <text class="time-value">{{item.label}}</text>
        <text class="time-status">{{item.available ? '可用' : '已预约'}}</text>
      </view>
    </view>
  </view>
</view>
```

**详细说明**：

#### 日期选择器
1. **选择器配置**：
   - **mode="date"**：设置为日期选择模式
   - **value绑定**：显示当前选择的日期
   - **bindchange事件**：日期变化时触发onDateChange方法
   - **start属性**：设置可选择的最早日期

2. **显示逻辑**：
   - **如果**：已选择日期，那么显示具体日期
   - **如果**：未选择日期，那么显示"请选择日期"提示

#### 时间段选择器
1. **循环渲染**：
   - **wx:for="{{timeSlots}}"**：循环渲染所有时间段选项
   - **wx:key="value"**：使用时间值作为唯一标识

2. **样式控制**：
   - **选中状态**：如果时间段被选中，那么添加'selected'样式类
   - **禁用状态**：如果时间段不可用，那么添加'disabled'样式类
   - **动态样式**：根据状态动态应用不同的视觉效果

3. **交互设计**：
   - **点击事件**：bindtap="selectTimeSlot"处理时间段选择
   - **数据传递**：通过data-time和data-available传递时间段信息

4. **状态显示**：
   - **时间值**：显示具体的时间，如"09:00"
   - **可用状态**：如果可用显示"可用"，如果不可用显示"已预约"

### 6. 时长选择区域（第84-100行）
```xml
<view class="duration-section">
  <text class="form-label">租用时长</text>
  <view class="duration-options">
    <view
      class="duration-option {{selectedDuration === item ? 'selected' : ''}}"
      wx:for="{{durationOptions}}"
      wx:key="*this"
      bindtap="selectDuration"
      data-duration="{{item}}"
    >
      <text class="duration-value">{{item}}小时</text>
    </view>
  </view>
</view>
```

**详细说明**：

#### 时长选择设计
1. **选项渲染**：
   - **wx:for="{{durationOptions}}"**：循环渲染所有时长选项
   - **wx:key="*this"**：使用数组项本身作为key（因为是简单数值）

2. **选中状态**：
   - **如果**：当前时长被选中，那么添加'selected'样式类
   - **视觉反馈**：选中的时长会有不同的视觉效果

3. **交互处理**：
   - **点击事件**：bindtap="selectDuration"处理时长选择
   - **数据传递**：通过data-duration传递时长数值

4. **显示格式**：
   - **友好显示**：显示"X小时"而不是单纯的数字
   - **用户理解**：明确告知用户选择的是时长单位

### 7. 费用明细区域（第102-125行）
```xml
<view class="fee-section">
  <text class="section-title">费用明细</text>
  <view class="fee-items">
    <view class="fee-item">
      <text class="fee-label">设备租用费</text>
      <text class="fee-value">¥{{equipment.price * selectedDuration}}</text>
    </view>
    <view class="fee-item">
      <text class="fee-label">服务费</text>
      <text class="fee-value">¥{{serviceFee}}</text>
    </view>
    <view class="fee-item">
      <text class="fee-label">保险费</text>
      <text class="fee-value">¥{{insuranceFee}}</text>
    </view>
    <view class="fee-item total">
      <text class="fee-label">总计</text>
      <text class="fee-value total-amount">¥{{totalFee}}</text>
    </view>
  </view>
</view>
```

**详细说明**：

#### 费用明细设计
1. **明细项目**：
   - **设备租用费**：动态计算，设备单价 × 选择时长
   - **服务费**：固定费用，显示serviceFee的值
   - **保险费**：固定费用，显示insuranceFee的值
   - **总计**：显示计算后的totalFee

2. **动态计算**：
   - **如果**：时长变化，那么设备租用费会自动重新计算
   - **实时更新**：费用明细会根据用户选择实时更新

3. **视觉层次**：
   - **普通费用项**：使用标准的费用项样式
   - **总计项**：使用'total'样式类，突出显示总金额

4. **透明度**：
   - **费用透明**：清楚显示各项费用的具体金额
   - **用户信任**：明细化的费用显示增加用户信任度

### 8. 用户余额显示（第127-135行）
```xml
<view class="balance-section">
  <view class="balance-info">
    <text class="balance-label">账户余额</text>
    <text class="balance-value {{userInfo.balance >= totalFee ? 'sufficient' : 'insufficient'}}">
      ¥{{userInfo.balance}}
    </text>
  </view>
  <text class="balance-status">
    {{userInfo.balance >= totalFee ? '余额充足' : '余额不足，请充值'}}
  </text>
</view>
```

**详细说明**：

#### 余额显示逻辑
1. **余额金额**：
   - **显示当前余额**：显示用户账户的当前余额
   - **动态样式**：根据余额是否充足应用不同样式

2. **余额状态**：
   - **如果**：余额 >= 总费用，那么显示"余额充足"并使用'sufficient'样式
   - **如果**：余额 < 总费用，那么显示"余额不足，请充值"并使用'insufficient'样式

3. **视觉反馈**：
   - **充足状态**：使用绿色或正常颜色显示
   - **不足状态**：使用红色或警告颜色显示

4. **用户引导**：
   - **明确提示**：如果余额不足，明确提示用户需要充值
   - **解决方案**：为用户提供明确的下一步操作指引

### 9. 提交按钮区域（第137-153行）
```xml
<view class="submit-section">
  <button
    class="submit-button {{canSubmit ? 'enabled' : 'disabled'}}"
    bindtap="submitOrder"
    disabled="{{!canSubmit}}"
  >
    {{submitButtonText}}
  </button>

  <view class="submit-tips" wx:if="{{!canSubmit && !submitting}}">
    <text class="tip-text">{{submitButtonText}}</text>
  </view>
</view>
```

**详细说明**：

#### 提交按钮设计
1. **按钮状态**：
   - **如果**：canSubmit为true，那么按钮可用并显示'enabled'样式
   - **如果**：canSubmit为false，那么按钮禁用并显示'disabled'样式

2. **按钮文字**：
   - **动态文字**：显示submitButtonText的内容
   - **状态反映**：文字内容反映当前的提交状态和条件

3. **交互控制**：
   - **点击事件**：bindtap="submitOrder"处理订单提交
   - **禁用属性**：disabled="{{!canSubmit}}"控制按钮是否可点击

#### 提交提示
1. **显示条件**：
   - **如果**：不能提交且不在提交中，那么显示提示信息
   - **用户引导**：告知用户为什么不能提交以及需要做什么

2. **提示内容**：
   - **具体说明**：显示submitButtonText的内容作为提示
   - **操作指引**：帮助用户了解下一步需要完成的操作

## 业务流程分析

### 1. 正常订单确认流程
1. **如果**：用户从地点选择页面进入，那么页面加载设备和地点信息
2. **如果**：基础信息加载完成，那么用户可以选择拍摄日期
3. **如果**：选择日期后，那么系统更新时间段的可用性
4. **如果**：用户选择可用的时间段，那么可以继续选择租用时长
5. **如果**：选择时长后，那么系统计算总费用并检查用户余额
6. **如果**：所有条件满足，那么用户可以提交订单
7. **如果**：订单提交成功，那么跳转到订单详情页面

### 2. 异常处理流程
1. **如果**：用户直接访问页面（缺少参数），那么提示订单信息不完整并引导重新选择
2. **如果**：设备或地点信息加载失败，那么显示错误提示并提供重新选择选项
3. **如果**：用户选择不可用的时间段，那么显示提示并阻止选择
4. **如果**：用户余额不足，那么提示充值并禁用提交按钮
5. **如果**：订单提交失败，那么显示错误信息并允许重试

### 3. 重新预订流程
1. **如果**：用户从订单管理页面点击"重新预订"，那么显示重新预订提示
2. **如果**：设备和地点信息已预填，那么用户只需重新选择时间
3. **如果**：用户完成时间选择，那么按正常流程提交新订单

## 总结

订单确认页面作为整个租用流程的核心转化页面，实现了以下关键功能：

1. **完整的订单信息展示**：如果用户需要确认订单，那么提供设备、地点、时间、费用等全方位信息展示
2. **智能的时间管理系统**：如果用户选择时间，那么系统自动检查可用性并提供合适的选择
3. **透明的费用计算机制**：如果用户关心费用，那么提供详细的费用明细和实时计算
4. **完善的支付验证系统**：如果用户要支付，那么检查余额充足性并提供明确的状态反馈
5. **健壮的错误处理机制**：如果出现异常情况，那么提供友好的错误提示和解决方案
6. **流畅的用户体验设计**：如果用户操作，那么提供即时反馈和明确的操作指引

整个页面的设计严格遵循了"如果...那么..."的条件逻辑，确保在各种用户操作、数据状态和异常情况下都能提供合适的响应和体验，同时保证了订单创建的准确性和业务流程的完整性。
