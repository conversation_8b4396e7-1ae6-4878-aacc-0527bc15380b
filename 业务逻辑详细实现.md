# 逍遥境小程序 - 业务逻辑详细实现

## 🔐 用户认证系统

### 1. 登录流程详细实现

#### 微信登录完整流程
```javascript
// pages/login/login.js
Page({
  data: {
    loading: false,
    loginType: 'wechat' // wechat | phone
  },

  // 微信授权登录
  async handleWechatLogin() {
    try {
      this.setData({ loading: true })
      
      // 步骤1: 获取微信登录凭证
      const loginResult = await this.getWechatLoginCode()
      
      // 步骤2: 获取用户基本信息
      const userProfile = await this.getWechatUserProfile()
      
      // 步骤3: 发送到后端验证并获取用户信息
      const userInfo = await this.authenticateWithBackend({
        code: loginResult.code,
        userInfo: userProfile.userInfo,
        signature: userProfile.signature,
        encryptedData: userProfile.encryptedData,
        iv: userProfile.iv
      })
      
      // 步骤4: 登录成功处理
      this.handleLoginSuccess(userInfo)
      
    } catch (error) {
      this.handleLoginError(error)
    } finally {
      this.setData({ loading: false })
    }
  },

  // 获取微信登录凭证
  getWechatLoginCode() {
    return new Promise((resolve, reject) => {
      wx.login({
        success: resolve,
        fail: reject
      })
    })
  },

  // 获取用户信息
  getWechatUserProfile() {
    return new Promise((resolve, reject) => {
      wx.getUserProfile({
        desc: '用于完善用户资料',
        success: resolve,
        fail: reject
      })
    })
  },

  // 后端认证
  async authenticateWithBackend(authData) {
    return new Promise((resolve, reject) => {
      wx.request({
        url: `${app.globalData.apiBase}/auth/wechat`,
        method: 'POST',
        data: authData,
        success: (res) => {
          if (res.data.success) {
            resolve(res.data.data)
          } else {
            reject(new Error(res.data.message))
          }
        },
        fail: reject
      })
    })
  },

  // 登录成功处理
  handleLoginSuccess(userInfo) {
    // 存储用户信息
    wx.setStorageSync('isLoggedIn', true)
    wx.setStorageSync('userInfo', userInfo)
    wx.setStorageSync('token', userInfo.token)
    wx.setStorageSync('loginTime', Date.now())
    
    // 更新全局状态
    const app = getApp()
    app.globalData.isLoggedIn = true
    app.globalData.userInfo = userInfo
    
    // 跳转到首页
    wx.switchTab({
      url: '/pages/home/<USER>'
    })
    
    // 显示欢迎信息
    wx.showToast({
      title: `欢迎，${userInfo.nickname}`,
      icon: 'success',
      duration: 2000
    })
  },

  // 登录错误处理
  handleLoginError(error) {
    let message = '登录失败，请重试'
    
    switch (error.message) {
      case 'getUserProfile:fail auth deny':
        message = '需要授权才能使用完整功能'
        break
      case 'login:fail':
        message = '网络连接失败，请检查网络'
        break
      case 'INVALID_CODE':
        message = '登录凭证无效，请重试'
        break
      case 'USER_NOT_FOUND':
        message = '用户不存在，请联系客服'
        break
    }
    
    wx.showModal({
      title: '登录失败',
      content: message,
      showCancel: false,
      confirmText: '确定'
    })
  }
})
```

#### 手机号登录流程
```javascript
// 手机号登录相关方法
Page({
  data: {
    phoneNumber: '',
    verifyCode: '',
    countdown: 0,
    canSendCode: true
  },

  // 显示手机号登录模态框
  showPhoneLogin() {
    this.setData({
      showPhoneModal: true,
      phoneNumber: '',
      verifyCode: '',
      countdown: 0,
      canSendCode: true
    })
  },

  // 输入手机号
  onPhoneInput(e) {
    this.setData({
      phoneNumber: e.detail.value
    })
  },

  // 发送验证码
  async sendVerifyCode() {
    const { phoneNumber } = this.data
    
    // 验证手机号格式
    if (!this.validatePhone(phoneNumber)) {
      wx.showToast({
        title: '请输入正确的手机号',
        icon: 'error'
      })
      return
    }
    
    try {
      // 发送验证码请求
      await this.requestVerifyCode(phoneNumber)
      
      // 开始倒计时
      this.startCountdown()
      
      wx.showToast({
        title: '验证码已发送',
        icon: 'success'
      })
      
    } catch (error) {
      wx.showToast({
        title: error.message || '发送失败，请重试',
        icon: 'error'
      })
    }
  },

  // 请求验证码
  requestVerifyCode(phoneNumber) {
    return new Promise((resolve, reject) => {
      wx.request({
        url: `${app.globalData.apiBase}/auth/send-code`,
        method: 'POST',
        data: { phone: phoneNumber },
        success: (res) => {
          if (res.data.success) {
            resolve(res.data)
          } else {
            reject(new Error(res.data.message))
          }
        },
        fail: reject
      })
    })
  },

  // 开始倒计时
  startCountdown() {
    this.setData({
      countdown: 60,
      canSendCode: false
    })
    
    const timer = setInterval(() => {
      const { countdown } = this.data
      if (countdown <= 1) {
        clearInterval(timer)
        this.setData({
          countdown: 0,
          canSendCode: true
        })
      } else {
        this.setData({
          countdown: countdown - 1
        })
      }
    }, 1000)
  },

  // 验证码输入
  onCodeInput(e) {
    this.setData({
      verifyCode: e.detail.value
    })
  },

  // 确认手机号登录
  async confirmPhoneLogin() {
    const { phoneNumber, verifyCode } = this.data
    
    // 验证输入
    if (!this.validatePhone(phoneNumber)) {
      wx.showToast({
        title: '请输入正确的手机号',
        icon: 'error'
      })
      return
    }
    
    if (!this.validateCode(verifyCode)) {
      wx.showToast({
        title: '请输入6位验证码',
        icon: 'error'
      })
      return
    }
    
    try {
      this.setData({ loading: true })
      
      // 验证手机号和验证码
      const userInfo = await this.verifyPhoneLogin({
        phone: phoneNumber,
        code: verifyCode
      })
      
      // 关闭模态框
      this.setData({ showPhoneModal: false })
      
      // 登录成功处理
      this.handleLoginSuccess(userInfo)
      
    } catch (error) {
      this.handleLoginError(error)
    } finally {
      this.setData({ loading: false })
    }
  },

  // 验证手机号登录
  verifyPhoneLogin(data) {
    return new Promise((resolve, reject) => {
      wx.request({
        url: `${app.globalData.apiBase}/auth/phone`,
        method: 'POST',
        data,
        success: (res) => {
          if (res.data.success) {
            resolve(res.data.data)
          } else {
            reject(new Error(res.data.message))
          }
        },
        fail: reject
      })
    })
  },

  // 验证手机号格式
  validatePhone(phone) {
    const phoneRegex = /^1[3-9]\d{9}$/
    return phoneRegex.test(phone)
  },

  // 验证验证码格式
  validateCode(code) {
    const codeRegex = /^\d{6}$/
    return codeRegex.test(code)
  }
})
```

## 🛒 订单管理系统

### 1. 订单创建流程

#### 订单确认页面逻辑
```javascript
// pages/order/order.js
Page({
  data: {
    deviceInfo: null,
    locationInfo: null,
    selectedDate: null,
    selectedTime: null,
    selectedDuration: 1,
    deviceFee: 0,
    serviceFee: 5,
    totalAmount: 0,
    userBalance: 0,
    availableTimes: [],
    loading: false
  },

  onLoad(options) {
    this.initOrderData(options)
  },

  // 初始化订单数据
  async initOrderData(options) {
    try {
      this.setData({ loading: true })
      
      // 获取设备信息
      const deviceInfo = await this.getDeviceInfo(options.deviceId)
      
      // 获取地点信息
      const locationInfo = await this.getLocationInfo(options.locationId)
      
      // 获取用户余额
      const userInfo = wx.getStorageSync('userInfo')
      const userBalance = userInfo.balance || 0
      
      // 获取可用时间段
      const availableTimes = await this.getAvailableTimes(options.deviceId, options.date)
      
      this.setData({
        deviceInfo,
        locationInfo,
        userBalance,
        availableTimes
      })
      
      // 计算初始费用
      this.calculateTotal()
      
    } catch (error) {
      wx.showToast({
        title: '加载失败，请重试',
        icon: 'error'
      })
    } finally {
      this.setData({ loading: false })
    }
  },

  // 选择日期
  selectDate(e) {
    const date = e.currentTarget.dataset.date
    this.setData({ selectedDate: date })
    
    // 重新获取可用时间段
    this.updateAvailableTimes(date)
    this.calculateTotal()
  },

  // 选择时间
  selectTime(e) {
    const time = e.currentTarget.dataset.time
    this.setData({ selectedTime: time })
    this.calculateTotal()
  },

  // 选择时长
  selectDuration(e) {
    const duration = parseInt(e.currentTarget.dataset.duration)
    this.setData({ selectedDuration: duration })
    this.calculateTotal()
  },

  // 计算总费用
  calculateTotal() {
    const { deviceInfo, selectedDuration, serviceFee } = this.data
    
    if (deviceInfo && selectedDuration) {
      const deviceFee = deviceInfo.hourlyRate * selectedDuration
      const totalAmount = deviceFee + serviceFee
      
      this.setData({
        deviceFee,
        totalAmount
      })
    }
  },

  // 提交订单
  async submitOrder() {
    // 验证订单数据
    if (!this.validateOrderData()) {
      return
    }
    
    // 检查余额
    const { totalAmount, userBalance } = this.data
    if (userBalance < totalAmount) {
      this.showInsufficientBalanceModal()
      return
    }
    
    try {
      this.setData({ loading: true })
      
      // 创建订单
      const orderData = this.buildOrderData()
      const order = await this.createOrder(orderData)
      
      // 支付订单
      await this.payOrder(order.id, totalAmount)
      
      // 跳转到操控页面
      wx.redirectTo({
        url: `/pages/control/control?orderId=${order.id}`
      })
      
    } catch (error) {
      this.handleOrderError(error)
    } finally {
      this.setData({ loading: false })
    }
  },

  // 验证订单数据
  validateOrderData() {
    const { deviceInfo, locationInfo, selectedDate, selectedTime, selectedDuration } = this.data
    
    if (!deviceInfo) {
      wx.showToast({ title: '设备信息错误', icon: 'error' })
      return false
    }
    
    if (!locationInfo) {
      wx.showToast({ title: '地点信息错误', icon: 'error' })
      return false
    }
    
    if (!selectedDate) {
      wx.showToast({ title: '请选择日期', icon: 'error' })
      return false
    }
    
    if (!selectedTime) {
      wx.showToast({ title: '请选择时间', icon: 'error' })
      return false
    }
    
    if (!selectedDuration || selectedDuration <= 0) {
      wx.showToast({ title: '请选择租赁时长', icon: 'error' })
      return false
    }
    
    return true
  },

  // 构建订单数据
  buildOrderData() {
    const { deviceInfo, locationInfo, selectedDate, selectedTime, selectedDuration, totalAmount } = this.data
    
    return {
      deviceId: deviceInfo.id,
      deviceName: deviceInfo.name,
      deviceImage: deviceInfo.image,
      locationId: locationInfo.id,
      locationName: locationInfo.name,
      locationAddress: locationInfo.address,
      date: selectedDate,
      time: selectedTime,
      duration: selectedDuration,
      hourlyRate: deviceInfo.hourlyRate,
      serviceFee: this.data.serviceFee,
      totalAmount: totalAmount,
      status: 'pending'
    }
  },

  // 创建订单
  createOrder(orderData) {
    return new Promise((resolve, reject) => {
      wx.request({
        url: `${app.globalData.apiBase}/orders`,
        method: 'POST',
        header: {
          'Authorization': `Bearer ${wx.getStorageSync('token')}`
        },
        data: orderData,
        success: (res) => {
          if (res.data.success) {
            // 同时保存到本地
            const orders = wx.getStorageSync('orders') || []
            orders.unshift(res.data.data)
            wx.setStorageSync('orders', orders)
            
            resolve(res.data.data)
          } else {
            reject(new Error(res.data.message))
          }
        },
        fail: reject
      })
    })
  },

  // 支付订单
  async payOrder(orderId, amount) {
    // 扣除余额
    const userInfo = wx.getStorageSync('userInfo')
    userInfo.balance -= amount
    wx.setStorageSync('userInfo', userInfo)
    
    // 更新订单状态
    await this.updateOrderStatus(orderId, 'active')
    
    // 记录交易
    this.recordTransaction({
      type: 'payment',
      orderId: orderId,
      amount: amount,
      description: '设备租赁费用'
    })
    
    wx.showToast({
      title: '支付成功',
      icon: 'success'
    })
  },

  // 显示余额不足提示
  showInsufficientBalanceModal() {
    wx.showModal({
      title: '余额不足',
      content: '当前余额不足以支付此订单，是否前往充值？',
      confirmText: '去充值',
      cancelText: '取消',
      success: (res) => {
        if (res.confirm) {
          wx.navigateTo({
            url: '/pages/recharge/recharge'
          })
        }
      }
    })
  },

  // 处理订单错误
  handleOrderError(error) {
    let message = '订单创建失败，请重试'
    
    switch (error.message) {
      case 'DEVICE_NOT_AVAILABLE':
        message = '设备暂时不可用，请选择其他时间'
        break
      case 'TIME_CONFLICT':
        message = '所选时间段已被预订，请选择其他时间'
        break
      case 'INSUFFICIENT_BALANCE':
        message = '余额不足，请先充值'
        break
    }
    
    wx.showModal({
      title: '订单失败',
      content: message,
      showCancel: false
    })
  }
})
```

### 2. 订单状态管理

#### 订单状态流转
```javascript
// utils/orderStatus.js
const OrderStatus = {
  PENDING: 'pending',     // 待支付
  ACTIVE: 'active',       // 已支付，可操控
  FLYING: 'flying',       // 飞行中
  COMPLETED: 'completed', // 已完成
  CANCELLED: 'cancelled'  // 已取消
}

const OrderStatusManager = {
  // 更新订单状态
  async updateStatus(orderId, newStatus, extraData = {}) {
    try {
      // 更新本地订单
      const orders = wx.getStorageSync('orders') || []
      const orderIndex = orders.findIndex(order => order.id === orderId)
      
      if (orderIndex !== -1) {
        orders[orderIndex] = {
          ...orders[orderIndex],
          status: newStatus,
          updatedAt: new Date().toISOString(),
          ...extraData
        }
        wx.setStorageSync('orders', orders)
      }
      
      // 同步到后端
      await this.syncToBackend(orderId, newStatus, extraData)
      
      return orders[orderIndex]
      
    } catch (error) {
      console.error('更新订单状态失败:', error)
      throw error
    }
  },

  // 同步到后端
  syncToBackend(orderId, status, extraData) {
    return new Promise((resolve, reject) => {
      wx.request({
        url: `${app.globalData.apiBase}/orders/${orderId}/status`,
        method: 'PUT',
        header: {
          'Authorization': `Bearer ${wx.getStorageSync('token')}`
        },
        data: { status, ...extraData },
        success: (res) => {
          if (res.data.success) {
            resolve(res.data.data)
          } else {
            reject(new Error(res.data.message))
          }
        },
        fail: reject
      })
    })
  },

  // 取消订单
  async cancelOrder(orderId) {
    const order = await this.updateStatus(orderId, OrderStatus.CANCELLED, {
      cancelledAt: new Date().toISOString()
    })
    
    // 如果已支付，退还余额
    if (order.status === OrderStatus.ACTIVE) {
      await this.refundOrder(order)
    }
    
    return order
  },

  // 退款处理
  async refundOrder(order) {
    const userInfo = wx.getStorageSync('userInfo')
    userInfo.balance += order.totalAmount
    wx.setStorageSync('userInfo', userInfo)
    
    // 记录退款交易
    this.recordTransaction({
      type: 'refund',
      orderId: order.id,
      amount: order.totalAmount,
      description: '订单取消退款'
    })
  }
}
```

## 💰 余额管理系统

### 1. 充值流程详细实现

#### 充值页面逻辑
```javascript
// pages/recharge/recharge.js
Page({
  data: {
    selectedAmount: 0,
    customAmount: '',
    selectedPayment: null,
    userBalance: 0,
    loading: false,
    amountOptions: [50, 100, 200, 300, 500, 1000]
  },

  onLoad() {
    this.initRechargeData()
  },

  onShow() {
    // 刷新余额信息
    this.refreshBalance()
  },

  // 初始化充值数据
  initRechargeData() {
    const userInfo = wx.getStorageSync('userInfo')
    this.setData({
      userBalance: userInfo.balance || 0
    })
  },

  // 刷新余额
  refreshBalance() {
    const userInfo = wx.getStorageSync('userInfo')
    this.setData({
      userBalance: userInfo.balance || 0
    })
  },

  // 选择充值金额
  selectAmount(e) {
    const amount = parseInt(e.currentTarget.dataset.amount)
    this.setData({
      selectedAmount: amount,
      customAmount: '' // 清空自定义金额
    })
  },

  // 自定义金额输入
  onCustomAmountInput(e) {
    const amount = parseFloat(e.detail.value) || 0
    this.setData({
      customAmount: e.detail.value,
      selectedAmount: amount
    })
  },

  // 选择支付方式
  selectPayment(e) {
    const payment = e.currentTarget.dataset.payment
    this.setData({
      selectedPayment: payment
    })
  },

  // 确认充值
  async confirmRecharge() {
    // 验证充值数据
    if (!this.validateRechargeData()) {
      return
    }

    const { selectedAmount, selectedPayment } = this.data

    try {
      this.setData({ loading: true })

      // 创建充值订单
      const rechargeOrder = await this.createRechargeOrder(selectedAmount, selectedPayment)

      // 调用支付
      await this.processPayment(rechargeOrder)

    } catch (error) {
      this.handleRechargeError(error)
    } finally {
      this.setData({ loading: false })
    }
  },

  // 验证充值数据
  validateRechargeData() {
    const { selectedAmount, selectedPayment } = this.data

    if (!selectedAmount || selectedAmount <= 0) {
      wx.showToast({
        title: '请选择充值金额',
        icon: 'error'
      })
      return false
    }

    if (selectedAmount < 10) {
      wx.showToast({
        title: '充值金额不能少于10元',
        icon: 'error'
      })
      return false
    }

    if (selectedAmount > 10000) {
      wx.showToast({
        title: '单次充值不能超过10000元',
        icon: 'error'
      })
      return false
    }

    if (!selectedPayment) {
      wx.showToast({
        title: '请选择支付方式',
        icon: 'error'
      })
      return false
    }

    return true
  },

  // 创建充值订单
  createRechargeOrder(amount, paymentMethod) {
    return new Promise((resolve, reject) => {
      wx.request({
        url: `${app.globalData.apiBase}/recharge/create`,
        method: 'POST',
        header: {
          'Authorization': `Bearer ${wx.getStorageSync('token')}`
        },
        data: {
          amount: amount,
          paymentMethod: paymentMethod
        },
        success: (res) => {
          if (res.data.success) {
            resolve(res.data.data)
          } else {
            reject(new Error(res.data.message))
          }
        },
        fail: reject
      })
    })
  },

  // 处理支付
  async processPayment(rechargeOrder) {
    const { selectedPayment } = this.data

    switch (selectedPayment) {
      case 'wechat':
        await this.wechatPay(rechargeOrder)
        break
      case 'alipay':
        await this.alipay(rechargeOrder)
        break
      default:
        throw new Error('不支持的支付方式')
    }
  },

  // 微信支付
  wechatPay(rechargeOrder) {
    return new Promise((resolve, reject) => {
      wx.requestPayment({
        timeStamp: rechargeOrder.timeStamp,
        nonceStr: rechargeOrder.nonceStr,
        package: rechargeOrder.package,
        signType: rechargeOrder.signType,
        paySign: rechargeOrder.paySign,
        success: (res) => {
          this.handlePaymentSuccess(rechargeOrder)
          resolve(res)
        },
        fail: (err) => {
          this.handlePaymentFail(err)
          reject(err)
        }
      })
    })
  },

  // 支付成功处理
  async handlePaymentSuccess(rechargeOrder) {
    try {
      // 更新本地余额
      const userInfo = wx.getStorageSync('userInfo')
      userInfo.balance = (userInfo.balance || 0) + rechargeOrder.amount
      wx.setStorageSync('userInfo', userInfo)

      // 更新页面余额显示
      this.setData({
        userBalance: userInfo.balance,
        selectedAmount: 0,
        customAmount: '',
        selectedPayment: null
      })

      // 记录交易
      this.recordTransaction({
        type: 'recharge',
        amount: rechargeOrder.amount,
        paymentMethod: rechargeOrder.paymentMethod,
        orderId: rechargeOrder.id,
        description: '账户充值'
      })

      // 显示成功提示
      this.showSuccessModal(rechargeOrder.amount)

      // 通知后端支付成功
      await this.notifyPaymentSuccess(rechargeOrder.id)

    } catch (error) {
      console.error('充值成功处理失败:', error)
    }
  },

  // 支付失败处理
  handlePaymentFail(error) {
    let message = '支付失败，请重试'

    if (error.errMsg.includes('cancel')) {
      message = '支付已取消'
    } else if (error.errMsg.includes('fail')) {
      message = '支付失败，请检查网络或余额'
    }

    wx.showToast({
      title: message,
      icon: 'error'
    })
  },

  // 显示成功模态框
  showSuccessModal(amount) {
    wx.showModal({
      title: '充值成功',
      content: `已成功充值 ¥${amount}，当前余额 ¥${this.data.userBalance}`,
      showCancel: false,
      confirmText: '确定',
      success: () => {
        // 可以选择返回上一页或停留在当前页
        // wx.navigateBack()
      }
    })
  },

  // 记录交易
  recordTransaction(transaction) {
    const transactions = wx.getStorageSync('transactions') || []
    transactions.unshift({
      id: 'TXN_' + Date.now(),
      timestamp: new Date().toISOString(),
      ...transaction
    })
    wx.setStorageSync('transactions', transactions)
  },

  // 处理充值错误
  handleRechargeError(error) {
    let message = '充值失败，请重试'

    switch (error.message) {
      case 'AMOUNT_INVALID':
        message = '充值金额无效'
        break
      case 'PAYMENT_METHOD_INVALID':
        message = '支付方式无效'
        break
      case 'USER_NOT_FOUND':
        message = '用户信息错误'
        break
    }

    wx.showModal({
      title: '充值失败',
      content: message,
      showCancel: false
    })
  }
})
```

### 2. 余额消费和退款

#### 余额操作管理器
```javascript
// utils/balanceManager.js
const BalanceManager = {
  // 检查余额是否充足
  checkSufficientBalance(amount) {
    const userInfo = wx.getStorageSync('userInfo')
    const currentBalance = userInfo.balance || 0
    return currentBalance >= amount
  },

  // 扣除余额
  async deductBalance(amount, orderId, description = '消费') {
    if (!this.checkSufficientBalance(amount)) {
      throw new Error('余额不足')
    }

    try {
      // 更新本地余额
      const userInfo = wx.getStorageSync('userInfo')
      userInfo.balance -= amount
      wx.setStorageSync('userInfo', userInfo)

      // 记录交易
      this.recordTransaction({
        type: 'deduct',
        amount: amount,
        orderId: orderId,
        description: description
      })

      // 同步到后端
      await this.syncBalanceToBackend(userInfo.balance)

      return userInfo.balance

    } catch (error) {
      // 如果同步失败，回滚本地余额
      const userInfo = wx.getStorageSync('userInfo')
      userInfo.balance += amount
      wx.setStorageSync('userInfo', userInfo)

      throw error
    }
  },

  // 退还余额
  async refundBalance(amount, orderId, description = '退款') {
    try {
      // 更新本地余额
      const userInfo = wx.getStorageSync('userInfo')
      userInfo.balance = (userInfo.balance || 0) + amount
      wx.setStorageSync('userInfo', userInfo)

      // 记录交易
      this.recordTransaction({
        type: 'refund',
        amount: amount,
        orderId: orderId,
        description: description
      })

      // 同步到后端
      await this.syncBalanceToBackend(userInfo.balance)

      return userInfo.balance

    } catch (error) {
      // 如果同步失败，回滚本地余额
      const userInfo = wx.getStorageSync('userInfo')
      userInfo.balance -= amount
      wx.setStorageSync('userInfo', userInfo)

      throw error
    }
  },

  // 同步余额到后端
  syncBalanceToBackend(balance) {
    return new Promise((resolve, reject) => {
      wx.request({
        url: `${app.globalData.apiBase}/user/balance`,
        method: 'PUT',
        header: {
          'Authorization': `Bearer ${wx.getStorageSync('token')}`
        },
        data: { balance },
        success: (res) => {
          if (res.data.success) {
            resolve(res.data.data)
          } else {
            reject(new Error(res.data.message))
          }
        },
        fail: reject
      })
    })
  },

  // 记录交易
  recordTransaction(transaction) {
    const transactions = wx.getStorageSync('transactions') || []
    transactions.unshift({
      id: 'TXN_' + Date.now(),
      timestamp: new Date().toISOString(),
      ...transaction
    })
    wx.setStorageSync('transactions', transactions)
  },

  // 获取交易记录
  getTransactions(type = 'all') {
    const transactions = wx.getStorageSync('transactions') || []

    if (type === 'all') {
      return transactions
    }

    return transactions.filter(transaction => transaction.type === type)
  }
}
```

## 📸 作品管理系统

### 1. 拍摄作品保存

#### 操控页面拍摄逻辑
```javascript
// pages/control/control.js - 拍摄相关方法
Page({
  data: {
    isRecording: false,
    photoCount: 0,
    videoCount: 0,
    currentOrderId: null
  },

  onLoad(options) {
    this.setData({
      currentOrderId: options.orderId
    })
  },

  // 拍照功能
  async takePhoto() {
    try {
      // 模拟拍照效果
      this.showCameraFlash()

      // 生成照片数据
      const photoData = await this.generatePhotoData()

      // 保存到作品集
      await this.saveToGallery(photoData)

      // 更新计数
      this.setData({
        photoCount: this.data.photoCount + 1
      })

      wx.showToast({
        title: '照片已保存',
        icon: 'success'
      })

    } catch (error) {
      wx.showToast({
        title: '拍照失败',
        icon: 'error'
      })
    }
  },

  // 录像功能
  async toggleRecording() {
    const { isRecording } = this.data

    if (!isRecording) {
      // 开始录像
      await this.startRecording()
    } else {
      // 停止录像
      await this.stopRecording()
    }
  },

  // 开始录像
  async startRecording() {
    try {
      this.setData({ isRecording: true })

      // 记录开始时间
      this.recordingStartTime = Date.now()

      // 更新UI状态
      this.updateRecordingUI(true)

      wx.showToast({
        title: '开始录制',
        icon: 'success'
      })

    } catch (error) {
      this.setData({ isRecording: false })
      wx.showToast({
        title: '录制失败',
        icon: 'error'
      })
    }
  },

  // 停止录像
  async stopRecording() {
    try {
      this.setData({ isRecording: false })

      // 计算录制时长
      const duration = Date.now() - this.recordingStartTime
      const durationStr = this.formatDuration(duration)

      // 生成视频数据
      const videoData = await this.generateVideoData(durationStr)

      // 保存到作品集
      await this.saveToGallery(videoData)

      // 更新计数
      this.setData({
        videoCount: this.data.videoCount + 1
      })

      // 更新UI状态
      this.updateRecordingUI(false)

      wx.showToast({
        title: '视频已保存',
        icon: 'success'
      })

    } catch (error) {
      wx.showToast({
        title: '保存失败',
        icon: 'error'
      })
    }
  },

  // 生成照片数据
  generatePhotoData() {
    const { currentOrderId } = this.data
    const currentOrder = this.getCurrentOrder(currentOrderId)

    return {
      id: 'PHOTO_' + Date.now(),
      type: 'photo',
      url: this.generatePhotoUrl(),
      thumbnail: this.generateThumbnailUrl(),
      timestamp: new Date().toISOString(),
      orderId: currentOrderId,
      locationId: currentOrder?.locationId,
      locationName: currentOrder?.locationName,
      deviceId: currentOrder?.deviceId,
      deviceName: currentOrder?.deviceName,
      fileSize: Math.floor(Math.random() * 5000000) + 1000000, // 1-6MB
      resolution: '4096x3072'
    }
  },

  // 生成视频数据
  generateVideoData(duration) {
    const { currentOrderId } = this.data
    const currentOrder = this.getCurrentOrder(currentOrderId)

    return {
      id: 'VIDEO_' + Date.now(),
      type: 'video',
      url: this.generateVideoUrl(),
      thumbnail: this.generateThumbnailUrl(),
      timestamp: new Date().toISOString(),
      orderId: currentOrderId,
      locationId: currentOrder?.locationId,
      locationName: currentOrder?.locationName,
      deviceId: currentOrder?.deviceId,
      deviceName: currentOrder?.deviceName,
      duration: duration,
      fileSize: Math.floor(Math.random() * 50000000) + 10000000, // 10-60MB
      resolution: '3840x2160',
      frameRate: '30fps'
    }
  },

  // 保存到作品集
  async saveToGallery(mediaData) {
    try {
      // 保存到本地
      const gallery = wx.getStorageSync('userGallery') || []
      gallery.unshift(mediaData)
      wx.setStorageSync('userGallery', gallery)

      // 同步到后端
      await this.syncToBackend(mediaData)

    } catch (error) {
      console.error('保存作品失败:', error)
      throw error
    }
  },

  // 同步到后端
  syncToBackend(mediaData) {
    return new Promise((resolve, reject) => {
      wx.request({
        url: `${app.globalData.apiBase}/gallery`,
        method: 'POST',
        header: {
          'Authorization': `Bearer ${wx.getStorageSync('token')}`
        },
        data: mediaData,
        success: (res) => {
          if (res.data.success) {
            resolve(res.data.data)
          } else {
            reject(new Error(res.data.message))
          }
        },
        fail: reject
      })
    })
  }
})
```

### 2. 作品集管理

#### 作品集页面逻辑
```javascript
// pages/gallery/gallery.js
Page({
  data: {
    mediaList: [],
    filteredList: [],
    currentFilter: 'all',
    loading: false,
    selectedMedia: null,
    showPreview: false,
    stats: {
      total: 0,
      photos: 0,
      videos: 0
    }
  },

  onLoad(options) {
    this.loadGalleryData()
  },

  onShow() {
    // 刷新作品集数据
    this.loadGalleryData()
  },

  // 加载作品集数据
  async loadGalleryData() {
    try {
      this.setData({ loading: true })

      // 从本地加载
      const localGallery = wx.getStorageSync('userGallery') || []

      // 从后端同步
      const remoteGallery = await this.fetchFromBackend()

      // 合并数据（以后端为准）
      const mediaList = this.mergeGalleryData(localGallery, remoteGallery)

      // 更新统计信息
      const stats = this.calculateStats(mediaList)

      this.setData({
        mediaList,
        filteredList: mediaList,
        stats
      })

    } catch (error) {
      console.error('加载作品集失败:', error)
      // 如果网络失败，使用本地数据
      const localGallery = wx.getStorageSync('userGallery') || []
      const stats = this.calculateStats(localGallery)

      this.setData({
        mediaList: localGallery,
        filteredList: localGallery,
        stats
      })
    } finally {
      this.setData({ loading: false })
    }
  },

  // 筛选作品
  filterMedia(e) {
    const filter = e.currentTarget.dataset.filter
    const { mediaList } = this.data

    let filteredList = mediaList

    switch (filter) {
      case 'photo':
        filteredList = mediaList.filter(item => item.type === 'photo')
        break
      case 'video':
        filteredList = mediaList.filter(item => item.type === 'video')
        break
      case 'recent':
        const oneWeekAgo = new Date()
        oneWeekAgo.setDate(oneWeekAgo.getDate() - 7)
        filteredList = mediaList.filter(item => new Date(item.timestamp) > oneWeekAgo)
        break
      default:
        filteredList = mediaList
    }

    this.setData({
      currentFilter: filter,
      filteredList
    })
  },

  // 预览作品
  previewMedia(e) {
    const mediaId = e.currentTarget.dataset.id
    const { mediaList } = this.data
    const selectedMedia = mediaList.find(item => item.id === mediaId)

    if (selectedMedia) {
      this.setData({
        selectedMedia,
        showPreview: true
      })
    }
  },

  // 关闭预览
  closePreview() {
    this.setData({
      showPreview: false,
      selectedMedia: null
    })
  },

  // 分享作品
  async shareMedia() {
    const { selectedMedia } = this.data

    if (!selectedMedia) return

    try {
      // 生成分享信息
      const shareInfo = {
        title: `我在逍遥境拍摄的${selectedMedia.type === 'photo' ? '照片' : '视频'}`,
        path: `/pages/gallery/gallery?mediaId=${selectedMedia.id}`,
        imageUrl: selectedMedia.thumbnail
      }

      // 调用分享
      wx.showShareMenu({
        withShareTicket: true
      })

      wx.showToast({
        title: '分享成功',
        icon: 'success'
      })

    } catch (error) {
      wx.showToast({
        title: '分享失败',
        icon: 'error'
      })
    }
  },

  // 删除作品
  async deleteMedia() {
    const { selectedMedia } = this.data

    if (!selectedMedia) return

    wx.showModal({
      title: '确认删除',
      content: '删除后无法恢复，确定要删除这个作品吗？',
      confirmText: '删除',
      confirmColor: '#ff4444',
      success: async (res) => {
        if (res.confirm) {
          await this.performDelete(selectedMedia.id)
        }
      }
    })
  },

  // 执行删除
  async performDelete(mediaId) {
    try {
      // 从本地删除
      let mediaList = wx.getStorageSync('userGallery') || []
      mediaList = mediaList.filter(item => item.id !== mediaId)
      wx.setStorageSync('userGallery', mediaList)

      // 从后端删除
      await this.deleteFromBackend(mediaId)

      // 更新页面数据
      const stats = this.calculateStats(mediaList)
      const filteredList = this.applyCurrentFilter(mediaList)

      this.setData({
        mediaList,
        filteredList,
        stats,
        showPreview: false,
        selectedMedia: null
      })

      wx.showToast({
        title: '删除成功',
        icon: 'success'
      })

    } catch (error) {
      wx.showToast({
        title: '删除失败',
        icon: 'error'
      })
    }
  },

  // 计算统计信息
  calculateStats(mediaList) {
    const total = mediaList.length
    const photos = mediaList.filter(item => item.type === 'photo').length
    const videos = mediaList.filter(item => item.type === 'video').length

    return { total, photos, videos }
  }
})
```

这份文档详细描述了用户认证、订单管理、余额管理和作品管理的完整业务逻辑实现，包括所有的成功和失败场景处理。
