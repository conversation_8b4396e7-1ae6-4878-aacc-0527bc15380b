// pages/order-confirm/order-confirm.js
const app = getApp()
const auth = require('../../utils/auth.js')
const { mockEquipment, mockLocations } = require('../../utils/mockData.js')
const orderManager = require('../../utils/orderManager.js')

Page({

  /**
   * 页面的初始数据
   */
  data: {
    // 订单信息
    equipment: null,
    location: null,

    // 时间选择
    startDate: '',
    selectedTimeSlot: '',
    selectedDuration: 1,
    timeSlots: [
      { label: '09:00', value: '09:00', available: true },
      { label: '10:30', value: '10:30', available: true },
      { label: '13:00', value: '13:00', available: true },
      { label: '14:00', value: '14:00', available: false },
      { label: '16:00', value: '16:00', available: true },
      { label: '18:00', value: '18:00', available: true }
    ],
    durationOptions: [1, 2, 3, 4],

    // 费用计算
    serviceFee: 10,
    insuranceFee: 5,
    totalFee: 0,

    // 用户信息
    userInfo: { balance: 150 },

    // 页面状态
    loading: false,
    submitting: false,
    canSubmit: false,
    submitButtonText: '请选择时间和时长'
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    console.log('订单确认页面加载', options)

    this.checkAuth()
    this.loadOrderInfo(options)
    this.initDateTime()
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {

  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {

  },

  /**
   * 检查认证状态
   */
  checkAuth() {
    if (!auth.checkLoginStatus()) {
      auth.redirectToLogin('/pages/order-confirm/order-confirm')
      return false
    }

    const userInfo = auth.getCurrentUser()
    this.setData({ userInfo })
    return true
  },

  /**
   * 加载订单信息
   */
  loadOrderInfo(options) {
    const { equipmentId, locationId, equipmentName, locationName, reorder } = options

    // 查找设备信息
    const equipment = mockEquipment.find(item => item.id === equipmentId)

    // 查找位置信息
    let location = null
    if (locationId) {
      // 通过locationId查找
      location = mockLocations.find(item => item.id === locationId)
    } else if (locationName) {
      // 通过locationName查找（重新预订场景）
      location = mockLocations.find(item => item.name === decodeURIComponent(locationName))
    }

    if (!equipment) {
      wx.showToast({
        title: '设备信息错误',
        icon: 'error'
      })
      setTimeout(() => {
        wx.navigateBack()
      }, 1500)
      return
    }

    if (!location) {
      wx.showToast({
        title: '位置信息错误',
        icon: 'error'
      })
      setTimeout(() => {
        wx.navigateBack()
      }, 1500)
      return
    }

    // 设置默认日期为今天
    const today = new Date()
    const startDate = today.toISOString().split('T')[0]

    // 如果是重新预订，确保设备显示为可用状态
    if (reorder) {
      equipment.available = true
      equipment.status = 'available'
    }

    this.setData({
      equipment,
      location,
      startDate,
      isReorder: !!reorder // 标记是否为重新预订
    })
    this.calculateFees()
    this.updateSubmitStatus()

    console.log('订单信息加载完成', equipment.name, location.name, reorder ? '(重新预订)' : '')
  },

  /**
   * 初始化日期时间
   */
  initDateTime() {
    const now = new Date()
    const tomorrow = new Date(now.getTime() + 24 * 60 * 60 * 1000)

    const startDate = tomorrow.toISOString().split('T')[0]
    const startTime = '09:00'

    this.setData({ startDate, startTime })
  },

  /**
   * 计算费用
   */
  calculateFees() {
    const { equipment, selectedDuration, serviceFee, insuranceFee } = this.data

    if (!equipment || !selectedDuration) return

    const equipmentFee = equipment.price * selectedDuration
    const totalFee = equipmentFee + serviceFee + insuranceFee

    this.setData({
      totalFee
    })

    console.log('费用计算完成', { equipmentFee, totalFee })
  },

  /**
   * 时长选择
   */
  onDurationChange(e) {
    const duration = this.data.durationOptions[e.detail.value]
    this.setData({ duration })
    this.calculateFees()
  },

  /**
   * 日期选择
   */
  onDateChange(e) {
    this.setData({ startDate: e.detail.value })
  },

  /**
   * 时间选择
   */
  onTimeChange(e) {
    this.setData({ startTime: e.detail.value })
  },

  /**
   * 检查余额
   */
  checkBalance() {
    const { userInfo, totalFee } = this.data

    if (!userInfo || userInfo.balance < totalFee) {
      wx.showModal({
        title: '余额不足',
        content: `当前余额：¥${userInfo?.balance || 0}\n需要支付：¥${totalFee}\n是否前往充值？`,
        success: (res) => {
          if (res.confirm) {
            wx.navigateTo({
              url: '/pages/recharge/recharge'
            })
          }
        }
      })
      return false
    }

    return true
  },

  /**
   * 提交订单
   */
  async submitOrder() {
    if (!this.checkAuth()) return

    if (!this.checkBalance()) return

    const { equipment, location, startDate, selectedTimeSlot, selectedDuration, totalFee, userInfo } = this.data

    // 验证必填信息
    if (!startDate || !selectedTimeSlot || !selectedDuration) {
      wx.showToast({
        title: '请选择租赁时间和时长',
        icon: 'none'
      })
      return
    }

    this.setData({ submitting: true })

    try {
      // 准备订单数据
      const orderData = {
        equipmentId: equipment.id,
        equipmentName: equipment.name,
        locationId: location.id,
        locationName: location.name,
        startTime: `${startDate} ${selectedTimeSlot}:00`,
        duration: selectedDuration,
        totalAmount: totalFee,
        userId: userInfo.id
      }

      console.log('提交订单', orderData)

      // 调用订单处理逻辑
      const result = await this.processOrder(orderData)

      if (result.success) {
        wx.showToast({
          title: '订单创建成功',
          icon: 'success',
          duration: 2000
        })

        // 跳转到订单详情或控制页面
        setTimeout(() => {
          wx.redirectTo({
            url: `/pages/drone-control/drone-control?orderId=${result.orderId}&equipmentId=${equipment.id}`
          })
        }, 2000)
      } else {
        throw new Error(result.message || '订单创建失败')
      }

    } catch (error) {
      console.error('提交订单失败', error)
      wx.showToast({
        title: error.message || '提交失败，请重试',
        icon: 'error'
      })
    } finally {
      this.setData({ submitting: false })
    }
  },

  /**
   * 选择时间段
   */
  selectTimeSlot(e) {
    const { time, available } = e.currentTarget.dataset
    if (!available) return

    this.setData({
      selectedTimeSlot: time
    })
    this.updateSubmitStatus()
  },

  /**
   * 选择租赁时长
   */
  selectDuration(e) {
    const duration = e.currentTarget.dataset.duration
    this.setData({
      selectedDuration: duration
    })
    this.calculateFees()
    this.updateSubmitStatus()
  },

  /**
   * 更新提交按钮状态
   */
  updateSubmitStatus() {
    const { selectedTimeSlot, selectedDuration, userInfo, totalFee, submitting } = this.data

    let submitButtonText = '请选择时间和时长'
    let canSubmit = false

    if (submitting) {
      submitButtonText = '提交中...'
    } else if (!selectedTimeSlot || !selectedDuration) {
      submitButtonText = '请选择时间和时长'
    } else if (userInfo.balance < totalFee) {
      submitButtonText = '余额不足，请充值'
    } else {
      submitButtonText = '确认下单'
      canSubmit = true
    }

    this.setData({ canSubmit, submitButtonText })
  },

  /**
   * 处理订单 - 扣除余额并创建订单
   */
  async processOrder(orderData) {
    try {
      // 补充订单数据
      const completeOrderData = {
        ...orderData,
        price: this.data.equipment.price,
        equipmentSpecs: this.data.equipment.specs,
        locationAddress: this.data.location.address,
        locationDistance: this.data.location.distance
      }

      // 使用订单管理器创建订单
      const result = await orderManager.createOrder(completeOrderData)

      if (result.success) {
        // 更新页面用户信息显示
        const updatedUser = auth.getCurrentUser()
        this.setData({ userInfo: updatedUser })
      }

      return result

    } catch (error) {
      console.error('处理订单失败：', error)
      return {
        success: false,
        message: error.message || '订单处理失败'
      }
    }
  },



})