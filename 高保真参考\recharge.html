<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>账户充值 - 逍遥境</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body { height: 852px; overflow: hidden; }
        .status-bar {
            background: black;
            color: white;
            height: 47px;
            font-size: 14px;
            font-weight: 600;
        }
        .content-area {
            height: calc(852px - 47px - 80px);
            overflow-y: auto;
        }
        .balance-card {
            background: linear-gradient(135deg, #10b981 0%, #059669 100%);
        }
        .amount-btn {
            transition: all 0.3s ease;
            border: 2px solid transparent;
        }
        .amount-btn.selected {
            background: #1f2937;
            color: white;
            border-color: #1f2937;
            transform: scale(1.05);
        }
        .amount-btn:hover {
            border-color: #1f2937;
        }
        .payment-method {
            transition: all 0.3s ease;
            border: 2px solid #e5e7eb;
        }
        .payment-method.selected {
            border-color: #1f2937;
            background: #f9fafb;
        }
        .confirm-btn {
            background: linear-gradient(135deg, #1f2937 0%, #374151 100%);
            transition: all 0.3s ease;
        }
        .confirm-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(31, 41, 55, 0.3);
        }
        .confirm-btn:disabled {
            background: #d1d5db;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }
        .promo-card {
            background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
        }
    </style>
</head>
<body class="bg-gray-50">
    <!-- iOS 状态栏 -->
    <div class="status-bar flex justify-between items-center px-6">
        <div class="flex items-center space-x-1">
            <span>9:41</span>
        </div>
        <div class="flex items-center space-x-1">
            <i class="fas fa-signal text-xs"></i>
            <i class="fas fa-wifi text-xs"></i>
            <div class="w-6 h-3 border border-white rounded-sm">
                <div class="w-4 h-1 bg-white rounded-sm mt-0.5 ml-0.5"></div>
            </div>
        </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="content-area">
        <!-- 顶部导航 -->
        <div class="bg-white px-6 py-4 shadow-sm">
            <div class="flex items-center space-x-4">
                <button onclick="goBack()">
                    <i class="fas fa-chevron-left text-xl text-gray-600"></i>
                </button>
                <h1 class="text-xl font-semibold text-gray-800">账户充值</h1>
            </div>
        </div>

        <!-- 当前余额卡片 -->
        <div class="balance-card m-4 rounded-2xl p-5 text-white">
            <div class="flex items-center justify-between">
                <div>
                    <h3 class="text-lg font-semibold mb-1 opacity-90">当前余额</h3>
                    <div class="text-3xl font-bold">¥<span id="currentBalance">150.00</span></div>
                    <p class="text-white text-opacity-80 text-sm mt-1">可租赁时长: 约2小时</p>
                </div>
                <div class="text-center">
                    <i class="fas fa-wallet text-4xl mb-2 opacity-80"></i>
                    <div class="text-xs opacity-80">电子钱包</div>
                </div>
            </div>
        </div>

        <!-- 快速充值金额选择 -->
        <div class="bg-white m-4 rounded-2xl p-5 shadow-sm">
            <h3 class="text-lg font-semibold text-gray-800 mb-4">选择充值金额</h3>
            <div class="grid grid-cols-2 gap-3 mb-4">
                <button class="amount-btn bg-gray-50 border border-gray-200 rounded-xl py-4 text-center" onclick="selectAmount(this, 50)">
                    <div class="text-xl font-bold text-gray-800">¥50</div>
                    <div class="text-xs text-gray-500">约0.6小时</div>
                </button>
                <button class="amount-btn bg-gray-50 border border-gray-200 rounded-xl py-4 text-center" onclick="selectAmount(this, 100)">
                    <div class="text-xl font-bold text-gray-800">¥100</div>
                    <div class="text-xs text-gray-500">约1.2小时</div>
                </button>
                <button class="amount-btn bg-gray-50 border border-gray-200 rounded-xl py-4 text-center" onclick="selectAmount(this, 200)">
                    <div class="text-xl font-bold text-gray-800">¥200</div>
                    <div class="text-xs text-gray-500">约2.5小时</div>
                </button>
                <button class="amount-btn bg-gray-50 border border-gray-200 rounded-xl py-4 text-center" onclick="selectAmount(this, 500)">
                    <div class="text-xl font-bold text-gray-800">¥500</div>
                    <div class="text-xs text-gray-500">约6小时</div>
                </button>
            </div>

            <!-- 自定义金额输入 -->
            <div>
                <label class="text-sm font-medium text-gray-600 mb-2 block">自定义金额</label>
                <div class="relative">
                    <span class="absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-500">¥</span>
                    <input type="number" id="customAmount" placeholder="请输入充值金额" 
                           class="w-full border border-gray-300 rounded-xl pl-8 pr-4 py-3 focus:outline-none focus:ring-2 focus:ring-gray-300"
                           onchange="selectCustomAmount(this.value)" min="10" max="5000">
                </div>
                <p class="text-xs text-gray-500 mt-1">最低充值¥10，最高充值¥5000</p>
            </div>
        </div>

        <!-- 支付方式选择 -->
        <div class="bg-white m-4 rounded-2xl p-5 shadow-sm">
            <h3 class="text-lg font-semibold text-gray-800 mb-4">选择支付方式</h3>
            <div class="space-y-3">
                <div class="payment-method rounded-xl p-4 cursor-pointer" onclick="selectPayment(this, 'wechat')">
                    <div class="flex items-center space-x-3">
                        <div class="w-12 h-12 bg-green-100 rounded-xl flex items-center justify-center">
                            <i class="fab fa-weixin text-green-600 text-xl"></i>
                        </div>
                        <div class="flex-1">
                            <h4 class="font-semibold text-gray-800">微信支付</h4>
                            <p class="text-sm text-gray-500">推荐使用，快速便捷</p>
                        </div>
                        <div class="w-6 h-6 border-2 border-gray-300 rounded-full flex items-center justify-center">
                            <div class="w-3 h-3 bg-gray-800 rounded-full hidden payment-indicator"></div>
                        </div>
                    </div>
                </div>

                <div class="payment-method rounded-xl p-4 cursor-pointer" onclick="selectPayment(this, 'alipay')">
                    <div class="flex items-center space-x-3">
                        <div class="w-12 h-12 bg-blue-100 rounded-xl flex items-center justify-center">
                            <i class="fab fa-alipay text-blue-600 text-xl"></i>
                        </div>
                        <div class="flex-1">
                            <h4 class="font-semibold text-gray-800">支付宝</h4>
                            <p class="text-sm text-gray-500">安全可靠，操作简单</p>
                        </div>
                        <div class="w-6 h-6 border-2 border-gray-300 rounded-full flex items-center justify-center">
                            <div class="w-3 h-3 bg-gray-800 rounded-full hidden payment-indicator"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 充值优惠活动 -->
        <div class="promo-card m-4 rounded-2xl p-5 border border-yellow-300">
            <div class="flex items-center space-x-3 mb-3">
                <i class="fas fa-gift text-orange-600 text-xl"></i>
                <h3 class="text-lg font-semibold text-orange-800">充值优惠</h3>
            </div>
            <div class="space-y-2 text-sm text-orange-700">
                <div class="flex items-center space-x-2">
                    <i class="fas fa-check text-orange-600 text-xs"></i>
                    <span>首次充值即送10%额外余额</span>
                </div>
                <div class="flex items-center space-x-2">
                    <i class="fas fa-check text-orange-600 text-xs"></i>
                    <span>充值满¥200送¥30优惠券</span>
                </div>
                <div class="flex items-center space-x-2">
                    <i class="fas fa-check text-orange-600 text-xs"></i>
                    <span>VIP会员充值享9.5折优惠</span>
                </div>
            </div>
        </div>

        <!-- 充值记录入口 -->
        <div class="bg-white m-4 rounded-2xl shadow-sm">
            <div class="flex items-center justify-between p-4 cursor-pointer" onclick="viewRechargeHistory()">
                <div class="flex items-center space-x-3">
                    <div class="w-10 h-10 bg-gray-100 rounded-xl flex items-center justify-center">
                        <i class="fas fa-history text-gray-600"></i>
                    </div>
                    <span class="font-medium text-gray-800">充值记录</span>
                </div>
                <i class="fas fa-chevron-right text-gray-400"></i>
            </div>
        </div>

        <!-- 安全提示 -->
        <div class="bg-blue-50 border border-blue-200 m-4 rounded-2xl p-4">
            <div class="flex items-start space-x-3">
                <i class="fas fa-shield-alt text-blue-600 mt-1"></i>
                <div>
                    <h4 class="font-semibold text-blue-800 mb-1">安全保障</h4>
                    <ul class="text-sm text-blue-700 space-y-1">
                        <li>• 采用银行级SSL加密技术</li>
                        <li>• 支付信息不留存本地</li>
                        <li>• 7×24小时安全监控</li>
                        <li>• 充值失败100%退款保障</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <!-- 底部操作栏 -->
    <div class="fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 px-6 py-4">
        <div class="flex items-center justify-between mb-3">
            <span class="text-gray-600">充值金额</span>
            <span class="text-2xl font-bold text-gray-800">¥<span id="selectedAmount">0</span></span>
        </div>
        <button id="confirmBtn" onclick="confirmRecharge()" 
                class="confirm-btn w-full text-white py-3 rounded-xl font-medium" disabled>
            请选择充值金额和支付方式
        </button>
    </div>

    <script>
        let selectedAmount = 0;
        let selectedPayment = '';
        let currentBalance = 150.00;
        let isFirstRecharge = false; // 模拟首次充值状态

        function goBack() {
            window.history.back();
        }

        function selectAmount(button, amount) {
            // 清除其他选中状态
            document.querySelectorAll('.amount-btn').forEach(btn => {
                btn.classList.remove('selected');
            });
            
            // 选中当前金额
            button.classList.add('selected');
            selectedAmount = amount;
            
            // 清空自定义输入
            document.getElementById('customAmount').value = '';
            
            updateConfirmButton();
            updateSelectedAmount();
        }

        function selectCustomAmount(amount) {
            // 清除快选按钮状态
            document.querySelectorAll('.amount-btn').forEach(btn => {
                btn.classList.remove('selected');
            });
            
            if (amount && amount >= 10 && amount <= 5000) {
                selectedAmount = parseFloat(amount);
                updateConfirmButton();
                updateSelectedAmount();
            } else if (amount) {
                alert('充值金额须在¥10-¥5000之间');
                document.getElementById('customAmount').value = '';
                selectedAmount = 0;
                updateConfirmButton();
                updateSelectedAmount();
            } else {
                selectedAmount = 0;
                updateConfirmButton();
                updateSelectedAmount();
            }
        }

        function selectPayment(element, paymentType) {
            // 清除其他选中状态
            document.querySelectorAll('.payment-method').forEach(method => {
                method.classList.remove('selected');
                method.querySelector('.payment-indicator').classList.add('hidden');
            });
            
            // 选中当前支付方式
            element.classList.add('selected');
            element.querySelector('.payment-indicator').classList.remove('hidden');
            selectedPayment = paymentType;
            
            updateConfirmButton();
        }

        function updateSelectedAmount() {
            document.getElementById('selectedAmount').textContent = selectedAmount.toFixed(2);
        }

        function updateConfirmButton() {
            const confirmBtn = document.getElementById('confirmBtn');
            
            if (selectedAmount > 0 && selectedPayment) {
                confirmBtn.disabled = false;
                confirmBtn.textContent = `确认充值 ¥${selectedAmount.toFixed(2)}`;
                confirmBtn.classList.remove('opacity-50');
            } else {
                confirmBtn.disabled = true;
                confirmBtn.textContent = '请选择充值金额和支付方式';
                confirmBtn.classList.add('opacity-50');
            }
        }

        function confirmRecharge() {
            if (selectedAmount <= 0 || !selectedPayment) {
                alert('请选择充值金额和支付方式');
                return;
            }

            // 计算优惠
            let bonusAmount = 0;
            let discount = 0;
            
            if (isFirstRecharge) {
                bonusAmount = selectedAmount * 0.1; // 首次充值送10%
            }
            
            if (selectedAmount >= 200) {
                // 满200送30优惠券的逻辑在这里可以记录
            }

            const finalAmount = selectedAmount + bonusAmount - discount;
            const paymentName = selectedPayment === 'wechat' ? '微信支付' : '支付宝';

            const confirmInfo = `
充值确认

充值金额：¥${selectedAmount.toFixed(2)}
${bonusAmount > 0 ? `首次充值奖励：¥${bonusAmount.toFixed(2)}` : ''}
${selectedAmount >= 200 ? '额外赠送：¥30优惠券' : ''}
到账金额：¥${finalAmount.toFixed(2)}
支付方式：${paymentName}
充值后余额：¥${(currentBalance + finalAmount).toFixed(2)}

确认充值？
            `;

            if (confirm(confirmInfo.trim())) {
                // 模拟支付流程
                simulatePayment(finalAmount, paymentName);
            }
        }

        function simulatePayment(amount, paymentMethod) {
            // 显示支付处理中
            const confirmBtn = document.getElementById('confirmBtn');
            confirmBtn.textContent = '支付处理中...';
            confirmBtn.disabled = true;

            // 模拟支付延迟
            setTimeout(() => {
                // 支付成功
                currentBalance += amount;
                document.getElementById('currentBalance').textContent = currentBalance.toFixed(2);
                
                alert(`充值成功！\n充值金额：¥${amount.toFixed(2)}\n当前余额：¥${currentBalance.toFixed(2)}\n\n感谢您使用${paymentMethod}!`);
                
                // 重置界面
                resetForm();
            }, 2000);
        }

        function resetForm() {
            selectedAmount = 0;
            selectedPayment = '';
            
            document.querySelectorAll('.amount-btn').forEach(btn => {
                btn.classList.remove('selected');
            });
            
            document.querySelectorAll('.payment-method').forEach(method => {
                method.classList.remove('selected');
                method.querySelector('.payment-indicator').classList.add('hidden');
            });
            
            document.getElementById('customAmount').value = '';
            updateSelectedAmount();
            updateConfirmButton();
        }

        function viewRechargeHistory() {
            alert('充值记录\n\n2024-11-30 14:30  +¥100.00  微信支付\n2024-11-25 09:15  +¥200.00  支付宝\n2024-11-20 16:45  +¥50.00   微信支付\n\n总充值：¥350.00');
        }

        // 初始化
        updateSelectedAmount();
        updateConfirmButton();
    </script>
</body>
</html> 