// app.js
App({
  globalData: {
    userInfo: null,
    isLoggedIn: false,
    systemInfo: null,
    networkType: 'unknown',
    apiBase: 'https://api.xiaoyaojing.com/v1', // 测试环境API
    version: '1.0.0'
  },

  onLaunch(options) {
    console.log('逍遥境小程序启动', options)
    
    // 获取系统信息
    this.getSystemInfo()
    
    // 检查网络状态
    this.checkNetworkStatus()
    
    // 检查登录状态
    this.checkLoginStatus()
    
    // 检查版本更新
    this.checkUpdateManager()
  },

  onShow(options) {
    console.log('逍遥境小程序显示', options)
  },

  onHide() {
    console.log('逍遥境小程序隐藏')
  },

  onError(msg) {
    console.error('小程序发生错误：', msg)
  },

  onPageNotFound(res) {
    console.log('页面不存在：', res)
    // 重定向到首页
    wx.switchTab({
      url: '/pages/home/<USER>'
    })
  },

  // 获取系统信息
  getSystemInfo() {
    try {
      const systemInfo = wx.getSystemInfoSync()
      this.globalData.systemInfo = systemInfo
      console.log('系统信息：', systemInfo)
    } catch (error) {
      console.error('获取系统信息失败：', error)
    }
  },

  // 检查网络状态
  checkNetworkStatus() {
    // 获取当前网络状态
    wx.getNetworkType({
      success: (res) => {
        this.globalData.networkType = res.networkType
        console.log('当前网络状态：', res.networkType)
      }
    })

    // 监听网络状态变化
    wx.onNetworkStatusChange((res) => {
      this.globalData.networkType = res.networkType
      console.log('网络状态变化：', res)
      
      if (!res.isConnected) {
        wx.showToast({
          title: '网络连接已断开',
          icon: 'error',
          duration: 2000
        })
      }
    })
  },

  // 检查登录状态
  checkLoginStatus() {
    try {
      const isLoggedIn = wx.getStorageSync('isLoggedIn')
      const userInfo = wx.getStorageSync('userInfo')
      const token = wx.getStorageSync('token')
      const loginTime = wx.getStorageSync('loginTime')
      
      console.log('登录检查：', { isLoggedIn, userInfo: !!userInfo, token: !!token, loginTime })
      
      // 检查登录状态和token有效性
      if (isLoggedIn && userInfo && token) {
        // 检查token是否过期（7天有效期）
        const tokenAge = Date.now() - (loginTime || 0)
        const tokenExpired = tokenAge > 7 * 24 * 60 * 60 * 1000
        
        if (!tokenExpired) {
          this.globalData.isLoggedIn = true
          this.globalData.userInfo = userInfo
          console.log('用户已登录：', userInfo.nickname)
        } else {
          console.log('Token已过期，清除登录状态')
          this.logout()
        }
      } else {
        console.log('用户未登录')
        this.globalData.isLoggedIn = false
      }
    } catch (error) {
      console.error('检查登录状态失败：', error)
      this.globalData.isLoggedIn = false
    }
  },

  // 检查版本更新
  checkUpdateManager() {
    if (wx.canIUse('getUpdateManager')) {
      const updateManager = wx.getUpdateManager()
      
      updateManager.onCheckForUpdate((res) => {
        console.log('检查更新结果：', res.hasUpdate)
      })
      
      updateManager.onUpdateReady(() => {
        wx.showModal({
          title: '更新提示',
          content: '新版本已经准备好，是否重启应用？',
          success: (res) => {
            if (res.confirm) {
              updateManager.applyUpdate()
            }
          }
        })
      })
      
      updateManager.onUpdateFailed(() => {
        console.error('版本更新失败')
      })
    }
  },

  // 登录成功处理
  loginSuccess(userInfo) {
    this.globalData.isLoggedIn = true
    this.globalData.userInfo = userInfo
    
    // 存储到本地
    wx.setStorageSync('isLoggedIn', true)
    wx.setStorageSync('userInfo', userInfo)
    wx.setStorageSync('token', userInfo.token)
    wx.setStorageSync('loginTime', Date.now())
    
    console.log('登录成功：', userInfo.nickname)
    
    // 发送登录成功事件
    this.triggerEvent('loginSuccess', userInfo)
  },

  // 退出登录
  logout() {
    this.globalData.isLoggedIn = false
    this.globalData.userInfo = null
    
    // 清除本地存储
    try {
      wx.removeStorageSync('isLoggedIn')
      wx.removeStorageSync('userInfo')
      wx.removeStorageSync('token')
      wx.removeStorageSync('loginTime')
    } catch (error) {
      console.error('清除本地存储失败：', error)
    }
    
    console.log('已退出登录')
    
    // 跳转到登录页
    wx.reLaunch({
      url: '/pages/login/login'
    })
  },

  // 更新用户信息
  updateUserInfo(userInfo) {
    this.globalData.userInfo = { ...this.globalData.userInfo, ...userInfo }
    wx.setStorageSync('userInfo', this.globalData.userInfo)
    console.log('用户信息已更新')
  },

  // 路由守卫 - 检查页面访问权限
  checkPageAccess(pageUrl) {
    const publicPages = [
      '/pages/login/login'
    ]
    
    // 公开页面直接允许访问
    if (publicPages.some(page => pageUrl.includes(page))) {
      return true
    }
    
    // 其他页面需要登录
    if (!this.globalData.isLoggedIn) {
      console.log('页面需要登录，重定向到登录页：', pageUrl)
      
      // 保存目标页面，登录后跳转
      wx.setStorageSync('redirectUrl', pageUrl)
      
      // 跳转到登录页
      wx.reLaunch({
        url: '/pages/login/login'
      })
      
      return false
    }
    
    return true
  },

  // 处理登录后重定向
  handleLoginRedirect() {
    const redirectUrl = wx.getStorageSync('redirectUrl')
    
    if (redirectUrl) {
      wx.removeStorageSync('redirectUrl')
      console.log('登录后重定向到：', redirectUrl)
      
      // 根据页面类型选择跳转方式
      if (redirectUrl.includes('/pages/home/') || 
          redirectUrl.includes('/pages/equipment/') || 
          redirectUrl.includes('/pages/orders/') || 
          redirectUrl.includes('/pages/profile/')) {
        wx.switchTab({ url: redirectUrl })
      } else {
        wx.navigateTo({ url: redirectUrl })
      }
    } else {
      // 默认跳转到首页
      wx.switchTab({
        url: '/pages/home/<USER>'
      })
    }
  },

  // 全局事件触发器
  triggerEvent(eventName, data) {
    console.log('全局事件：', eventName, data)
    // 这里可以实现全局事件系统
  },

  // 显示全局错误提示
  showError(message, duration = 2000) {
    wx.showToast({
      title: message,
      icon: 'error',
      duration: duration
    })
  },

  // 显示全局成功提示
  showSuccess(message, duration = 2000) {
    wx.showToast({
      title: message,
      icon: 'success',
      duration: duration
    })
  },

  // 显示全局加载提示
  showLoading(message = '加载中...') {
    wx.showLoading({
      title: message
    })
  },

  // 隐藏全局加载提示
  hideLoading() {
    wx.hideLoading()
  }
})
