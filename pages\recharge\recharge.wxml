<!--pages/recharge/recharge.wxml-->
<view class="recharge-container">
  <!-- 顶部导航 -->
  <view class="nav-header">
    <view class="nav-content">
    
      <text class="nav-title">账户充值</text>
    </view>
  </view>

  <!-- 当前余额卡片 -->
  <view class="balance-card">
    <view class="balance-content">
      <view class="balance-info">
        <text class="balance-title">当前余额</text>
        <text class="balance-amount">¥{{userBalance || '150.00'}}</text>
        <text class="balance-desc">可租赁时长: 约{{availableHours || '2'}}小时</text>
      </view>
    </view>
  </view>

  <!-- 快速充值金额选择 -->
  <view class="amount-section">
    <text class="section-title">选择充值金额</text>
    <view class="amount-grid">
      <button
        class="amount-btn {{selectedAmount === item.value ? 'selected' : ''}}"
        wx:for="{{amountOptions}}"
        wx:key="value"
        bindtap="selectAmount"
        data-amount="{{item.value}}"
      >
        <text class="amount-value">¥{{item.value}}</text>
        <text class="amount-desc">约{{item.hours}}小时</text>
      </button>
    </view>

    <!-- 自定义金额输入 -->
    <view class="custom-amount">
      <text class="custom-label">自定义金额</text>
      <view class="custom-input-wrapper">
        <text class="currency-symbol">¥</text>
        <input
          class="custom-input"
          type="number"
          placeholder="请输入充值金额"
          value="{{customAmount}}"
          bindinput="onCustomAmountInput"
        />
      </view>
      <text class="amount-tip">最低充值¥10，最高充值¥5000</text>
    </view>
  </view>

  <!-- 支付方式选择 -->
  <view class="payment-section">
    <text class="section-title">选择支付方式</text>
    <view class="payment-list">
      <view
        class="payment-method {{selectedPayment === item.type ? 'selected' : ''}}"
        wx:for="{{paymentMethods}}"
        wx:key="type"
        bindtap="selectPayment"
        data-type="{{item.type}}"
      >
        <view class="payment-left">
          <view class="payment-icon-wrapper {{item.colorClass}}">
            <text class="payment-icon">{{item.icon}}</text>
          </view>
          <view class="payment-info">
            <text class="payment-name">{{item.name}}</text>
            <text class="payment-desc">{{item.desc}}</text>
          </view>
        </view>
        <view class="payment-radio">
          <view class="radio-outer">
            <view class="radio-inner {{selectedPayment === item.type ? 'selected' : ''}}"></view>
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 充值优惠活动 -->
  <view class="promo-section">
    <view class="promo-header">
      <text class="promo-icon">🎁</text>
      <text class="promo-title">充值优惠</text>
    </view>
    <view class="promo-list">
      <view class="promo-item">
        <text class="promo-check">✓</text>
        <text class="promo-text">首次充值即送10%额外余额</text>
      </view>
      <view class="promo-item">
        <text class="promo-check">✓</text>
        <text class="promo-text">充值满¥200送¥30优惠券</text>
      </view>
      <view class="promo-item">
        <text class="promo-check">✓</text>
        <text class="promo-text">VIP会员充值享9.5折优惠</text>
      </view>
    </view>
  </view>

  <!-- 充值记录入口 -->
  <!-- <view class="history-section">
    <view class="history-item" bindtap="viewRechargeHistory">
      <view class="history-left">
        <view class="history-icon-wrapper">
          <text class="history-icon">🕒</text>
        </view>
        <text class="history-text">充值记录</text>
      </view>
      <text class="history-arrow">›</text>
    </view>
  </view> -->

  <!-- 安全提示 -->
  <!-- <view class="security-section">
    <view class="security-header">
      <text class="security-icon">🛡️</text>
      <text class="security-title">安全保障</text>
    </view>
    <view class="security-list">
      <text class="security-item">• 采用银行级SSL加密技术</text>
      <text class="security-item">• 支付信息不留存本地</text>
      <text class="security-item">• 7×24小时安全监控</text>
      <text class="security-item">• 充值失败100%退款保障</text>
    </view>
  </view> -->
</view>

<!-- 底部操作栏 -->
<view class="bottom-bar">
  <view class="bottom-info">
    <text class="bottom-label">充值金额</text>
    <text class="bottom-amount">¥{{selectedAmount || '0'}}</text>
  </view>
  <button
    class="confirm-btn {{canRecharge ? '' : 'disabled'}}"
    bindtap="confirmRecharge"
    disabled="{{!canRecharge}}"
  >
    {{confirmBtnText}}
  </button>
</view>